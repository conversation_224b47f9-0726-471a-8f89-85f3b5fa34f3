[workspace]
members = [
    "backend",
    "client", 
    "shared"
]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["VPN Team"]
license = "MIT"

[workspace.dependencies]
# Common dependencies across crates
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Backend specific
axum = "0.7"
tower = { version = "0.4", features = ["util"] }
tower-http = { version = "0.5", features = ["cors", "fs", "trace"] }
sea-orm = { version = "1.1", features = ["sqlx-postgres", "runtime-tokio-rustls", "macros"] }
sea-orm-migration = "1.1"
jsonwebtoken = "9.0"
bcrypt = "0.15"
reqwest = { version = "0.11", features = ["json"] }
dotenv = "0.15"
async-trait = "0.1"
rust_decimal = { version = "1.0", features = ["serde"] }
sha2 = "0.10"
rand = "0.8"
hex = "0.4"
validator = { version = "0.16", features = ["derive"] }

# Client specific  
tauri = { version = "2.0" }
tauri-build = "2.0"

# SQLite dependencies - unified versions to avoid conflicts
libsqlite3-sys = "0.30.1"
rusqlite = "0.32.0"

# Leaf proxy engine (to be added later from ../leaf)
# leaf = { path = "../leaf", default-features = false, features = ["config-conf"] }