# VPN 项目数据库模型和后端 API 设计文档

## 1. 数据库模型设计

### 1.1 核心表结构

#### 1.1.1 用户表 (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_admin BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    -- 用户配置
    max_concurrent_devices INTEGER DEFAULT 3,
    -- 用户状态和限制
    is_banned BOOLEAN DEFAULT false,
    ban_reason TEXT,
    banned_until TIMESTAMP WITH TIME ZONE,
    -- 统计信息
    total_traffic_used BIGINT DEFAULT 0, -- 总流量使用量(字节)
    last_traffic_reset TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### 1.1.2 套餐表 (plans)
```sql
CREATE TABLE plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    -- 价格和周期
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    duration_days INTEGER NOT NULL, -- 0 表示永久套餐
    -- 流量限制
    traffic_limit_gb DECIMAL(10,3), -- NULL 表示无限流量
    -- 连接限制
    max_concurrent_devices INTEGER DEFAULT 1,
    max_concurrent_connections INTEGER DEFAULT 10,
    -- 速度限制 (Mbps)
    speed_limit_upload INTEGER, -- NULL 表示无限制
    speed_limit_download INTEGER, -- NULL 表示无限制
    -- 功能权限
    priority_level INTEGER DEFAULT 1, -- 1-10, 数字越大优先级越高
    -- 配置模板
    config_template TEXT NOT NULL, -- leaf 配置模板，使用占位符
    -- 状态
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_plans_is_active ON plans(is_active);
CREATE INDEX idx_plans_sort_order ON plans(sort_order);
CREATE INDEX idx_plans_price ON plans(price);
```

#### 1.1.3 订阅表 (subscriptions)
```sql
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    plan_id UUID NOT NULL REFERENCES plans(id) ON DELETE RESTRICT,
    -- 订阅周期
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    -- 流量管理
    total_traffic_gb DECIMAL(10,3) NOT NULL, -- 总流量配额
    used_traffic_gb DECIMAL(10,3) DEFAULT 0, -- 已使用流量
    -- 连接管理
    max_concurrent_devices INTEGER NOT NULL,
    current_devices_count INTEGER DEFAULT 0,
    -- 状态
    status subscription_status DEFAULT 'active',
    -- 自动续费
    auto_renew BOOLEAN DEFAULT false,
    auto_renew_plan_id UUID REFERENCES plans(id),
    -- 代理服务器凭证 (由用户管理API服务器生成)
    proxy_username VARCHAR(100), -- 代理服务器用户名
    proxy_password VARCHAR(100), -- 代理服务器密码
    proxy_port INTEGER, -- 分配的端口
    proxy_config_hash VARCHAR(64), -- 配置哈希，用于检测变更
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_traffic_sync TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- 统计信息
    total_connections_count BIGINT DEFAULT 0,
    total_session_time BIGINT DEFAULT 0 -- 总连接时间(秒)
);

-- 枚举类型
CREATE TYPE subscription_status AS ENUM ('active', 'expired', 'suspended', 'cancelled');

-- 索引
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_plan_id ON subscriptions(plan_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_end_date ON subscriptions(end_date);
CREATE UNIQUE INDEX idx_subscriptions_user_active ON subscriptions(user_id) 
    WHERE status = 'active'; -- 确保每个用户只有一个活跃订阅
```

#### 1.1.4 订单表 (orders)
```sql
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number VARCHAR(32) UNIQUE NOT NULL, -- 格式: ORD-YYYYMMDD-XXXXXX
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    plan_id UUID NOT NULL REFERENCES plans(id) ON DELETE RESTRICT,
    -- 订单信息
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    quantity INTEGER DEFAULT 1, -- 购买月数/年数
    -- 支付信息
    payment_method payment_method_type,
    payment_reference VARCHAR(255), -- 支付平台的交易ID
    payment_proof_url VARCHAR(500), -- 支付凭证图片URL
    -- 状态
    status order_status DEFAULT 'pending_payment',
    -- 处理信息
    processed_by UUID REFERENCES users(id), -- 处理订单的管理员
    processed_at TIMESTAMP WITH TIME ZONE,
    notes TEXT, -- 管理员备注
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '24 hours' -- 订单过期时间
);

-- 枚举类型
CREATE TYPE order_status AS ENUM ('pending_payment', 'paid', 'processing', 'completed', 'cancelled', 'refunded');
CREATE TYPE payment_method_type AS ENUM ('manual', 'alipay', 'wechat', 'paypal', 'crypto');

-- 索引
CREATE INDEX idx_orders_order_number ON orders(order_number);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_expires_at ON orders(expires_at);
```

#### 1.1.5 公告表 (announcements)
```sql
CREATE TABLE announcements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    -- 目标用户
    target_audience announcement_audience DEFAULT 'all',
    target_plan_ids JSON, -- 特定套餐用户 ["plan_id1", "plan_id2"]
    -- 显示设置
    announcement_type announcement_type DEFAULT 'info',
    is_popup BOOLEAN DEFAULT false, -- 是否弹窗显示
    is_pinned BOOLEAN DEFAULT false, -- 是否置顶
    -- 状态
    is_active BOOLEAN DEFAULT true,
    -- 时间设置
    publish_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id)
);

-- 枚举类型
CREATE TYPE announcement_audience AS ENUM ('all', 'active_users', 'expired_users', 'specific_plans');
CREATE TYPE announcement_type AS ENUM ('info', 'warning', 'maintenance', 'promotion', 'urgent');

-- 索引
CREATE INDEX idx_announcements_is_active ON announcements(is_active);
CREATE INDEX idx_announcements_publish_at ON announcements(publish_at);
CREATE INDEX idx_announcements_target_audience ON announcements(target_audience);
```

#### 1.1.6 用户连接日志表 (connection_logs)
```sql
CREATE TABLE connection_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subscription_id UUID NOT NULL REFERENCES subscriptions(id) ON DELETE CASCADE,
    -- 连接信息
    client_ip INET,
    client_port INTEGER,
    user_agent TEXT,
    device_info JSON, -- 设备信息
    -- 会话信息
    session_id VARCHAR(64), -- 会话标识
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    disconnected_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER, -- 连接持续时间
    -- 流量统计
    bytes_uploaded BIGINT DEFAULT 0,
    bytes_downloaded BIGINT DEFAULT 0,
    -- 断开原因
    disconnect_reason VARCHAR(50), -- normal, timeout, error, banned, limit_exceeded
    -- 地理位置 (可选)
    country_code VARCHAR(2),
    city VARCHAR(50)
);

-- 索引 (重要：用于性能优化)
CREATE INDEX idx_connection_logs_user_id ON connection_logs(user_id);
CREATE INDEX idx_connection_logs_connected_at ON connection_logs(connected_at);
CREATE INDEX idx_connection_logs_session_id ON connection_logs(session_id);
-- 分区索引 (建议按月分区)
CREATE INDEX idx_connection_logs_connected_at_month ON connection_logs(date_trunc('month', connected_at));
```

#### 1.1.7 系统配置表 (system_settings)
```sql
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    value_type setting_value_type DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT false, -- 是否对客户端公开
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 枚举类型
CREATE TYPE setting_value_type AS ENUM ('string', 'integer', 'decimal', 'boolean', 'json');

-- 预设配置项
INSERT INTO system_settings (key, value, value_type, description, is_public) VALUES
('site_name', 'VPN Service', 'string', '站点名称', true),
('cleanup_inactive_users_days', '30', 'integer', '清理未购买套餐用户的天数', false),
('renewal_reminder_days', '7', 'integer', '续费提醒提前天数', false),
('max_login_attempts', '5', 'integer', '最大登录尝试次数', false),
('login_lockout_minutes', '30', 'integer', '登录锁定时间(分钟)', false),
('renewal_url_template', 'https://your-domain.com/user/subscribe?user_id={{user_id}}&token={{token}}', 'string', '续费链接模板', false),
('support_contact', '<EMAIL>', 'string', '客服联系方式', true),
('maintenance_mode', 'false', 'boolean', '维护模式', true),
('maintenance_message', '', 'string', '维护提示信息', true);
```

#### 1.1.8 API 令牌表 (api_tokens)
```sql
CREATE TABLE api_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL UNIQUE, -- SHA-256 哈希
    name VARCHAR(100) NOT NULL, -- 令牌名称
    permissions JSON, -- 权限列表 ["read:profile", "write:orders"]
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    last_used_ip INET,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_api_tokens_token_hash ON api_tokens(token_hash);
CREATE INDEX idx_api_tokens_user_id ON api_tokens(user_id);
CREATE INDEX idx_api_tokens_expires_at ON api_tokens(expires_at);
```

### 1.2 数据库约束和触发器

#### 1.2.1 自动更新时间戳触发器
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为所有需要的表添加触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_plans_updated_at BEFORE UPDATE ON plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_announcements_updated_at BEFORE UPDATE ON announcements
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();


CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 1.2.2 订单号生成触发器
```sql
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL THEN
        NEW.order_number := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || 
                           LPAD(EXTRACT(EPOCH FROM NOW())::TEXT, 6, '0');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER generate_order_number_trigger BEFORE INSERT ON orders
    FOR EACH ROW EXECUTE FUNCTION generate_order_number();
```

## 2. 后端 API 设计

### 2.1 API 架构概览

- **基础框架**: Rust + Axum
- **数据库 ORM**: SeaORM
- **认证**: JWT Bearer Token
- **API 版本**: v1
- **响应格式**: JSON
- **错误处理**: 统一错误响应格式

### 2.2 通用响应格式

#### 2.2.1 成功响应
```json
{
  "success": true,
  "data": {}, // 具体数据
  "message": "操作成功", // 可选
  "pagination": { // 分页响应时包含
    "page": 1,
    "limit": 20,
    "total": 100,
    "total_pages": 5
  }
}
```

#### 2.2.2 错误响应
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入数据验证失败",
    "details": {
      "username": ["用户名已存在"],
      "email": ["邮箱格式不正确"]
    }
  }
}
```

### 2.3 认证和授权 API

#### 2.3.1 用户注册
```
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123",
  "email": "<EMAIL>" // 可选
}

响应：
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "testuser",
      "email": "<EMAIL>",
      "is_active": true,
      "created_at": "2025-07-08T10:00:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

#### 2.3.2 用户登录
```
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}

响应：
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "testuser",
      "email": "<EMAIL>",
      "is_active": true,
      "last_login_at": "2025-07-08T10:00:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2025-07-15T10:00:00Z"
  }
}
```

#### 2.3.3 刷新令牌
```
POST /api/v1/auth/refresh
Authorization: Bearer <token>

响应：
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2025-07-15T10:00:00Z"
  }
}
```

#### 2.3.4 注销
```
POST /api/v1/auth/logout
Authorization: Bearer <token>

响应：
{
  "success": true,
  "message": "注销成功"
}
```

### 2.4 用户管理 API

#### 2.4.1 获取用户信息
```
GET /api/v1/user/profile
Authorization: Bearer <token>

响应：
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "testuser",
      "email": "<EMAIL>",
      "is_active": true,
      "max_concurrent_devices": 3,
      "total_traffic_used": 1073741824,
      "created_at": "2025-07-01T10:00:00Z",
      "last_login_at": "2025-07-08T10:00:00Z"
    },
    "subscription": {
      "id": "660e8400-e29b-41d4-a716-446655440001",
      "plan": {
        "id": "770e8400-e29b-41d4-a716-446655440002",
        "name": "高级套餐",
        "traffic_limit_gb": 100.0
      },
      "status": "active",
      "start_date": "2025-07-01T00:00:00Z",
      "end_date": "2025-08-01T00:00:00Z",
      "total_traffic_gb": 100.0,
      "used_traffic_gb": 15.5,
      "remaining_days": 23
    }
  }
}
```

#### 2.4.2 更新用户信息
```
PUT /api/v1/user/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>"
}

响应：
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "testuser",
      "email": "<EMAIL>",
      // ... 其他字段
    }
  }
}
```

#### 2.4.3 修改密码
```
PUT /api/v1/user/password
Authorization: Bearer <token>
Content-Type: application/json

{
  "current_password": "oldpassword123",
  "new_password": "newpassword123"
}

响应：
{
  "success": true,
  "message": "密码修改成功"
}
```

#### 2.4.4 获取代理配置
```
GET /api/v1/user/config
Authorization: Bearer <token>

响应：
{
  "success": true,
  "data": {
    "config": "# Leaf 配置文件内容\n[General]\nlogLevel = info\n...",
    "subscription_info": {
      "remaining_traffic_gb": 84.5,
      "remaining_days": 23,
      "max_devices": 3,
      "current_devices": 1
    }
  }
}
```

### 2.5 套餐管理 API

#### 2.5.1 获取所有套餐
```
GET /api/pub/plans/

响应：
{
  "success": true,
  "data": {
    "plans": [
      {
        "id": "770e8400-e29b-41d4-a716-446655440002",
        "name": "基础套餐",
        "description": "适合轻度使用的用户",
        "price": 9.99,
        "currency": "USD",
        "duration_days": 30,
        "duration_display": "1个月",
        "traffic_limit_gb": 50.0,
        "traffic_limit_display": "50GB",
        "max_concurrent_devices": 1,
        "max_concurrent_connections": 10,
        "speed_limit_upload": 10,
        "speed_limit_download": 50,
        "speed_limit_display": "上传10Mbps/下载50Mbps",
        "priority_level": 1,
        "is_active": true,
        "is_featured": false,
        "sort_order": 1,
        "features": [
          "1设备同时连接",
          "10并发连接",
          "流量: 50GB",
          "速度: 上传10Mbps/下载50Mbps",
          "有效期: 1个月"
        ],
        "created_at": "2025-07-08T10:00:00Z",
        "updated_at": "2025-07-08T10:00:00Z"
      },
      {
        "id": "770e8400-e29b-41d4-a716-446655440003",
        "name": "高级套餐",
        "description": "适合重度使用的用户",
        "price": 19.99,
        "currency": "USD",
        "duration_days": 30,
        "duration_display": "1个月",
        "traffic_limit_gb": 200.0,
        "traffic_limit_display": "200GB",
        "max_concurrent_devices": 3,
        "max_concurrent_connections": 10,
        "speed_limit_upload": null,
        "speed_limit_download": null,
        "speed_limit_display": "无限制",
        "priority_level": 5,
        "is_active": true,
        "is_featured": true,
        "sort_order": 2,
        "features": [
          "3设备同时连接",
          "10并发连接",
          "流量: 200GB",
          "速度: 无限制",
          "有效期: 1个月",
          "高优先级",
          "推荐套餐"
        ],
        "created_at": "2025-07-08T10:00:00Z",
        "updated_at": "2025-07-08T10:00:00Z"
      }
    ],
    "total_count": 2,
    "featured_plans": [
      {
        "id": "770e8400-e29b-41d4-a716-446655440003",
        "name": "高级套餐",
        // ... 完整套餐信息
      }
    ],
    "currencies": ["USD"]
  }
}
```

#### 2.5.2 获取套餐详情
```
GET /api/plans/{plan_id}

响应：
{
  "success": true,
  "data": {
    "id": "770e8400-e29b-41d4-a716-446655440002",
    "name": "高级套餐",
    "description": "适合重度使用的用户",
    "price": 19.99,
    "currency": "USD",
    "duration_days": 30,
    "duration_display": "1个月",
    "traffic_limit_gb": 200.0,
    "traffic_limit_display": "200GB",
    "max_concurrent_devices": 3,
    "max_concurrent_connections": 10,
    "speed_limit_upload": null,
    "speed_limit_download": null,
    "speed_limit_display": "无限制",
    "priority_level": 5,
    "is_active": true,
    "is_featured": true,
    "sort_order": 2,
    "features": [
      "3设备同时连接",
      "10并发连接",
      "流量: 200GB",
      "速度: 无限制",
      "有效期: 1个月",
      "高优先级",
      "推荐套餐"
    ],
    "created_at": "2025-07-08T10:00:00Z",
    "updated_at": "2025-07-08T10:00:00Z"
  }
}
```

### 2.6 订单管理 API

#### 2.6.1 创建订单
```
POST /api/v1/orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "plan_id": "770e8400-e29b-41d4-a716-446655440002",
  "quantity": 1, // 购买月数
  "payment_method": "manual"
}

响应：
{
  "success": true,
  "data": {
    "order": {
      "id": "880e8400-e29b-41d4-a716-446655440004",
      "order_number": "ORD-20250708-123456",
      "plan": {
        "id": "770e8400-e29b-41d4-a716-446655440002",
        "name": "高级套餐",
        "price": 19.99
      },
      "amount": 19.99,
      "currency": "USD",
      "quantity": 1,
      "status": "pending_payment",
      "payment_method": "manual",
      "created_at": "2025-07-08T10:00:00Z",
      "expires_at": "2025-07-09T10:00:00Z"
    },
    "payment_instructions": {
      "method": "manual",
      "details": "请联系客服并提供订单号完成支付",
      "contact": "<EMAIL>"
    }
  }
}
```

#### 2.6.2 获取订单列表
```
GET /api/v1/orders?page=1&limit=10&status=all
Authorization: Bearer <token>

响应：
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "880e8400-e29b-41d4-a716-446655440004",
        "order_number": "ORD-20250708-123456",
        "plan_name": "高级套餐",
        "amount": 19.99,
        "currency": "USD",
        "status": "completed",
        "payment_method": "manual",
        "created_at": "2025-07-08T10:00:00Z",
        "processed_at": "2025-07-08T11:30:00Z"
      }
    ]
  },
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 5,
    "total_pages": 1
  }
}
```

#### 2.6.3 获取订单详情
```
GET /api/v1/orders/{order_id}
Authorization: Bearer <token>

响应：
{
  "success": true,
  "data": {
    "order": {
      "id": "880e8400-e29b-41d4-a716-446655440004",
      "order_number": "ORD-20250708-123456",
      "plan": {
        "id": "770e8400-e29b-41d4-a716-446655440002",
        "name": "高级套餐",
        "description": "适合重度使用的用户"
      },
      "amount": 19.99,
      "currency": "USD",
      "quantity": 1,
      "status": "completed",
      "payment_method": "manual",
      "payment_reference": "PAY-123456789",
      "created_at": "2025-07-08T10:00:00Z",
      "processed_at": "2025-07-08T11:30:00Z",
      "notes": "管理员手动确认支付"
    }
  }
}
```

### 2.7 公告 API

#### 2.7.1 获取公告列表
```
GET /api/v1/announcements?page=1&limit=10
Authorization: Bearer <token>

响应：
{
  "success": true,
  "data": {
    "announcements": [
      {
        "id": "990e8400-e29b-41d4-a716-446655440005",
        "title": "系统维护通知",
        "content": "系统将于明日凌晨2点进行维护，预计持续2小时。",
        "type": "maintenance",
        "is_popup": true,
        "is_pinned": true,
        "publish_at": "2025-07-08T10:00:00Z",
        "expires_at": "2025-07-10T10:00:00Z"
      }
    ]
  },
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 3,
    "total_pages": 1
  }
}
```

#### 2.7.2 获取公告详情
```
GET /api/v1/announcements/{announcement_id}
Authorization: Bearer <token>

响应：
{
  "success": true,
  "data": {
    "announcement": {
      "id": "990e8400-e29b-41d4-a716-446655440005",
      "title": "系统维护通知",
      "content": "# 系统维护通知\n\n系统将于明日凌晨2点进行维护...",
      "type": "maintenance",
      "is_popup": true,
      "is_pinned": true,
      "publish_at": "2025-07-08T10:00:00Z",
      "expires_at": "2025-07-10T10:00:00Z"
    }
  }
}
```


### 2.8 管理员 API

#### 2.8.1 用户管理

##### 获取用户列表
```
GET /api/v1/admin/users?page=1&limit=20&search=username&status=all
Authorization: Bearer <admin_token>

响应：
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "username": "testuser",
        "email": "<EMAIL>",
        "is_active": true,
        "is_admin": false,
        "is_banned": false,
        "total_traffic_used": 1073741824,
        "created_at": "2025-07-01T10:00:00Z",
        "last_login_at": "2025-07-08T10:00:00Z",
        "current_subscription": {
          "plan_name": "高级套餐",
          "status": "active",
          "end_date": "2025-08-01T00:00:00Z"
        }
      }
    ]
  },
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "total_pages": 8
  }
}
```

##### 创建用户
```
POST /api/v1/admin/users
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "temppassword123",
  "is_admin": false,
  "max_concurrent_devices": 3
}

响应：
{
  "success": true,
  "data": {
    "user": {
      "id": "aa0e8400-e29b-41d4-a716-446655440006",
      "username": "newuser",
      "email": "<EMAIL>",
      "is_active": true,
      "is_admin": false,
      "max_concurrent_devices": 3,
      "created_at": "2025-07-08T10:00:00Z"
    }
  }
}
```

##### 更新用户
```
PUT /api/v1/admin/users/{user_id}
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "is_active": true,
  "is_banned": false,
  "max_concurrent_devices": 5,
  "notes": "VIP 用户"
}

响应：
{
  "success": true,
  "data": {
    "user": {
      // 更新后的用户信息
    }
  }
}
```

##### 删除用户
```
DELETE /api/v1/admin/users/{user_id}
Authorization: Bearer <admin_token>

响应：
{
  "success": true,
  "message": "用户已删除"
}
```

#### 2.8.2 套餐管理

##### 创建套餐
```
POST /api/v1/admin/plans
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "name": "企业套餐",
  "description": "适合企业用户",
  "price": 49.99,
  "currency": "USD",
  "duration_days": 30,
  "traffic_limit_gb": 500.0,
  "max_concurrent_devices": 10,
  "speed_limit_upload": null,
  "speed_limit_download": null,
  "config_template": "# 企业套餐配置模板\n...",
  "is_active": true,
  "is_featured": true,
  "sort_order": 3
}

响应：
{
  "success": true,
  "data": {
    "plan": {
      "id": "bb0e8400-e29b-41d4-a716-446655440007",
      // 完整套餐信息
    }
  }
}
```

##### 更新套餐
```
PUT /api/v1/admin/plans/{plan_id}
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "price": 39.99,
  "description": "更新后的描述",
  "is_featured": false
}

响应：
{
  "success": true,
  "data": {
    "plan": {
      // 更新后的套餐信息
    }
  }
}
```

#### 2.8.3 订单管理

##### 获取订单列表
```
GET /api/v1/admin/orders?page=1&limit=20&status=pending_payment&user_search=username
Authorization: Bearer <admin_token>

响应：
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "880e8400-e29b-41d4-a716-446655440004",
        "order_number": "ORD-20250708-123456",
        "user": {
          "id": "550e8400-e29b-41d4-a716-446655440000",
          "username": "testuser",
          "email": "<EMAIL>"
        },
        "plan_name": "高级套餐",
        "amount": 19.99,
        "currency": "USD",
        "status": "pending_payment",
        "payment_method": "manual",
        "created_at": "2025-07-08T10:00:00Z",
        "expires_at": "2025-07-09T10:00:00Z"
      }
    ]
  },
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "total_pages": 3
  }
}
```

##### 处理订单
```
PUT /api/v1/admin/orders/{order_id}/process
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "action": "complete", // complete, cancel, refund
  "payment_reference": "PAY-123456789",
  "notes": "支付凭证已验证"
}

响应：
{
  "success": true,
  "data": {
    "order": {
      // 处理后的订单信息
      "status": "completed",
      "processed_at": "2025-07-08T11:30:00Z",
      "processed_by": "admin_user_id"
    },
    "subscription": {
      // 如果是完成订单，返回生成的订阅信息
      "id": "cc0e8400-e29b-41d4-a716-446655440008",
      "status": "active",
      "start_date": "2025-07-08T11:30:00Z",
      "end_date": "2025-08-08T11:30:00Z"
    }
  }
}
```

#### 2.8.4 公告管理

##### 创建公告
```
POST /api/v1/admin/announcements
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "title": "新功能上线通知",
  "content": "# 新功能上线\n\n我们很高兴地宣布...",
  "target_audience": "all",
  "target_plan_ids": null,
  "type": "info",
  "is_popup": false,
  "is_pinned": false,
  "publish_at": "2025-07-08T10:00:00Z",
  "expires_at": "2025-07-15T10:00:00Z"
}

响应：
{
  "success": true,
  "data": {
    "announcement": {
      "id": "dd0e8400-e29b-41d4-a716-446655440009",
      // 完整公告信息
    }
  }
}
```

#### 2.8.5 系统统计

##### 获取仪表盘统计
```
GET /api/v1/admin/dashboard/stats
Authorization: Bearer <admin_token>

响应：
{
  "success": true,
  "data": {
    "overview": {
      "total_users": 1520,
      "active_users": 1340,
      "total_subscriptions": 980,
      "active_subscriptions": 856,
      "pending_orders": 15,
      "total_revenue": 45678.90,
      "monthly_revenue": 8934.50
    },
    "recent_activity": {
      "new_users_today": 12,
      "new_orders_today": 8,
      "completed_orders_today": 6,
      "total_traffic_today_gb": 1250.5
    },
    "top_plans": [
      {
        "plan_name": "高级套餐",
        "active_subscriptions": 450,
        "monthly_revenue": 4500.0
      }
    ],
    "traffic_stats": {
      "total_traffic_gb": 125000.0,
      "monthly_traffic_gb": 12500.0,
      "daily_traffic_gb": 850.5
    }
  }
}
```

### 2.9 系统配置 API

#### 2.9.1 获取系统设置
```
GET /api/v1/admin/settings
Authorization: Bearer <admin_token>

响应：
{
  "success": true,
  "data": {
    "settings": [
      {
        "key": "site_name",
        "value": "VPN Service",
        "value_type": "string",
        "description": "站点名称",
        "is_public": true
      },
      {
        "key": "cleanup_inactive_users_days",
        "value": "30",
        "value_type": "integer",
        "description": "清理未购买套餐用户的天数",
        "is_public": false
      }
    ]
  }
}
```

#### 2.9.2 更新系统设置
```
PUT /api/v1/admin/settings
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "settings": [
    {
      "key": "site_name",
      "value": "新的站点名称"
    },
    {
      "key": "cleanup_inactive_users_days",
      "value": "45"
    }
  ]
}

响应：
{
  "success": true,
  "message": "设置更新成功"
}
```

### 2.10 WebSocket 实时 API

#### 2.10.1 客户端状态推送
```
WebSocket: /api/v1/ws/client
Authorization: Bearer <token>

客户端接收消息格式：
{
  "type": "subscription_update",
  "data": {
    "subscription": {
      "status": "expired",
      "remaining_traffic_gb": 0,
      "end_date": "2025-07-08T10:00:00Z"
    }
  },
  "timestamp": "2025-07-08T10:00:00Z"
}

消息类型：
- subscription_update: 订阅状态更新
- traffic_update: 流量使用更新
- announcement: 新公告推送
- force_disconnect: 强制断开连接
```

### 2.11 错误代码定义

```
认证相关:
- AUTH_INVALID_CREDENTIALS: 无效的用户名或密码
- AUTH_TOKEN_EXPIRED: 令牌已过期
- AUTH_TOKEN_INVALID: 无效的令牌
- AUTH_INSUFFICIENT_PERMISSIONS: 权限不足

用户相关:
- USER_NOT_FOUND: 用户不存在
- USER_ALREADY_EXISTS: 用户已存在
- USER_INACTIVE: 用户账户已禁用
- USER_BANNED: 用户已被封禁

订阅相关:
- SUBSCRIPTION_NOT_FOUND: 订阅不存在
- SUBSCRIPTION_EXPIRED: 订阅已过期
- SUBSCRIPTION_SUSPENDED: 订阅已暂停
- SUBSCRIPTION_TRAFFIC_EXCEEDED: 流量已用尽

订单相关:
- ORDER_NOT_FOUND: 订单不存在
- ORDER_EXPIRED: 订单已过期
- ORDER_ALREADY_PROCESSED: 订单已处理

系统相关:
- MAINTENANCE_MODE: 系统维护中
- RATE_LIMIT_EXCEEDED: 请求频率过高
- VALIDATION_ERROR: 输入数据验证失败
- INTERNAL_SERVER_ERROR: 内部服务器错误
```

## 3. 与用户管理 API 服务器的集成

### 3.1 用户管理服务接口定义

```rust
// 用户管理服务客户端接口
pub struct UserManagementClient {
    base_url: String,
    api_key: String,
}

impl UserManagementClient {
    // 创建代理用户
    pub async fn create_proxy_user(&self, user_id: Uuid) -> Result<ProxyUser, Error> {
        // POST /api/users
        // 返回: { username, password, port }
    }
    
    // 删除代理用户
    pub async fn delete_proxy_user(&self, user_id: Uuid) -> Result<(), Error> {
        // DELETE /api/users/{user_id}
    }
    
    // 暂停代理用户
    pub async fn suspend_proxy_user(&self, user_id: Uuid) -> Result<(), Error> {
        // PUT /api/users/{user_id}/suspend
    }
    
    // 恢复代理用户
    pub async fn resume_proxy_user(&self, user_id: Uuid) -> Result<(), Error> {
        // PUT /api/users/{user_id}/resume
    }
    
    // 更新用户配置
    pub async fn update_proxy_config(&self, user_id: Uuid, config: ProxyConfig) -> Result<(), Error> {
        // PUT /api/users/{user_id}/config
    }
    
    // 获取用户统计
    pub async fn get_user_stats(&self, user_id: Uuid) -> Result<UserStats, Error> {
        // GET /api/users/{user_id}/stats
    }
}

#[derive(Serialize, Deserialize)]
pub struct ProxyUser {
    pub username: String,
    pub password: String,
    pub port: u16,
}

#[derive(Serialize, Deserialize)]
pub struct UserStats {
    pub bytes_uploaded: u64,
    pub bytes_downloaded: u64,
    pub connection_count: u32,
    pub last_seen: DateTime<Utc>,
}
```

### 3.2 集成流程

1. **用户订阅激活时**:
   - 调用 `create_proxy_user()` 创建代理用户
   - 将返回的凭证保存到 `subscriptions` 表
   - 生成完整的 leaf 配置文件

2. **订阅过期/暂停时**:
   - 调用 `suspend_proxy_user()` 暂停用户
   - 更新本地订阅状态

3. **订阅恢复时**:
   - 调用 `resume_proxy_user()` 恢复用户
   - 更新本地订阅状态

4. **订阅删除时**:
   - 调用 `delete_proxy_user()` 删除用户
   - 清理本地数据

5. **定期同步统计**:
   - 定时调用 `get_user_stats()` 获取流量统计
   - 更新 `subscriptions.used_traffic_gb` 字段

---

这个设计提供了完整的数据库模型和 API 接口，涵盖了 PRD 中提到的所有功能需求。数据库设计考虑了性能、扩展性和数据一致性，API 设计遵循 RESTful 原则，提供了清晰的接口和错误处理机制。