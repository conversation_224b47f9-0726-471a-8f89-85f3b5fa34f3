# Tauri Desktop Client Setup

This document describes the setup and structure of the Tauri desktop client for the VPN application.

## Project Structure

```
vpn/
├── client/                    # Tauri Rust backend
│   ├── src/
│   │   ├── main.rs           # Main Tauri application
│   │   └── proxy_manager.rs  # Proxy management module
│   ├── tauri.conf.json       # Tauri configuration
│   └── Cargo.toml           # Rust dependencies
├── frontend/                 # React frontend
│   ├── src/
│   │   ├── components/       # React components
│   │   ├── contexts/         # React contexts
│   │   ├── pages/           # Application pages
│   │   └── styles.css       # Global styles
│   ├── package.json         # Node.js dependencies
│   └── vite.config.ts       # Vite configuration
└── dist/                    # Built frontend assets
```

## Key Features Implemented

### 1. Tauri Backend (`client/`)
- **Main Application**: Entry point with Tauri commands
- **Proxy Manager**: Mock proxy status and control
- **Window Configuration**: Responsive window with appropriate sizing
- **Commands**: 
  - `greet` - Test command
  - `get_proxy_status` - Get current proxy status
  - `toggle_proxy` - Connect/disconnect proxy
  - `get_proxy_stats` - Get connection statistics
  - `update_config` - Update proxy configuration

### 2. React Frontend (`frontend/`)
- **Modern Stack**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS with custom components
- **Routing**: React Router for navigation
- **State Management**: Context API for auth and proxy state
- **UI Components**: 
  - Dashboard with connection status
  - Login page with authentication
  - Settings page for configuration
  - Logs page for monitoring
  - Responsive sidebar navigation

### 3. Authentication System
- **AuthContext**: JWT token management
- **Login Flow**: Username/password authentication
- **Token Storage**: Local storage with session management
- **Route Protection**: Protected routes with authentication guards

### 4. Proxy Management
- **ProxyContext**: Connection state management
- **Real-time Updates**: Status polling every 2 seconds
- **Statistics**: Upload/download tracking
- **Error Handling**: Comprehensive error states

## Build Configuration

### Frontend Build
```bash
cd frontend
npm install
npm run build
```

### Tauri Build
```bash
cd client
cargo tauri build --debug
```

## Development Workflow

1. **Start Backend**: Run the Rust backend API server
2. **Build Frontend**: Build React app to `dist/`
3. **Run Tauri**: Start desktop application with `cargo tauri dev`

## Configuration

### Tauri Configuration (`tauri.conf.json`)
- **Window Size**: 1000x700 with 800x600 minimum
- **Build Commands**: Configured for frontend integration
- **Security**: CSP disabled for development

### Frontend Configuration
- **Vite**: Modern bundler with React support
- **Tailwind**: Utility-first CSS framework
- **TypeScript**: Type-safe development

## Key Dependencies

### Rust (Tauri)
- `tauri`: Desktop application framework
- `tokio`: Async runtime
- `serde`: Serialization
- `reqwest`: HTTP client

### Frontend (React)
- `react`: UI library
- `react-router-dom`: Routing
- `@tauri-apps/api`: Tauri API bindings
- `tailwindcss`: CSS framework
- `lucide-react`: Icons

## Next Steps

1. **Authentication Integration**: Connect to backend API
2. **Proxy Integration**: Implement real proxy functionality
3. **Node Selection**: Add server selection interface
4. **Real-time Monitoring**: Implement live statistics
5. **System Integration**: Add system tray and notifications

This setup provides a solid foundation for a modern desktop VPN client with a clean architecture and modern technologies.