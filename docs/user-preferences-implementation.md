# 用户偏好持久化实现说明

## 功能概述

实现了用户代理模式和节点选择的持久化存储，主要关注全局模式的偏好记忆功能。用户的选择会在应用重启后自动恢复。

## 实现特性

### 1. 数据存储
- **存储机制**: 使用浏览器 localStorage
- **存储位置**: `vpn-user-preferences` 键
- **数据格式**: JSON 格式
- **自动编码**: 自动处理序列化和反序列化

### 2. 偏好内容
```typescript
interface UserPreferences {
  proxyMode: 'rule' | 'global';           // 代理模式偏好
  preferredOutboundTag?: string;          // 全局模式下的出站标签偏好
  lastConfigHash?: string;                // 配置哈希值（用于检测配置变更）
  lastUpdated: number;                    // 最后更新时间
}
```

### 3. 核心功能

#### 自动保存
- 用户更改代理模式时自动保存
- 用户在全局模式下选择出站时自动保存
- 使用防抖机制避免频繁保存（300ms 延迟）

#### 自动恢复
- 应用启动时自动加载用户偏好
- 优先使用用户偏好，降级到系统默认
- 全局模式下自动恢复出站选择

#### 配置变更处理
- 检测配置文件变更（通过哈希值比较）
- 配置变更时尝试恢复用户偏好
- 智能匹配：出站标签不存在时选择第一个可用选项

#### 用户通知
- 偏好恢复成功时显示友好提示
- 配置更新时显示通知消息
- 3秒后自动消失

## 设计理念

### 简化规则模式
- 规则模式的节点选择由 leaf 后端自动记住
- 前端不需要复杂的规则模式偏好管理
- 专注于全局模式的用户体验

### 面向未来的设计
- 支持配置文件从服务器自动更新的场景
- 智能的降级策略保证用户体验
- 配置变更检测确保偏好不丢失

### 错误处理
- localStorage 不可用时优雅降级
- 偏好数据损坏时自动恢复
- API 调用失败时不影响 UI 状态

## 使用场景

1. **日常使用**: 用户选择全局模式和特定出站后，重启应用自动恢复
2. **配置更新**: 服务器推送新配置时，自动重新应用用户偏好
3. **降级处理**: 偏好的出站不存在时，自动选择第一个可用选项

## 技术实现

### 关键函数
- `loadUserPreferences()`: 加载用户偏好
- `saveUserPreferences()`: 保存用户偏好
- `saveUserPreferencesDebounced()`: 防抖保存
- `handleConfigUpdate()`: 配置变更处理
- `findBestGlobalOutboundMatch()`: 智能出站匹配

### 生命周期
1. 组件挂载时加载偏好
2. 配置加载完成后应用偏好
3. 用户操作时自动保存
4. 配置变更时重新应用

## 兼容性

- 向后兼容：不影响现有功能
- 渐进增强：localStorage 不可用时不影响基本功能
- 跨平台：使用标准 Web API，支持所有平台

## 维护建议

1. 定期清理过期的偏好数据
2. 监控偏好恢复成功率
3. 根据用户反馈优化匹配算法
4. 考虑添加偏好重置功能

## 测试场景

1. 首次启动应用（无偏好）
2. 设置全局模式后重启
3. 配置文件更新后的偏好恢复
4. 偏好的出站不存在时的降级处理
5. localStorage 不可用时的降级行为