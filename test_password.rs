// Temporary test file to verify password hashing works
use std::process::Command;

fn main() {
    // Create a simple test program that can be run standalone
    let test_code = r#"
use bcrypt::{hash, verify, DEFAULT_COST};

fn main() {
    let password = "test_password_123!";
    let hashed = hash(password, DEFAULT_COST).unwrap();
    
    println!("Original password: {}", password);
    println!("Hashed password: {}", hashed);
    println!("Hash starts with $2b$: {}", hashed.starts_with("$2b$"));
    
    let verified = verify(password, &hashed).unwrap();
    println!("Verification successful: {}", verified);
    
    let wrong_verified = verify("wrong_password", &hashed).unwrap();
    println!("Wrong password verification: {}", wrong_verified);
}
"#;

    std::fs::write("/tmp/test_bcrypt.rs", test_code).unwrap();
    
    // Use the current backend's Cargo.toml to get the bcrypt dependency
    let output = Command::new("cargo")
        .args(&["run", "--manifest-path", "/Users/<USER>/code/github/vpn/backend/Cargo.toml", "--bin", "test_bcrypt"])
        .current_dir("/tmp")
        .output()
        .unwrap();
    
    println!("stdout: {}", String::from_utf8_lossy(&output.stdout));
    println!("stderr: {}", String::from_utf8_lossy(&output.stderr));
}