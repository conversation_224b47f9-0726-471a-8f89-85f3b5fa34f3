import React, { useState, useEffect } from "react";
import { Save, RefreshCw } from "lucide-react";
import { useProxy } from "../contexts/ProxyContext";

const Settings: React.FC = () => {
  const { updateConfig, saveConfig, loadConfig } = useProxy();
  const [config, setConfig] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState<"success" | "error" | "">("");

  // Load saved config on component mount
  useEffect(() => {
    const loadSavedConfig = async () => {
      try {
        const savedConfig = await loadConfig();
        if (savedConfig) {
          setConfig(savedConfig);
          setMessage("Configuration loaded from saved settings");
          setMessageType("success");
          setTimeout(() => {
            setMessage("");
            setMessageType("");
          }, 3000);
        }
      } catch (error) {
        console.error("Failed to load saved config:", error);
      }
    };

    loadSavedConfig();
  }, []); // Empty dependency array to run only once on mount

  const handleConfigUpdate = async () => {
    if (!config.trim()) {
      setMessage("Please enter a configuration");
      setMessageType("error");
      return;
    }

    setIsLoading(true);
    setMessage("");
    
    try {
      const success = await updateConfig(config);
      if (success) {
        // Auto-save config after successful update
        const saveSuccess = await saveConfig(config);
        if (saveSuccess) {
          setMessage("Configuration updated and saved successfully");
          setMessageType("success");
        } else {
          setMessage("Configuration updated but failed to save");
          setMessageType("error");
        }
      } else {
        setMessage("Failed to update configuration");
        setMessageType("error");
      }
    } catch (error) {
      setMessage("Error updating configuration");
      setMessageType("error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = () => {
    setConfig("");
    setMessage("");
    setMessageType("");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Configure your VPN connection settings</p>
      </div>

      {/* Configuration Section */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">VPN Configuration</h3>
          <p className="text-sm text-gray-500 mt-1">
            Enter your VPN configuration details
          </p>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            <div>
              <label htmlFor="config" className="block text-sm font-medium text-gray-700">
                Configuration
              </label>
              <textarea
                id="config"
                rows={10}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Enter your VPN configuration here..."
                value={config}
                onChange={(e) => setConfig(e.target.value)}
              />
            </div>

            {message && (
              <div
                className={`rounded-md p-4 ${
                  messageType === "success"
                    ? "bg-green-50 text-green-700"
                    : "bg-red-50 text-red-700"
                }`}
              >
                {message}
              </div>
            )}

            <div className="flex space-x-3">
              <button
                onClick={handleConfigUpdate}
                disabled={isLoading}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {isLoading ? "Updating..." : "Update Configuration"}
              </button>

              <button
                onClick={handleRefresh}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Connection Settings */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Connection Settings</h3>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Auto-connect on startup
                </label>
                <p className="text-sm text-gray-500">
                  Automatically connect to VPN when the app starts
                </p>
              </div>
              <button
                type="button"
                className="bg-gray-200 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                role="switch"
                aria-checked="false"
              >
                <span className="translate-x-0 pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"></span>
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Start minimized
                </label>
                <p className="text-sm text-gray-500">
                  Start the application minimized to system tray
                </p>
              </div>
              <button
                type="button"
                className="bg-gray-200 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                role="switch"
                aria-checked="false"
              >
                <span className="translate-x-0 pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"></span>
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Enable notifications
                </label>
                <p className="text-sm text-gray-500">
                  Show notifications for connection status changes
                </p>
              </div>
              <button
                type="button"
                className="bg-primary-600 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                role="switch"
                aria-checked="true"
              >
                <span className="translate-x-5 pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"></span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;