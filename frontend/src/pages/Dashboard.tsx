import React, { useState, useEffect, useCallback } from "react";
import { Play, Square, Wifi, Upload, Download, Clock, Settings, MapPin, Zap, RefreshCw, ChevronDown, ChevronRight } from "lucide-react";
import { useProxy } from "../contexts/ProxyContext";
import { useAuth } from "../contexts/AuthContext";
import { invoke } from "@tauri-apps/api/core";
import clsx from "clsx";

interface ProxyNode {
  id: string;
  name: string;
  location: string;
  latency: number;
  available: boolean;
  selectable?: boolean;
}

interface ProxyTag {
  tag: string;
  protocol: string;
  nodes: ProxyNode[];
  selected: string;
  selectable: boolean;
  has_sub_outbounds: boolean;
}

interface ProxyNodesResponse {
  tags: ProxyTag[];
  legacy_nodes: ProxyNode[];
  selected: string;
}

interface ProxyMode {
  id: string;
  name: string;
  description: string;
}

interface UserPreferences {
  lastSelectedGlobalOutbound?: string;  // 最近选择的全局出站标签
  currentMode?: 'rule' | 'global';     // 当前选择的代理模式
}

const Dashboard: React.FC = () => {
  const { isConnected, isConnecting, stats, error, connect, disconnect } = useProxy();
  const { isAuthenticated } = useAuth();
  const [proxyMode, setProxyMode] = useState<string>("rule");
  const [selectedNode, setSelectedNode] = useState<string>("direct");
  const [globalSelectedOutbound, setGlobalSelectedOutbound] = useState<string>("direct");
  const [proxyTags, setProxyTags] = useState<ProxyTag[]>([]);
  const [nodes, setNodes] = useState<ProxyNode[]>([]); // Keep for backward compatibility
  const [loadingNodes, setLoadingNodes] = useState(false);
  const [testingLatency, setTestingLatency] = useState<Set<string>>(new Set());
  const [expandedTags, setExpandedTags] = useState<Set<string>>(new Set());
  const [preferencesMessage, setPreferencesMessage] = useState<string | null>(null);

  const proxyModes: ProxyMode[] = [
    { id: "global", name: "Global Mode", description: "Route all traffic through VPN" },
    { id: "rule", name: "Rule Mode", description: "Use proxy auto-config rules" }
  ];

  // 用户偏好管理功能
  const loadUserPreferences = useCallback((): UserPreferences | null => {
    try {
      const saved = localStorage.getItem('vpn-user-preferences');
      if (saved) {
        const preferences: UserPreferences = JSON.parse(saved);
        return preferences;
      }
    } catch (error) {
      console.error('Failed to load user preferences:', error);
    }
    return null;
  }, []);

  const saveUserPreferences = useCallback((preferences: UserPreferences) => {
    try {
      localStorage.setItem('vpn-user-preferences', JSON.stringify(preferences));
    } catch (error) {
      console.error('Failed to save user preferences:', error);
    }
  }, []);


  // 保存用户偏好
  const saveGlobalOutboundPreference = useCallback((outboundTag: string) => {
    const preferences: UserPreferences = {
      lastSelectedGlobalOutbound: outboundTag
    };
    saveUserPreferences(preferences);
  }, [saveUserPreferences]);



  // Load proxy nodes on component mount
  useEffect(() => {
    loadProxyNodes();
  }, [isAuthenticated]);

  // Auto-refresh nodes when proxy connects
  useEffect(() => {
    if (isConnected) {
      // Refresh nodes when proxy connects
      const timer = setTimeout(() => {
        loadProxyNodes();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isConnected]);

  // Listen for node refresh events
  useEffect(() => {
    const handleRefreshNodes = () => {
      loadProxyNodes();
    };

    window.addEventListener('refreshNodes', handleRefreshNodes);
    return () => window.removeEventListener('refreshNodes', handleRefreshNodes);
  }, []);

  // Listen for page visibility changes to refresh state when user returns to Dashboard
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && isAuthenticated) {
        // Page became visible, refresh the current global target state
        console.log('Dashboard page became visible, refreshing global target state');
        refreshGlobalTargetState();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isAuthenticated]);

  // Function to refresh global target state from backend
  const refreshGlobalTargetState = useCallback(async () => {
    if (!isAuthenticated) return;
    
    // First, check saved mode preference
    const preferences = loadUserPreferences();
    const savedMode = preferences?.currentMode;
    
    if (savedMode) {
      // Use saved mode preference
      setProxyMode(savedMode);
      console.log('Restored mode from preferences:', savedMode);
      
      if (savedMode === 'global') {
        // For global mode, update the global outbound from backend
        try {
          const currentTarget = await invoke<string | null>("get_global_target");
          if (currentTarget) {
            setGlobalSelectedOutbound(currentTarget);
            console.log('Refreshed global target state:', currentTarget);
          } else if (preferences?.lastSelectedGlobalOutbound) {
            // If backend has no target but we have a saved preference, try to restore it
            try {
              await invoke<string>("set_global_target", { target: preferences.lastSelectedGlobalOutbound });
              setGlobalSelectedOutbound(preferences.lastSelectedGlobalOutbound);
              console.log('Restored global target from preferences:', preferences.lastSelectedGlobalOutbound);
            } catch (restoreErr) {
              console.error("Failed to restore global target:", restoreErr);
            }
          }
        } catch (err) {
          console.error("Failed to refresh global target state:", err);
        }
      } else {
        // For rule mode, make sure backend is in rule mode
        setGlobalSelectedOutbound("direct");
        try {
          const currentTarget = await invoke<string | null>("get_global_target");
          if (currentTarget) {
            // Backend still has global target, clear it
            await invoke<string>("set_global_target", { target: null });
            console.log('Cleared global target to ensure rule mode');
          }
        } catch (err) {
          console.error("Failed to clear global target for rule mode:", err);
        }
      }
    } else {
      // No saved mode preference, infer from backend state
      try {
        const currentTarget = await invoke<string | null>("get_global_target");
        if (currentTarget) {
          setGlobalSelectedOutbound(currentTarget);
          setProxyMode("global");
          console.log('Inferred global mode from backend state:', currentTarget);
        } else {
          setProxyMode("rule");
          console.log('Inferred rule mode from backend state');
        }
      } catch (err) {
        console.error("Failed to refresh global target state:", err);
      }
    }
  }, [isAuthenticated, loadUserPreferences]);

  // Load current global target and user preferences on component mount
  useEffect(() => {
    const loadGlobalTargetAndPreferences = async () => {
      if (!isAuthenticated) return;
      
      // First, try to load saved mode preference
      const preferences = loadUserPreferences();
      const savedMode = preferences?.currentMode;
      
      if (savedMode) {
        // Use saved mode preference
        setProxyMode(savedMode);
        console.log('Loaded saved mode from preferences:', savedMode);
        
        if (savedMode === 'global') {
          // For global mode, restore the global outbound
          try {
            const currentTarget = await invoke<string | null>("get_global_target");
            if (currentTarget) {
              setGlobalSelectedOutbound(currentTarget);
              console.log('Loaded current global target from backend:', currentTarget);
            } else if (preferences.lastSelectedGlobalOutbound) {
              // Backend has no target but we have a saved preference, restore it
              try {
                await invoke<string>("set_global_target", { target: preferences.lastSelectedGlobalOutbound });
                setGlobalSelectedOutbound(preferences.lastSelectedGlobalOutbound);
                console.log('Restored global target from preferences:', preferences.lastSelectedGlobalOutbound);
                
                // Show user-friendly message
                setPreferencesMessage(`已恢复您的偏好设置：${preferences.lastSelectedGlobalOutbound}`);
                setTimeout(() => setPreferencesMessage(null), 3000);
              } catch (err) {
                console.error("Failed to restore global target:", err);
              }
            }
          } catch (err) {
            console.error("Failed to load current global target:", err);
          }
        } else {
          // For rule mode, ensure backend is in rule mode
          setGlobalSelectedOutbound("direct");
          try {
            const currentTarget = await invoke<string | null>("get_global_target");
            if (currentTarget) {
              // Backend still has global target, clear it
              await invoke<string>("set_global_target", { target: null });
              console.log('Cleared global target to ensure rule mode');
            }
          } catch (err) {
            console.error("Failed to clear global target for rule mode:", err);
          }
        }
      } else {
        // No saved mode preference, infer from backend state
        try {
          const currentTarget = await invoke<string | null>("get_global_target");
          if (currentTarget) {
            setGlobalSelectedOutbound(currentTarget);
            setProxyMode("global");
            console.log('Inferred global mode from backend state:', currentTarget);
          } else {
            setProxyMode("rule");
            console.log('Inferred rule mode from backend state');
          }
        } catch (err) {
          console.error("Failed to load global target:", err);
        }
      }
    };

    loadGlobalTargetAndPreferences();
  }, [isAuthenticated, loadUserPreferences]);

  const loadProxyNodes = async () => {
    if (!isAuthenticated) return;
    
    setLoadingNodes(true);
    try {
      const result = await invoke<ProxyNodesResponse>("get_proxy_nodes");
      
      // Debug: Log the full response structure
      console.log('=== Debug: Full proxy nodes response ===');
      console.log('Full result:', JSON.stringify(result, null, 2));
      
      // Debug: Log each tag's structure
      result.tags.forEach((tag, index) => {
        console.log(`Tag ${index + 1} - ${tag.tag}:`, {
          protocol: tag.protocol,
          has_sub_outbounds: tag.has_sub_outbounds,
          nodes_count: tag.nodes.length,
          nodes: tag.nodes.map(node => node.id)
        });
        
        if (tag.tag === 'Proxy') {
          console.log('=== Proxy group details ===');
          console.log('Proxy nodes:', tag.nodes);
          console.log('Expected: Should only have FailOver and direct');
          console.log('Actual node IDs:', tag.nodes.map(n => n.id));
        }
      });
      
      setProxyTags(result.tags);
      setNodes(result.legacy_nodes || []); // Keep for backward compatibility
      setSelectedNode(result.selected);
      
      // Don't auto-expand tags by default to avoid confusion
      // Users can manually expand groups they want to see
      setExpandedTags(new Set());
      
      // 如果当前没有全局目标，尝试从偏好恢复或使用第一个可用的
      if (proxyMode === 'global' && !globalSelectedOutbound && result.tags.length > 0) {
        const preferences = loadUserPreferences();
        const targetTag = preferences?.lastSelectedGlobalOutbound || result.tags[0].tag;
        
        if (result.tags.find(tag => tag.tag === targetTag)) {
          setGlobalSelectedOutbound(targetTag);
          try {
            await invoke<string>("set_global_target", { target: targetTag });
            console.log('Set global target to:', targetTag);
          } catch (err) {
            console.error("Failed to set global target:", err);
          }
        }
      }
    } catch (err) {
      console.error("Failed to load proxy nodes:", err);
      // Fallback to old format
      try {
        const fallbackResult = await invoke<{ nodes: ProxyNode[]; selected: string }>("get_proxy_nodes");
        setNodes(fallbackResult.nodes);
        setSelectedNode(fallbackResult.selected);
        
      } catch (fallbackErr) {
        console.error("Failed to load proxy nodes (fallback):", fallbackErr);
      }
    } finally {
      setLoadingNodes(false);
    }
  };

  const testNodeLatency = async (nodeId: string) => {
    setTestingLatency(prev => new Set(prev).add(nodeId));
    
    try {
      // Simulate latency testing - in real implementation this would ping the server
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      // Update node latency with random value for demo
      const newLatency = Math.floor(Math.random() * 200) + 10;
      
      // Update in both tags and legacy nodes
      setProxyTags(prev => prev.map(tag => ({
        ...tag,
        nodes: tag.nodes.map(node => 
          node.id === nodeId ? { ...node, latency: newLatency } : node
        )
      })));
      
      setNodes(prev => prev.map(node => 
        node.id === nodeId ? { ...node, latency: newLatency } : node
      ));
    } catch (err) {
      console.error("Failed to test latency:", err);
    } finally {
      setTestingLatency(prev => {
        const newSet = new Set(prev);
        newSet.delete(nodeId);
        return newSet;
      });
    }
  };

  const toggleTagExpansion = (tagName: string) => {
    setExpandedTags(prev => {
      const newSet = new Set(prev);
      if (newSet.has(tagName)) {
        newSet.delete(tagName);
      } else {
        newSet.add(tagName);
      }
      return newSet;
    });
  };

  const selectNodeForTag = async (tagName: string, nodeId: string) => {
    // Check if this tag is selectable
    const tag = proxyTags.find(t => t.tag === tagName);
    if (!tag || !tag.selectable) {
      return; // Don't try to select non-selectable tags
    }
    
    try {
      await invoke<string>("select_proxy_node_for_tag", { tag: tagName, nodeId });
      
      // Update the selected node for this tag
      setProxyTags(prev => prev.map(tag => 
        tag.tag === tagName ? { ...tag, selected: nodeId } : tag
      ));
      
      // Update global selected node (for backward compatibility)
      setSelectedNode(nodeId);
      
      // 规则模式不需要保存用户偏好，leaf 后端会自动记住选择
    } catch (err) {
      console.error("Failed to select node:", err);
    }
  };

  const selectNode = async (nodeId: string) => {
    try {
      await invoke<string>("select_proxy_node", { nodeId });
      setSelectedNode(nodeId);
      
      // 规则模式不需要保存用户偏好，leaf 后端会自动记住选择
    } catch (err) {
      console.error("Failed to select node:", err);
    }
  };

  const handleModeChange = async (mode: string) => {
    try {
      if (mode === "global") {
        // When switching to global mode, try to restore last selected or use first available
        const preferences = loadUserPreferences();
        const targetTag = preferences?.lastSelectedGlobalOutbound || (proxyTags.length > 0 ? proxyTags[0].tag : null);
        
        if (targetTag) {
          await invoke<string>("set_global_target", { target: targetTag });
          setGlobalSelectedOutbound(targetTag);
        }
      } else if (mode === "rule") {
        // When switching to rule mode, disable global mode
        await invoke<string>("set_global_target", { target: null });
        setGlobalSelectedOutbound("direct");
        
        // Verify that global target was actually cleared
        try {
          const verifyTarget = await invoke<string | null>("get_global_target");
          if (verifyTarget) {
            console.warn("Global target was not cleared properly, retrying...");
            await invoke<string>("set_global_target", { target: null });
          }
        } catch (verifyErr) {
          console.error("Failed to verify global target clear:", verifyErr);
        }
      }
      
      setProxyMode(mode);
      
      // Save the current mode to preferences
      const currentPreferences = loadUserPreferences();
      const updatedPreferences: UserPreferences = {
        ...currentPreferences,
        currentMode: mode as 'rule' | 'global'
      };
      saveUserPreferences(updatedPreferences);
      console.log(`Mode changed to: ${mode}, saved to preferences`);
      
    } catch (err) {
      console.error("Failed to change proxy mode:", err);
      // Still update the UI even if API call fails
      setProxyMode(mode);
      
      // Still save the mode preference even if backend call failed
      const currentPreferences = loadUserPreferences();
      const updatedPreferences: UserPreferences = {
        ...currentPreferences,
        currentMode: mode as 'rule' | 'global'
      };
      saveUserPreferences(updatedPreferences);
    }
  };

  const selectGlobalOutbound = async (outboundTag: string) => {
    try {
      console.log(`Attempting to select global outbound: ${outboundTag}`);
      // Call the new API to set global target
      await invoke<string>("set_global_target", { target: outboundTag });
      setGlobalSelectedOutbound(outboundTag);
      console.log(`Global outbound selected: ${outboundTag}`);
      
      // 保存用户偏好
      try {
        saveGlobalOutboundPreference(outboundTag);
        console.log(`User preferences saved for: ${outboundTag}`);
      } catch (prefErr) {
        console.error("Failed to save user preferences:", prefErr);
      }
    } catch (err) {
      console.error("Failed to select global outbound:", err);
      
      // 提供用户反馈
      setPreferencesMessage(`选择节点失败: ${err}，请稍后重试`);
      setTimeout(() => setPreferencesMessage(null), 5000);
      
      // 尝试恢复到之前的状态
      try {
        const currentTarget = await invoke<string | null>("get_global_target");
        if (currentTarget && currentTarget !== outboundTag) {
          setGlobalSelectedOutbound(currentTarget);
          console.log('Reverted to current backend target:', currentTarget);
        }
      } catch (revertErr) {
        console.error("Failed to revert to current target:", revertErr);
        // 最后的fallback：从偏好恢复
        const preferences = loadUserPreferences();
        if (preferences && preferences.lastSelectedGlobalOutbound) {
          setGlobalSelectedOutbound(preferences.lastSelectedGlobalOutbound);
        }
      }
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const handleToggleConnection = () => {
    if (isConnected) {
      disconnect();
    } else {
      connect();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Monitor your VPN connection and statistics</p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        </div>
      )}

      {/* Preferences Message */}
      {preferencesMessage && (
        <div className="rounded-md bg-blue-50 p-4">
          <div className="flex">
            <div className="text-sm text-blue-700">{preferencesMessage}</div>
          </div>
        </div>
      )}

      {/* Connection Status Card */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div
                className={clsx(
                  "w-4 h-4 rounded-full mr-3",
                  isConnected ? "bg-green-500" : "bg-red-500"
                )}
              />
              <div>
                <h2 className="text-lg font-medium text-gray-900">
                  VPN Status
                </h2>
                <p className="text-sm text-gray-500">
                  {isConnected ? "Connected and secure" : "Disconnected"}
                </p>
                {(proxyMode === "global" ? globalSelectedOutbound !== "direct" : selectedNode !== "direct") && (
                  <p className="text-xs text-blue-600">
                    via {
                      proxyMode === "global" 
                        ? globalSelectedOutbound
                        : // Try to find the node name from tag-based structure first
                          proxyTags.find(tag => tag.selected === selectedNode)?.nodes.find(n => n.id === selectedNode)?.name ||
                          // Fallback to legacy nodes structure
                          nodes.find(n => n.id === selectedNode)?.name ||
                          selectedNode
                    }
                  </p>
                )}
              </div>
            </div>
            
            <button
              onClick={handleToggleConnection}
              disabled={isConnecting}
              className={clsx(
                "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",
                isConnected
                  ? "bg-red-600 hover:bg-red-700 focus:ring-red-500"
                  : "bg-green-600 hover:bg-green-700 focus:ring-green-500"
              )}
            >
              {isConnecting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              ) : isConnected ? (
                <Square className="w-4 h-4 mr-2" />
              ) : (
                <Play className="w-4 h-4 mr-2" />
              )}
              {isConnecting
                ? "Connecting..."
                : isConnected
                ? "Disconnect"
                : "Connect"
              }
            </button>
          </div>
        </div>
      </div>

      {/* Proxy Mode Selection - Compact Tab Style */}
      {isConnected && (
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-6 py-3 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-base font-medium text-gray-900 flex items-center">
                <Settings className="w-4 h-4 mr-2" />
                策略
              </h3>
              <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                {proxyModes.map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => handleModeChange(mode.id)}
                    className={clsx(
                      "px-3 py-1 text-sm font-medium rounded-md transition-all duration-200",
                      proxyMode === mode.id
                        ? "bg-white text-gray-900 shadow-sm"
                        : "text-gray-600 hover:text-gray-900"
                    )}
                  >
                    {mode.id === "global" ? "全局代理" : "规则判定"}
                  </button>
                ))}
              </div>
            </div>
          </div>
          <div className="px-6 py-2 bg-gray-50 border-b border-gray-200">
            <p className="text-xs text-gray-600">
              {proxyMode === "global" ? "根据选择的代理策略来处理方式。" : "根据规则匹配处理方式。"}
            </p>
          </div>
        </div>
      )}

      {/* Node Selection - Only show when connected */}
      {isConnected && (
        <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-6 py-3 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-base font-medium text-gray-900 flex items-center">
              <MapPin className="w-4 h-4 mr-2" />
              代理
            </h3>
            <button
              onClick={loadProxyNodes}
              disabled={loadingNodes}
              className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-500 disabled:opacity-50"
            >
              <RefreshCw className={clsx("w-3 h-3 mr-1", loadingNodes && "animate-spin")} />
              刷新
            </button>
          </div>
        </div>
        <div className="px-6 py-3">
          <div className="space-y-3">
            {proxyMode === "global" ? (
              // Global Mode: Show proxy nodes first, then proxy groups
              <div className="space-y-4">
                {/* Global Mode Status Bar */}
                {globalSelectedOutbound !== "direct" && (
                  <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm font-medium text-blue-900">
                        当前全局代理: {globalSelectedOutbound}
                      </span>
                      {(() => {
                        const selectedTag = proxyTags.find(tag => tag.tag === globalSelectedOutbound);
                        if (selectedTag && selectedTag.protocol === "select" && selectedTag.selected) {
                          return (
                            <span className="text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded">
                              {selectedTag.selected}
                            </span>
                          );
                        }
                        return null;
                      })()}
                    </div>
                  </div>
                )}
                
                {/* Proxy Nodes Section - Show First */}
                {proxyTags.filter(tag => !tag.has_sub_outbounds).length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700 flex items-center">
                      <span className="w-1 h-4 bg-gray-400 rounded-full mr-2"></span>
                      代理节点
                    </h4>
                    <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                      {proxyTags.filter(tag => !tag.has_sub_outbounds).map((tag) => (
                        <div
                          key={tag.tag}
                          className={clsx(
                            "flex flex-col items-center justify-center p-3 rounded-lg border cursor-pointer transition-all",
                            globalSelectedOutbound === tag.tag
                              ? "border-blue-500 bg-blue-50"
                              : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                          )}
                          onClick={() => selectGlobalOutbound(tag.tag)}
                        >
                          <div className="flex items-center space-x-1 mb-1">
                            <div
                              className={clsx(
                                "w-2 h-2 rounded-full",
                                globalSelectedOutbound === tag.tag ? "bg-blue-500" : "bg-gray-300"
                              )}
                            />
                            <span className="text-xs text-gray-500 bg-gray-100 px-1 py-0.5 rounded">
                              {tag.protocol}
                            </span>
                          </div>
                          <div className="text-center">
                            <h4 className="text-xs font-medium text-gray-900 truncate max-w-full">
                              {tag.tag}
                            </h4>
                            {globalSelectedOutbound === tag.tag && (
                              <span className="text-xs text-blue-600 font-medium">已选择</span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Proxy Groups Section - Show Second */}
                {proxyTags.filter(tag => tag.has_sub_outbounds).length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700 flex items-center">
                      <span className="w-1 h-4 bg-gray-400 rounded-full mr-2"></span>
                      代理组
                    </h4>
                    {proxyTags.filter(tag => tag.has_sub_outbounds).map((tag) => (
                      <div key={tag.tag} className={clsx(
                        "border border-gray-200 rounded-lg overflow-hidden",
                        globalSelectedOutbound === tag.tag && "border-blue-500 bg-blue-50"
                      )}>
                        {/* Tag Header */}
                        <div
                          className={clsx(
                            "flex items-center justify-between p-3 cursor-pointer transition-colors",
                            globalSelectedOutbound === tag.tag
                              ? "bg-blue-50 hover:bg-blue-100"
                              : "bg-gray-50 hover:bg-gray-100"
                          )}
                          onClick={() => selectGlobalOutbound(tag.tag)}
                        >
                          <div className="flex items-center space-x-2">
                            {tag.has_sub_outbounds && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleTagExpansion(tag.tag);
                                }}
                                className="p-1 hover:bg-gray-200 rounded transition-colors"
                              >
                                {expandedTags.has(tag.tag) ? (
                                  <ChevronDown className="w-3 h-3 text-gray-500" />
                                ) : (
                                  <ChevronRight className="w-3 h-3 text-gray-500" />
                                )}
                              </button>
                            )}
                            <div
                              className={clsx(
                                "w-2 h-2 rounded-full",
                                globalSelectedOutbound === tag.tag ? "bg-blue-500" : "bg-gray-300"
                              )}
                            />
                            <h4 className="text-sm font-medium text-gray-900">{tag.tag}</h4>
                            <span className={clsx(
                              "px-2 py-1 text-xs rounded-full",
                              tag.protocol === "select"
                                ? "bg-green-100 text-green-800"
                                : "bg-gray-100 text-gray-800"
                            )}>
                              {tag.protocol}
                            </span>
                            {globalSelectedOutbound === tag.tag && (
                              <span className="text-xs text-blue-600 font-medium bg-blue-100 px-2 py-1 rounded">
                                已选择
                              </span>
                            )}
                          </div>
                          <div className="text-xs text-gray-500">
                            {tag.has_sub_outbounds ? `${tag.nodes.length} 节点` : "单一出站"}
                            {tag.selected && tag.selected !== "N/A" && (
                              <span className="ml-2 text-blue-600 font-medium">• {tag.selected}</span>
                            )}
                          </div>
                        </div>

                        {/* Tag Nodes - only show if has sub_outbounds and expanded */}
                        {tag.has_sub_outbounds && expandedTags.has(tag.tag) && (
                          <div className="border-t border-gray-200 bg-gray-50">
                            <div className="p-3">
                              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                                {tag.nodes.map((node) => (
                                  <div
                                    key={`${tag.tag}-${node.id}`}
                                    className={clsx(
                                      "flex items-center justify-between p-2 rounded-lg border transition-colors",
                                      tag.selected === node.id
                                        ? "border-blue-400 bg-blue-50"
                                        : "border-gray-200 bg-white",
                                      // Only select protocol groups are clickable in global mode
                                      tag.protocol === "select" && tag.selectable && node.available && node.selectable !== false
                                        ? "cursor-pointer hover:border-gray-300 hover:bg-gray-50"
                                        : "cursor-not-allowed opacity-60"
                                    )}
                                    onClick={() => {
                                      if (tag.protocol === "select" && tag.selectable && node.available && node.selectable !== false) {
                                        selectNodeForTag(tag.tag, node.id);
                                      }
                                    }}
                                  >
                                    <div className="flex items-center space-x-2">
                                      <div
                                        className={clsx(
                                          "w-2 h-2 rounded-full",
                                          node.available ? "bg-green-500" : "bg-red-500"
                                        )}
                                      />
                                      <div className="min-w-0">
                                        <h6 className="text-xs font-medium text-gray-900 truncate">{node.name}</h6>
                                        <p className="text-xs text-gray-500">{node.latency}ms</p>
                                      </div>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                      {tag.selected === node.id && (
                                        <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                                      )}
                                      {tag.protocol === "select" && (
                                        <button
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            if (tag.selectable && node.available && node.selectable !== false) {
                                              testNodeLatency(node.id);
                                            }
                                          }}
                                          disabled={testingLatency.has(node.id) || !tag.selectable || !node.available}
                                          className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                                        >
                                          <Zap className={clsx(
                                            "w-3 h-3",
                                            testingLatency.has(node.id) && "animate-pulse"
                                          )} />
                                        </button>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              // Rule Mode: Show proxy nodes first, then proxy groups
              <div className="space-y-4">
                {/* Rule Mode Info */}
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                  <p className="text-sm text-gray-600">
                    规则模式下，代理节点选择由规则自动决定。下方显示各代理节点和代理组的配置状态。
                  </p>
                </div>
                
                {/* Proxy Nodes in Rule Mode */}
                {proxyTags.filter(tag => !tag.has_sub_outbounds).length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700 flex items-center">
                      <span className="w-1 h-4 bg-gray-400 rounded-full mr-2"></span>
                      代理节点配置
                    </h4>
                    <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                      {proxyTags.filter(tag => !tag.has_sub_outbounds).map((tag) => (
                        <div
                          key={tag.tag}
                          className="flex flex-col items-center justify-center p-3 rounded-lg border bg-gray-50"
                        >
                          <div className="flex items-center space-x-1 mb-1">
                            <div className="w-2 h-2 rounded-full bg-gray-400" />
                            <span className="text-xs text-gray-500 bg-gray-100 px-1 py-0.5 rounded">
                              {tag.protocol}
                            </span>
                          </div>
                          <div className="text-center">
                            <h4 className="text-xs font-medium text-gray-900 truncate max-w-full">
                              {tag.tag}
                            </h4>
                            <span className="text-xs text-gray-500">只读</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Proxy Groups in Rule Mode */}
                {proxyTags.filter(tag => tag.has_sub_outbounds).length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700 flex items-center">
                      <span className="w-1 h-4 bg-gray-400 rounded-full mr-2"></span>
                      代理组配置
                    </h4>
                    {proxyTags.filter(tag => tag.has_sub_outbounds).map((tag) => (
                      <div key={tag.tag} className="border border-gray-200 rounded-lg overflow-hidden">
                        {/* Tag Header */}
                        <div
                          className="flex items-center justify-between p-3 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
                          onClick={() => toggleTagExpansion(tag.tag)}
                        >
                          <div className="flex items-center space-x-2">
                            {tag.has_sub_outbounds ? (
                              expandedTags.has(tag.tag) ? (
                                <ChevronDown className="w-3 h-3 text-gray-500" />
                              ) : (
                                <ChevronRight className="w-3 h-3 text-gray-500" />
                              )
                            ) : (
                              <div className="w-3 h-3" />
                            )}
                            <h4 className="text-sm font-medium text-gray-900">{tag.tag}</h4>
                            <span className={clsx(
                              "px-2 py-1 text-xs rounded-full",
                              tag.protocol === "select"
                                ? "bg-green-100 text-green-800"
                                : "bg-gray-100 text-gray-800"
                            )}>
                              {tag.protocol}
                            </span>
                            {tag.protocol !== "select" && (
                              <span className="text-xs text-gray-500 bg-orange-100 px-2 py-1 rounded">
                                只读
                              </span>
                            )}
                          </div>
                          <div className="text-xs text-gray-500">
                            {tag.has_sub_outbounds ? `${tag.nodes.length} 节点` : "单一出站"}
                            {tag.selected && tag.selected !== "N/A" && (
                              <span className="ml-2 text-blue-600 font-medium">• {tag.selected}</span>
                            )}
                          </div>
                        </div>

                        {/* Tag Nodes - only show if has sub_outbounds and expanded */}
                        {tag.has_sub_outbounds && expandedTags.has(tag.tag) && (
                          <div className="border-t border-gray-200 bg-gray-50">
                            <div className="p-3">
                              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                                {tag.nodes.map((node) => (
                                  <div
                                    key={`${tag.tag}-${node.id}`}
                                    className={clsx(
                                      "flex items-center justify-between p-2 rounded-lg border transition-colors",
                                      tag.selected === node.id
                                        ? "border-blue-400 bg-blue-50"
                                        : "border-gray-200 bg-white",
                                      // Only select protocol groups are clickable in rule mode
                                      tag.protocol === "select" && tag.selectable && node.available && node.selectable !== false
                                        ? "cursor-pointer hover:border-gray-300 hover:bg-gray-50"
                                        : "cursor-not-allowed opacity-60"
                                    )}
                                    onClick={() => {
                                      if (tag.protocol === "select" && tag.selectable && node.available && node.selectable !== false) {
                                        selectNodeForTag(tag.tag, node.id);
                                      }
                                    }}
                                  >
                                    <div className="flex items-center space-x-2">
                                      <div
                                        className={clsx(
                                          "w-2 h-2 rounded-full",
                                          node.available ? "bg-green-500" : "bg-red-500"
                                        )}
                                      />
                                      <div className="min-w-0">
                                        <h6 className="text-xs font-medium text-gray-900 truncate">{node.name}</h6>
                                        <p className="text-xs text-gray-500">{node.latency}ms</p>
                                      </div>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                      {tag.selected === node.id && (
                                        <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                                      )}
                                      {tag.protocol === "select" && (
                                        <button
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            if (tag.selectable && node.available && node.selectable !== false) {
                                              testNodeLatency(node.id);
                                            }
                                          }}
                                          disabled={testingLatency.has(node.id) || !tag.selectable || !node.available}
                                          className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                                        >
                                          <Zap className={clsx(
                                            "w-3 h-3",
                                            testingLatency.has(node.id) && "animate-pulse"
                                          )} />
                                        </button>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Show message if no sub_outbounds */}
                        {!tag.has_sub_outbounds && (
                          <div className="border-t border-gray-200 bg-gray-50">
                            <div className="p-3 text-xs text-gray-500 italic">
                              此代理组无子节点配置
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      )}

      {/* Legacy fallback for old node format */}
      {isConnected && proxyTags.length === 0 && nodes.length > 0 && (
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-6 py-3 border-b border-gray-200">
            <h3 className="text-base font-medium text-gray-900">
              Legacy Nodes
            </h3>
          </div>
          <div className="px-6 py-3">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {nodes.map((node) => (
                <div
                  key={node.id}
                  className={clsx(
                    "flex items-center justify-between p-3 rounded-lg border transition-colors cursor-pointer",
                    selectedNode === node.id
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-gray-300",
                    !node.available && "opacity-50 cursor-not-allowed"
                  )}
                  onClick={() => node.available && selectNode(node.id)}
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className={clsx(
                        "w-2 h-2 rounded-full",
                        node.available ? "bg-green-500" : "bg-red-500"
                      )}
                    />
                    <div className="min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">{node.name}</h4>
                      <p className="text-xs text-gray-500">{node.location}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-xs text-gray-600">
                      {node.latency}ms
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        testNodeLatency(node.id);
                      }}
                      disabled={testingLatency.has(node.id)}
                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                    >
                      <Zap className={clsx(
                        "w-3 h-3",
                        testingLatency.has(node.id) && "animate-pulse"
                      )} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Real-time Statistics Grid - Compact */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* Upload Statistics */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Upload className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="text-xs font-medium text-gray-500 truncate">
                    上传
                  </dt>
                  <dd className="text-sm font-medium text-gray-900">
                    {formatBytes(stats.bytesUploaded)}
                  </dd>
                  <dd className="text-xs text-blue-600">
                    {formatBytes(stats.uploadSpeed || 0)}/s
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Download Statistics */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Download className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="text-xs font-medium text-gray-500 truncate">
                    下载
                  </dt>
                  <dd className="text-sm font-medium text-gray-900">
                    {formatBytes(stats.bytesDownloaded)}
                  </dd>
                  <dd className="text-xs text-green-600">
                    {formatBytes(stats.downloadSpeed || 0)}/s
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Connection Time */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="text-xs font-medium text-gray-500 truncate">
                    运行时间
                  </dt>
                  <dd className="text-sm font-medium text-gray-900">
                    {formatTime(stats.connectionTime)}
                  </dd>
                  <dd className="text-xs text-purple-600">
                    {isConnected ? "已连接" : "离线"}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Connection Count */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Wifi className="h-5 w-5 text-orange-600" />
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="text-xs font-medium text-gray-500 truncate">
                    连接数
                  </dt>
                  <dd className="text-sm font-medium text-gray-900">
                    {stats.connections?.length || 0}
                  </dd>
                  <dd className="text-xs text-orange-600">
                    活跃
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Network Information */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Network Information</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <dt className="text-sm font-medium text-gray-500">Local IP</dt>
              <dd className="mt-1 text-sm text-gray-900">*************</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Public IP</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {isConnected ? "************" : "Not connected"}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">VPN Server</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {isConnected ? 
                  proxyMode === "global" 
                    ? globalSelectedOutbound
                    : // Try to find the node location from tag-based structure first
                      proxyTags.find(tag => tag.selected === selectedNode)?.nodes.find(n => n.id === selectedNode)?.location ||
                      // Fallback to legacy nodes structure
                      nodes.find(n => n.id === selectedNode)?.location ||
                      "Direct Connection"
                  : "Not connected"}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Protocol</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {isConnected ? "Shadowsocks/Trojan" : "N/A"}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Encryption</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {isConnected ? "ChaCha20Poly1305" : "N/A"}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Proxy Mode</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {proxyModes.find(m => m.id === proxyMode)?.name || "Global Mode"}
              </dd>
            </div>
          </div>
          
          {/* Connection History/Status */}
          {isConnected && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Connection Details</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div>Connected via: {
                  proxyMode === "global" 
                    ? globalSelectedOutbound
                    : // Try to find the node name from tag-based structure first
                      proxyTags.find(tag => tag.selected === selectedNode)?.nodes.find(n => n.id === selectedNode)?.name ||
                      // Fallback to legacy nodes structure
                      nodes.find(n => n.id === selectedNode)?.name ||
                      "Direct"
                }</div>
                <div>Current latency: {
                  proxyMode === "global" 
                    ? "N/A"
                    : // Try to find the node latency from tag-based structure first
                      proxyTags.find(tag => tag.selected === selectedNode)?.nodes.find(n => n.id === selectedNode)?.latency ||
                      // Fallback to legacy nodes structure
                      nodes.find(n => n.id === selectedNode)?.latency ||
                      0
                }ms</div>
                <div>Data encrypted: Yes</div>
                <div>DNS protected: Yes</div>
                <div>Proxy mode: {proxyModes.find(m => m.id === proxyMode)?.name || "Unknown"}</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;