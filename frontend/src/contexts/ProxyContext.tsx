import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from "react";
import { invoke } from "@tauri-apps/api/core";

interface ProxyStats {
  bytesUploaded: number;
  bytesDownloaded: number;
  uploadSpeed?: number;
  downloadSpeed?: number;
  connectionTime: number;
  uptime?: number;
  connections?: any[];
  isConnected: boolean;
}

interface ProxyContextType {
  isConnected: boolean;
  isConnecting: boolean;
  stats: ProxyStats;
  error: string | null;
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  getStatus: () => Promise<void>;
  updateConfig: (config: string) => Promise<boolean>;
  refreshNodes: () => Promise<void>;
  saveConfig: (config: string) => Promise<boolean>;
  loadConfig: () => Promise<string | null>;
}

const ProxyContext = createContext<ProxyContextType | undefined>(undefined);

export const useProxy = () => {
  const context = useContext(ProxyContext);
  if (context === undefined) {
    throw new Error("useProxy must be used within a ProxyProvider");
  }
  return context;
};

interface ProxyProviderProps {
  children: ReactNode;
}

export const ProxyProvider: React.FC<ProxyProviderProps> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [stats, setStats] = useState<ProxyStats>({
    bytesUploaded: 0,
    bytesDownloaded: 0,
    connectionTime: 0,
    isConnected: false,
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Initialize proxy status on component mount
    getStatus();
    
    // Set up periodic status updates
    const interval = setInterval(getStatus, 2000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatus = async () => {
    try {
      const status = await invoke<string>("get_proxy_status");
      const isConnectedNow = status === "connected";
      setIsConnected(isConnectedNow);
      
      if (isConnectedNow) {
        const statsData = await invoke<any>("get_proxy_stats");
        setStats({
          bytesUploaded: statsData.upload_bytes || 0,
          bytesDownloaded: statsData.download_bytes || 0,
          uploadSpeed: statsData.upload_speed || 0,
          downloadSpeed: statsData.download_speed || 0,
          connectionTime: statsData.uptime || 0,
          uptime: statsData.uptime || 0,
          connections: statsData.connections || [],
          isConnected: true,
        });
      } else {
        setStats(prev => ({ ...prev, isConnected: false }));
      }
      
      setError(null);
    } catch (err) {
      console.error("Failed to get proxy status:", err);
      setError("Failed to get proxy status");
    }
  };

  const connect = async () => {
    if (isConnecting || isConnected) return;
    
    setIsConnecting(true);
    setError(null);
    
    try {
      await invoke<string>("toggle_proxy", { enabled: true });
      setIsConnected(true);
      
      // Auto-refresh nodes after successful connection
      setTimeout(() => {
        refreshNodes();
      }, 2000); // Wait 2 seconds for proxy to fully initialize
    } catch (err) {
      console.error("Failed to connect:", err);
      setError("Failed to connect to VPN");
      setIsConnected(false);
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnect = async () => {
    if (isConnecting || !isConnected) return;
    
    setIsConnecting(true);
    setError(null);
    
    try {
      await invoke<string>("toggle_proxy", { enabled: false });
      setIsConnected(false);
      setStats(prev => ({ ...prev, isConnected: false }));
    } catch (err) {
      console.error("Failed to disconnect:", err);
      setError("Failed to disconnect from VPN");
    } finally {
      setIsConnecting(false);
    }
  };

  const updateConfig = async (config: string): Promise<boolean> => {
    try {
      await invoke<string>("update_config", { config });
      return true;
    } catch (err) {
      console.error("Failed to update config:", err);
      setError("Failed to update VPN configuration");
      return false;
    }
  };

  const refreshNodes = async () => {
    try {
      // Emit a custom event to trigger node refresh in Dashboard
      window.dispatchEvent(new CustomEvent('refreshNodes'));
    } catch (err) {
      console.error("Failed to refresh nodes:", err);
    }
  };

  const saveConfig = useCallback(async (config: string): Promise<boolean> => {
    try {
      await invoke<string>("save_config", { config });
      return true;
    } catch (err) {
      console.error("Failed to save config:", err);
      setError("Failed to save VPN configuration");
      return false;
    }
  }, []);

  const loadConfig = useCallback(async (): Promise<string | null> => {
    try {
      const config = await invoke<string | null>("load_config");
      return config;
    } catch (err) {
      console.error("Failed to load config:", err);
      return null;
    }
  }, []);

  const value: ProxyContextType = {
    isConnected,
    isConnecting,
    stats,
    error,
    connect,
    disconnect,
    getStatus,
    updateConfig,
    refreshNodes,
    saveConfig,
    loadConfig,
  };

  return <ProxyContext.Provider value={value}>{children}</ProxyContext.Provider>;
};