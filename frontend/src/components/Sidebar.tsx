import React from "react";
import { NavLink } from "react-router-dom";
import { Shield, Settings, FileText, Wifi } from "lucide-react";
import { useProxy } from "../contexts/ProxyContext";
import clsx from "clsx";

const Sidebar: React.FC = () => {
  const { isConnected } = useProxy();

  const navigation = [
    { name: "Dashboard", href: "/", icon: Shield },
    { name: "Setting<PERSON>", href: "/settings", icon: Settings },
    { name: "Logs", href: "/logs", icon: FileText },
  ];

  return (
    <div className="flex flex-col w-64 bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Shield className="h-8 w-8 text-primary-600" />
          <span className="text-xl font-bold text-gray-900">VPN Client</span>
        </div>
      </div>

      {/* Connection Status */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div
            className={clsx(
              "w-3 h-3 rounded-full",
              isConnected ? "bg-green-500 pulse-green" : "bg-red-500 pulse-red"
            )}
          />
          <span className="text-sm font-medium text-gray-900">
            {isConnected ? "Connected" : "Disconnected"}
          </span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1">
        {navigation.map((item) => (
          <NavLink
            key={item.name}
            to={item.href}
            className={({ isActive }) =>
              clsx(
                "group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors",
                isActive
                  ? "bg-primary-100 text-primary-900"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              )
            }
          >
            <item.icon
              className={clsx(
                "mr-3 h-5 w-5 flex-shrink-0",
                "text-gray-400 group-hover:text-gray-500"
              )}
            />
            {item.name}
          </NavLink>
        ))}
      </nav>

      {/* Network Status */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <Wifi className="h-4 w-4" />
          <span>Network Status: Online</span>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;