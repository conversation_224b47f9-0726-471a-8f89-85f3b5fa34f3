{"name": "vpn-client-frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@tauri-apps/api": ">=2.0.0-alpha.0", "@tauri-apps/plugin-shell": ">=2.0.0-alpha.0", "lucide-react": "^0.263.1", "clsx": "^1.2.1"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "typescript": "^5.0.2", "vite": "^4.4.4"}}