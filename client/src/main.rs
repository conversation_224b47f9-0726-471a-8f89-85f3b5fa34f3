// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use tauri::{Manager, State};
use tokio::sync::Mutex;

mod auth_manager;
mod log_manager;
mod proxy_manager;
use auth_manager::{AuthData, AuthManager, User};
use log_manager::{LogEntry, LogManager};
use proxy_manager::ProxyManager;

struct AppState {
    proxy_manager: Arc<Mutex<ProxyManager>>,
    auth_manager: Arc<AuthManager>,
    log_manager: Arc<LogManager>,
}

fn main() {
    #[cfg(not(target_os = "android"))]
    if !check_sudo() {
        use std::process;
        process::exit(0);
    }
    let proxy_manager = Arc::new(Mutex::new(ProxyManager::new()));
    let auth_manager = Arc::new(AuthManager::new("http://127.0.0.1:3000".to_string()));
    let log_manager = Arc::new(LogManager::new(1000)); // Store up to 1000 log entries

    // Global state to track if we're in the process of closing
    let is_closing = Arc::new(AtomicBool::new(false));

    // Initialize logging with log collection
    if let Err(e) = log_manager::init_logging_with_collection(&log_manager) {
        eprintln!("Failed to initialize logging: {}", e);
    }

    tauri::Builder::default()
        .manage(AppState {
            proxy_manager: proxy_manager.clone(),
            auth_manager,
            log_manager,
        })
        .setup(|app| {
            // app.get_webview_window("main").unwrap().open_devtools();
            // Load saved authentication data on startup
            let app_handle = app.handle().clone();
            let auth_manager = app.state::<AppState>().auth_manager.clone();
            let proxy_manager = app.state::<AppState>().proxy_manager.clone();

            tauri::async_runtime::spawn(async move {
                if let Err(e) = auth_manager.load_from_secure_storage(&app_handle).await {
                    eprintln!("Failed to load authentication data: {}", e);
                }
            });

            // Load saved proxy configuration on startup
            let app_handle_proxy = app.handle().clone();
            let proxy_manager_load = proxy_manager.clone();
            tauri::async_runtime::spawn(async move {
                if let Ok(Some(config)) = load_config_internal(app_handle_proxy).await {
                    let mut proxy_manager = proxy_manager_load.lock().await;
                    if let Err(e) = proxy_manager.update_config(config).await {
                        eprintln!("Failed to load saved proxy configuration: {}", e);
                    } else {
                        println!("Saved proxy configuration loaded successfully");
                    }
                }
            });

            Ok(())
        })
        .on_window_event({
            let is_closing = is_closing.clone();
            move |window, event| {
                match event {
                    tauri::WindowEvent::CloseRequested { api, .. } => {
                        // Check if we're already in the process of closing
                        if is_closing.swap(true, Ordering::SeqCst) {
                            // Already closing, just allow it to close
                            return;
                        }

                        // Prevent immediate close and handle cleanup
                        api.prevent_close();

                        let app_handle = window.app_handle();
                        let proxy_manager = app_handle.state::<AppState>().proxy_manager.clone();
                        let window_clone = window.clone();

                        tauri::async_runtime::spawn(async move {
                            // Ensure proxy is stopped before closing
                            {
                                let mut pm = proxy_manager.lock().await;
                                if let Err(e) = pm.stop().await {
                                    eprintln!("Error stopping proxy during window close: {}", e);
                                } else {
                                    println!("Proxy stopped successfully during app exit");
                                }
                            }

                            // Use destroy instead of close to avoid retriggering CloseRequested
                            if let Err(e) = window_clone.destroy() {
                                eprintln!("Error destroying window: {}", e);
                            }
                        });
                    }
                    _ => {}
                }
            }
        })
        .invoke_handler(tauri::generate_handler![
            greet,
            get_proxy_status,
            toggle_proxy,
            get_proxy_stats,
            update_config,
            auth_login,
            auth_logout,
            auth_get_current_user,
            auth_is_authenticated,
            auth_refresh_token,
            get_vpn_config,
            set_vpn_config,
            get_proxy_nodes,
            select_proxy_node,
            select_proxy_node_for_tag,
            get_selected_proxy_node,
            get_logs,
            clear_logs,
            save_config,
            load_config,
            set_global_target,
            get_global_target
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn get_proxy_status(state: State<'_, AppState>) -> Result<String, String> {
    let proxy_manager = state.proxy_manager.lock().await;
    Ok(proxy_manager.get_status())
}

#[tauri::command]
async fn toggle_proxy(enabled: bool, state: State<'_, AppState>) -> Result<String, String> {
    let mut proxy_manager = state.proxy_manager.lock().await;

    if enabled {
        proxy_manager.start().await?;
    } else {
        proxy_manager.stop().await?;
    }

    Ok(proxy_manager.get_status())
}

#[tauri::command]
async fn get_proxy_stats(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    let proxy_manager = state.proxy_manager.lock().await;
    Ok(proxy_manager.get_detailed_stats().await)
}

#[tauri::command]
async fn update_config(config: String, state: State<'_, AppState>) -> Result<String, String> {
    let mut proxy_manager = state.proxy_manager.lock().await;
    proxy_manager.update_config(config).await
}

// Authentication commands
#[tauri::command]
async fn auth_login(
    username: String,
    password: String,
    state: State<'_, AppState>,
    _app_handle: tauri::AppHandle,
) -> Result<AuthData, String> {
    let auth_manager = &state.auth_manager;

    match auth_manager.login(username, password).await {
        Ok(auth_data) => {
            // Save authentication data securely
            if let Err(e) = auth_manager.save_to_secure_storage(&_app_handle).await {
                eprintln!("Failed to save authentication data: {}", e);
            }
            Ok(auth_data)
        }
        Err(e) => Err(format!("Login failed: {}", e)),
    }
}

#[tauri::command]
async fn auth_logout(
    state: State<'_, AppState>,
    _app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let auth_manager = &state.auth_manager;

    match auth_manager.logout().await {
        Ok(()) => {
            // Clear stored authentication data
            if let Err(e) = auth_manager.clear_secure_storage().await {
                eprintln!("Failed to clear authentication data: {}", e);
            }
            Ok(())
        }
        Err(e) => Err(format!("Logout failed: {}", e)),
    }
}

#[tauri::command]
async fn auth_get_current_user(state: State<'_, AppState>) -> Result<Option<User>, String> {
    let auth_manager = &state.auth_manager;
    Ok(auth_manager.get_current_user().await)
}

#[tauri::command]
async fn auth_is_authenticated(state: State<'_, AppState>) -> Result<bool, String> {
    let auth_manager = &state.auth_manager;
    Ok(auth_manager.is_authenticated().await)
}

#[tauri::command]
async fn auth_refresh_token(
    state: State<'_, AppState>,
    _app_handle: tauri::AppHandle,
) -> Result<AuthData, String> {
    let auth_manager = &state.auth_manager;

    match auth_manager.refresh_token().await {
        Ok(auth_data) => {
            // Save updated authentication data
            if let Err(e) = auth_manager.save_to_secure_storage(&_app_handle).await {
                eprintln!("Failed to save refreshed authentication data: {}", e);
            }
            Ok(auth_data)
        }
        Err(e) => Err(format!("Token refresh failed: {}", e)),
    }
}

// VPN Configuration Management Commands
#[tauri::command]
async fn get_vpn_config(
    state: State<'_, AppState>,
    _app_handle: tauri::AppHandle,
) -> Result<Option<String>, String> {
    let auth_manager = &state.auth_manager;

    // Check if user is authenticated
    if !auth_manager.is_authenticated().await {
        return Err("User not authenticated".to_string());
    }

    // Get user's VPN configuration from backend
    if let Some(token) = auth_manager.get_access_token().await {
        let client = reqwest::Client::new();
        let response = client
            .get("http://127.0.0.1:3000/api/user/config")
            .header("Authorization", format!("Bearer {}", token))
            .send()
            .await
            .map_err(|e| format!("Failed to fetch config: {}", e))?;

        if response.status().is_success() {
            let config_text = response
                .text()
                .await
                .map_err(|e| format!("Failed to read config: {}", e))?;
            Ok(Some(config_text))
        } else {
            Err(format!("Failed to get config: {}", response.status()))
        }
    } else {
        Err("No access token available".to_string())
    }
}

#[tauri::command]
async fn set_vpn_config(config: String, state: State<'_, AppState>) -> Result<String, String> {
    let mut proxy_manager = state.proxy_manager.lock().await;
    proxy_manager.update_config(config).await
}

#[cfg(not(target_os = "android"))]
fn check_sudo() -> bool {
    use std::env::current_exe;
    let is_elevated = privilege::user::privileged();
    if !is_elevated {
        let Ok(exe) = current_exe() else {
            return true;
        };
        let mut elevated_cmd = privilege::runas::Command::new(exe);
        let _ = elevated_cmd.force_prompt(true).hide(true).gui(true).run();
    }
    is_elevated
}

// Proxy Node Management Commands
#[tauri::command]
async fn get_proxy_nodes(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    let auth_manager = &state.auth_manager;

    // Check if user is authenticated
    if !auth_manager.is_authenticated().await {
        return Err("User not authenticated".to_string());
    }

    // Get proxy manager for accessing leaf API
    let proxy_manager = &state.proxy_manager.lock().await;

    // Get all outbounds from new API
    let all_outbounds_result = proxy_manager.get_all_outbound_nodes().await;

    let mut tags = Vec::new();

    match all_outbounds_result {
        Ok(data) => {
            // Debug: Log the raw data from leaf API
            println!("=== Debug: Raw leaf API data ===");
            println!("All outbounds data: {:?}", data);

            if let Some(outbounds) = data.outbounds {
                println!("Total outbounds count: {}", outbounds.len());
                for (i, outbound) in outbounds.iter().enumerate() {
                    println!(
                        "Outbound {}: tag={}, protocol={}, sub_outbounds={:?}",
                        i + 1,
                        outbound.tag,
                        outbound.protocol,
                        outbound.sub_outbounds_tag
                    );

                    if outbound.tag == "Proxy" {
                        println!("=== Proxy group raw data ===");
                        tracing::info!("Proxy sub_outbounds_tag: {:?}", outbound.sub_outbounds_tag);
                        println!("Expected: Should be [\"FailOver\", \"direct\"] or similar");
                    }
                }
                
                for outbound in outbounds {
                    // Get currently selected node for this outbound
                    let mut selected_sub_outbound = None;
                    if let Ok(Some(selected)) = proxy_manager.get_selected_node(&outbound.tag).await
                    {
                        selected_sub_outbound = Some(selected);
                    }

                    // Create tag structure for UI
                    let tag_info = if outbound.protocol == "select" {
                        // For select protocol, show sub_outbounds as selectable nodes
                        let nodes = if let Some(sub_outbounds) = &outbound.sub_outbounds_tag {
                            sub_outbounds
                                .iter()
                                .map(|node| {
                                    serde_json::json!({
                                        "id": node,
                                        "name": node,
                                        "location": "Unknown",
                                        "latency": 0,
                                        "available": true,
                                        "selectable": true
                                    })
                                })
                                .collect::<Vec<_>>()
                        } else {
                            vec![]
                        };

                        serde_json::json!({
                            "tag": outbound.tag,
                            "protocol": outbound.protocol,
                            "nodes": nodes,
                            "selected": selected_sub_outbound.unwrap_or_else(|| "direct".to_string()),
                            "selectable": true,
                            "has_sub_outbounds": outbound.sub_outbounds_tag.is_some()
                        })
                    } else {
                        // For non-select protocols, show as non-selectable
                        let nodes = if let Some(sub_outbounds) = &outbound.sub_outbounds_tag {
                            sub_outbounds
                                .iter()
                                .map(|node| {
                                    serde_json::json!({
                                        "id": node,
                                        "name": node,
                                        "location": "Unknown",
                                        "latency": 0,
                                        "available": true,
                                        "selectable": false
                                    })
                                })
                                .collect::<Vec<_>>()
                        } else {
                            vec![]
                        };

                        serde_json::json!({
                            "tag": outbound.tag,
                            "protocol": outbound.protocol,
                            "nodes": nodes,
                            "selected": "N/A",
                            "selectable": false,
                            "has_sub_outbounds": outbound.sub_outbounds_tag.is_some()
                        })
                    };

                    tags.push(tag_info);
                }
            }
        }
        Err(_) => {
            // Fallback to mock data if API not available
            tags = vec![serde_json::json!({
                "tag": "Proxy",
                "protocol": "select",
                "nodes": [
                    {
                        "id": "direct",
                        "name": "direct",
                        "location": "Unknown",
                        "latency": 0,
                        "available": true,
                        "selectable": true
                    },
                    {
                        "id": "fallback-node",
                        "name": "fallback-node",
                        "location": "Unknown",
                        "latency": 0,
                        "available": true,
                        "selectable": true
                    }
                ],
                "selected": "direct",
                "selectable": true,
                "has_sub_outbounds": true
            })];
        }
    }

    let response = serde_json::json!({
        "tags": tags,
    });

    Ok(response)
}

#[tauri::command]
async fn select_proxy_node(node_id: String, state: State<'_, AppState>) -> Result<String, String> {
    let proxy_manager = state.proxy_manager.lock().await;

    // Try to set selected node for all common outbound names
    let common_outbounds = vec!["Proxy", "PROXY", "proxy", "Select", "SELECT", "select"];
    let mut success = false;
    let mut last_error = String::new();

    for outbound in common_outbounds {
        match proxy_manager.set_selected_node(outbound, &node_id).await {
            Ok(()) => {
                success = true;
                break;
            }
            Err(e) => {
                last_error = e;
                // Continue trying other outbound names
            }
        }
    }

    if success {
        Ok(format!("Selected node: {}", node_id))
    } else {
        Err(format!("Failed to select node: {}", last_error))
    }
}

#[tauri::command]
async fn select_proxy_node_for_tag(
    tag: String,
    node_id: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let proxy_manager = state.proxy_manager.lock().await;

    match proxy_manager.set_selected_node(&tag, &node_id).await {
        Ok(()) => Ok(format!("Selected node '{}' for tag '{}'", node_id, tag)),
        Err(e) => Err(format!(
            "Failed to select node '{}' for tag '{}': {}",
            node_id, tag, e
        )),
    }
}

#[tauri::command]
async fn get_selected_proxy_node(state: State<'_, AppState>) -> Result<String, String> {
    let proxy_manager = state.proxy_manager.lock().await;

    // Try to get currently selected node from all common outbound names
    let common_outbounds = vec!["Proxy", "PROXY", "proxy", "Select", "SELECT", "select"];

    for outbound in common_outbounds {
        match proxy_manager.get_selected_node(outbound).await {
            Ok(Some(node)) => return Ok(node),
            Ok(None) => continue, // Try next outbound
            Err(_) => continue,   // Try next outbound
        }
    }

    // If no selected node found, return default
    Ok("direct".to_string())
}

// Log management commands
#[tauri::command]
async fn get_logs(
    level_filter: Option<String>,
    limit: Option<usize>,
    state: State<'_, AppState>,
) -> Result<Vec<LogEntry>, String> {
    let log_manager = &state.log_manager;

    let level_filter_ref = level_filter.as_deref();
    let logs = log_manager.get_logs_filtered(level_filter_ref, limit);

    Ok(logs)
}

#[tauri::command]
fn clear_logs(state: State<'_, AppState>) -> Result<String, String> {
    let log_manager = &state.log_manager;
    log_manager.clear_logs();
    Ok("Logs cleared successfully".to_string())
}

// Internal function to load config (shared between startup and command)
async fn load_config_internal(app_handle: tauri::AppHandle) -> Result<Option<String>, String> {
    use base64::{engine::general_purpose, Engine};
    use std::fs;

    // Get app data directory
    let app_data_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data directory: {}", e))?;

    let config_file = app_data_dir.join("vpn_config").join("config.dat");

    if !config_file.exists() {
        return Ok(None);
    }

    // Read and decode config file
    let encoded_config = fs::read_to_string(&config_file)
        .map_err(|e| format!("Failed to read config file: {}", e))?;

    let decoded_config = general_purpose::STANDARD
        .decode(&encoded_config)
        .map_err(|e| format!("Failed to decode config: {}", e))?;

    let config_string = String::from_utf8(decoded_config)
        .map_err(|e| format!("Failed to convert config to string: {}", e))?;

    Ok(Some(config_string))
}

// Configuration storage commands
#[tauri::command]
async fn save_config(config: String, app_handle: tauri::AppHandle) -> Result<String, String> {
    use base64::{engine::general_purpose, Engine};
    use std::fs;

    // Get app data directory
    let app_data_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data directory: {}", e))?;

    // Create config directory if it doesn't exist
    let config_dir = app_data_dir.join("vpn_config");
    if !config_dir.exists() {
        fs::create_dir_all(&config_dir)
            .map_err(|e| format!("Failed to create config directory: {}", e))?;
    }

    // Save config file with base64 encoding for basic obfuscation
    let config_file = config_dir.join("config.dat");
    let encoded_config = general_purpose::STANDARD.encode(config.as_bytes());

    fs::write(&config_file, encoded_config)
        .map_err(|e| format!("Failed to save config file: {}", e))?;

    Ok("Configuration saved successfully".to_string())
}

#[tauri::command]
async fn load_config(app_handle: tauri::AppHandle) -> Result<Option<String>, String> {
    load_config_internal(app_handle).await
}

// Global target management commands
#[tauri::command]
async fn set_global_target(
    target: Option<String>,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let proxy_manager = state.proxy_manager.lock().await;

    match proxy_manager.set_global_target(target.clone()).await {
        Ok(()) => {
            let message = if let Some(target) = target {
                format!("Global target set to: {}", target)
            } else {
                "Global mode disabled".to_string()
            };
            Ok(message)
        }
        Err(e) => Err(format!("Failed to set global target: {}", e)),
    }
}

#[tauri::command]
async fn get_global_target(state: State<'_, AppState>) -> Result<Option<String>, String> {
    let proxy_manager = state.proxy_manager.lock().await;

    match proxy_manager.get_global_target().await {
        Ok(target) => Ok(target),
        Err(e) => Err(format!("Failed to get global target: {}", e)),
    }
}
