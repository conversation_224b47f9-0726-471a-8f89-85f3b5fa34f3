use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mute<PERSON>;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};
use tauri::AppHandle;
use anyhow::{Result, anyhow};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub username: String,
    pub email: String,
    pub role: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TokenInfo {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_at: DateTime<Utc>,
    pub refresh_expires_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuthData {
    pub user: User,
    pub token_info: TokenInfo,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub user: User,
}

pub struct AuthManager {
    auth_data: Arc<Mutex<Option<AuthData>>>,
    http_client: reqwest::Client,
    api_base_url: String,
}

impl AuthManager {
    pub fn new(api_base_url: String) -> Self {
        let http_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .unwrap();

        Self {
            auth_data: Arc::new(Mutex::new(None)),
            http_client,
            api_base_url,
        }
    }

    pub async fn login(&self, username: String, password: String) -> Result<AuthData> {
        let login_request = LoginRequest { username, password };
        
        println!("Attempting login to: {}/api/auth/login", self.api_base_url);
        println!("Login request: {:?}", login_request);
        
        let response = self.http_client
            .post(&format!("{}/api/auth/login", self.api_base_url))
            .json(&login_request)
            .send()
            .await?;

        let status = response.status();
        println!("Response status: {}", status);
        
        if !status.is_success() {
            let error_text = response.text().await?;
            println!("Error response: {}", error_text);
            return Err(anyhow!("Login failed: {} - {}", status, error_text));
        }

        let login_response: serde_json::Value = response.json().await?;
        println!("Login response: {}", serde_json::to_string_pretty(&login_response)?);
        
        // Extract data from the response (backend returns top-level structure)
        let access_token = login_response.get("access_token")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing access_token"))?
            .to_string();

        let refresh_token = login_response.get("refresh_token")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing refresh_token"))?
            .to_string();

        let user_id = login_response.get("user_id")
            .and_then(|v| v.as_str())
            .unwrap_or("unknown")
            .to_string();

        let username = login_response.get("username")
            .and_then(|v| v.as_str())
            .unwrap_or("unknown")
            .to_string();

        let user = User {
            id: user_id,
            username,
            email: "".to_string(), // Backend doesn't return email in login response
            role: "user".to_string(), // Default role, could be enhanced later
        };

        // Token expires in 1 hour, refresh token expires in 7 days
        let expires_at = Utc::now() + Duration::hours(1);
        let refresh_expires_at = Utc::now() + Duration::days(7);

        let token_info = TokenInfo {
            access_token,
            refresh_token,
            expires_at,
            refresh_expires_at,
        };

        let auth_data = AuthData {
            user,
            token_info,
        };

        // Store authentication data
        let mut stored_auth = self.auth_data.lock().await;
        *stored_auth = Some(auth_data.clone());

        Ok(auth_data)
    }

    pub async fn logout(&self) -> Result<()> {
        let mut stored_auth = self.auth_data.lock().await;
        *stored_auth = None;
        Ok(())
    }

    pub async fn get_current_user(&self) -> Option<User> {
        let auth_data = self.auth_data.lock().await;
        auth_data.as_ref().map(|data| data.user.clone())
    }

    pub async fn get_access_token(&self) -> Option<String> {
        let auth_data = self.auth_data.lock().await;
        auth_data.as_ref().map(|data| data.token_info.access_token.clone())
    }

    pub async fn is_authenticated(&self) -> bool {
        let auth_data = self.auth_data.lock().await;
        match auth_data.as_ref() {
            Some(data) => Utc::now() < data.token_info.expires_at,
            None => false,
        }
    }

    pub async fn refresh_token(&self) -> Result<AuthData> {
        let refresh_token = {
            let auth_data = self.auth_data.lock().await;
            match auth_data.as_ref() {
                Some(data) => {
                    if Utc::now() >= data.token_info.refresh_expires_at {
                        return Err(anyhow!("Refresh token expired"));
                    }
                    data.token_info.refresh_token.clone()
                }
                None => return Err(anyhow!("No refresh token available")),
            }
        };

        let mut payload = HashMap::new();
        payload.insert("refresh_token", refresh_token);

        let response = self.http_client
            .post(&format!("{}/api/auth/refresh", self.api_base_url))
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(anyhow!("Token refresh failed: {}", response.status()));
        }

        let refresh_response: serde_json::Value = response.json().await?;
        
        let data = refresh_response.get("data")
            .ok_or_else(|| anyhow!("Invalid refresh response format"))?;

        let access_token = data.get("access_token")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing access_token in refresh response"))?
            .to_string();

        let refresh_token = data.get("refresh_token")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing refresh_token in refresh response"))?
            .to_string();

        // Update token info
        let mut stored_auth = self.auth_data.lock().await;
        if let Some(auth_data) = stored_auth.as_mut() {
            auth_data.token_info.access_token = access_token;
            auth_data.token_info.refresh_token = refresh_token;
            auth_data.token_info.expires_at = Utc::now() + Duration::hours(1);
            auth_data.token_info.refresh_expires_at = Utc::now() + Duration::days(7);
            
            return Ok(auth_data.clone());
        }

        Err(anyhow!("No authentication data to refresh"))
    }

    pub async fn ensure_valid_token(&self) -> Result<String> {
        let auth_data = self.auth_data.lock().await;
        
        if let Some(data) = auth_data.as_ref() {
            // Check if token is still valid (with 5-minute buffer)
            if Utc::now() + Duration::minutes(5) < data.token_info.expires_at {
                return Ok(data.token_info.access_token.clone());
            }
        }
        
        // Release the lock before refreshing
        drop(auth_data);
        
        // Token is expired or close to expiry, refresh it
        let refreshed_data = self.refresh_token().await?;
        Ok(refreshed_data.token_info.access_token)
    }

    pub async fn save_to_secure_storage(&self, _app_handle: &AppHandle) -> Result<()> {
        let auth_data = self.auth_data.lock().await;
        
        if let Some(data) = auth_data.as_ref() {
            let auth_json = serde_json::to_string(data)?;
            
            // For now, we'll use a simple file storage
            // In production, this should use platform-specific secure storage like keychain/credential manager
            let auth_file_path = format!("{}/.vpn_auth", std::env::var("HOME").unwrap_or_default());
            
            tokio::fs::write(auth_file_path, auth_json).await?;
        }
        
        Ok(())
    }

    pub async fn load_from_secure_storage(&self, _app_handle: &AppHandle) -> Result<()> {
        let auth_file_path = format!("{}/.vpn_auth", std::env::var("HOME").unwrap_or_default());
        
        if let Ok(auth_json) = tokio::fs::read_to_string(&auth_file_path).await {
            let auth_data: AuthData = serde_json::from_str(&auth_json)?;
            
            // Check if tokens are still valid
            if Utc::now() < auth_data.token_info.refresh_expires_at {
                let mut stored_auth = self.auth_data.lock().await;
                *stored_auth = Some(auth_data);
            } else {
                // Clean up expired data
                let _ = tokio::fs::remove_file(&auth_file_path).await;
            }
        }
        
        Ok(())
    }

    pub async fn clear_secure_storage(&self) -> Result<()> {
        let auth_file_path = format!("{}/.vpn_auth", std::env::var("HOME").unwrap_or_default());
        let _ = tokio::fs::remove_file(&auth_file_path).await;
        Ok(())
    }
}