use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::sync::Arc;
use std::sync::RwLock;
use tracing::{Level, Subscriber};
use tracing_subscriber::{layer::SubscriberExt, Layer};
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LogEntry {
    pub id: String,
    pub timestamp: DateTime<Utc>,
    pub level: String,
    pub message: String,
    pub details: Option<String>,
    pub target: Option<String>,
}

impl LogEntry {
    pub fn new(
        level: Level,
        message: String,
        target: Option<String>,
        details: Option<String>,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            level: level.to_string().to_lowercase(),
            message,
            details,
            target,
        }
    }
}

#[derive(Debug)]
pub struct LogManager {
    logs: Arc<RwLock<VecDeque<LogEntry>>>,
    max_logs: usize,
}

impl LogManager {
    pub fn new(max_logs: usize) -> Self {
        Self {
            logs: Arc::new(RwLock::new(VecDeque::with_capacity(max_logs))),
            max_logs,
        }
    }

    pub fn add_log(&self, entry: LogEntry) {
        let mut logs = self.logs.write().unwrap();

        // Remove oldest log if we've reached the limit
        if logs.len() >= self.max_logs {
            logs.pop_front();
        }

        logs.push_back(entry);
    }

    pub fn get_logs(&self) -> Vec<LogEntry> {
        let logs = self.logs.read().unwrap();
        logs.iter().cloned().collect()
    }

    pub fn get_logs_filtered(
        &self,
        level_filter: Option<&str>,
        limit: Option<usize>,
    ) -> Vec<LogEntry> {
        let logs = self.logs.read().unwrap();
        let mut filtered_logs: Vec<LogEntry> = logs
            .iter()
            .filter(|log| {
                if let Some(filter) = level_filter {
                    log.level == filter
                } else {
                    true
                }
            })
            .cloned()
            .collect();

        // Sort by timestamp (newest first)
        filtered_logs.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));

        if let Some(limit) = limit {
            filtered_logs.truncate(limit);
        }

        filtered_logs
    }

    pub fn clear_logs(&self) {
        let mut logs = self.logs.write().unwrap();
        logs.clear();
    }

    pub fn get_log_count(&self) -> usize {
        let logs = self.logs.read().unwrap();
        logs.len()
    }

    pub fn get_collector_layer(&self) -> LogCollectorLayer {
        LogCollectorLayer {
            log_manager: self.logs.clone(),
            max_logs: self.max_logs,
        }
    }
}

// Custom tracing layer to collect logs
pub struct LogCollectorLayer {
    log_manager: Arc<RwLock<VecDeque<LogEntry>>>,
    max_logs: usize,
}

impl<S> Layer<S> for LogCollectorLayer
where
    S: Subscriber,
{
    fn on_event(
        &self,
        event: &tracing::Event<'_>,
        _ctx: tracing_subscriber::layer::Context<'_, S>,
    ) {
        let metadata = event.metadata();
        let level = *metadata.level();
        let target = metadata.target().to_string();

        // Extract the message from the event
        let mut visitor = MessageVisitor::new();
        event.record(&mut visitor);

        let log_entry = LogEntry::new(level, visitor.message, Some(target), visitor.details);

        // Spawn a task to add the log entry asynchronously
        let log_manager = self.log_manager.clone();
        let max_logs = self.max_logs;

        let mut logs = log_manager.write().unwrap();

        // Remove oldest log if we've reached the limit
        if logs.len() >= max_logs {
            logs.pop_front();
        }

        logs.push_back(log_entry);
    }
}

// Visitor to extract message and fields from tracing events
struct MessageVisitor {
    message: String,
    details: Option<String>,
}

impl MessageVisitor {
    fn new() -> Self {
        Self {
            message: String::new(),
            details: None,
        }
    }
}

impl tracing::field::Visit for MessageVisitor {
    fn record_debug(&mut self, field: &tracing::field::Field, value: &dyn std::fmt::Debug) {
        if field.name() == "message" {
            self.message = format!("{:?}", value);
            // Remove quotes from debug formatting
            if self.message.starts_with('"') && self.message.ends_with('"') {
                self.message = self.message[1..self.message.len() - 1].to_string();
            }
        } else {
            // Collect other fields as details
            let field_value = format!("{}: {:?}", field.name(), value);
            if let Some(ref mut details) = self.details {
                details.push_str(&format!(", {}", field_value));
            } else {
                self.details = Some(field_value);
            }
        }
    }

    fn record_str(&mut self, field: &tracing::field::Field, value: &str) {
        if field.name() == "message" {
            self.message = value.to_string();
        } else {
            // Collect other fields as details
            let field_value = format!("{}: {}", field.name(), value);
            if let Some(ref mut details) = self.details {
                details.push_str(&format!(", {}", field_value));
            } else {
                self.details = Some(field_value);
            }
        }
    }
}

// Helper function to initialize logging with log collection
pub fn init_logging_with_collection(
    log_manager: &LogManager,
) -> Result<(), Box<dyn std::error::Error>> {
    use tracing_subscriber::{fmt, EnvFilter};

    let collector_layer = log_manager.get_collector_layer();

    let subscriber = tracing_subscriber::registry()
        .with(EnvFilter::from_default_env().add_directive(tracing::Level::INFO.into()))
        .with(fmt::layer().with_target(false))
        .with(collector_layer);

    // Try to set global default, but don't fail if one is already set
    match tracing::subscriber::set_global_default(subscriber) {
        Ok(()) => {
            tracing::info!("Log collection initialized successfully");
        }
        Err(_) => {
            // Global subscriber already set, this is fine
            tracing::warn!(
                "Global tracing subscriber already set, log collection may not work properly"
            );
        }
    }

    Ok(())
}
