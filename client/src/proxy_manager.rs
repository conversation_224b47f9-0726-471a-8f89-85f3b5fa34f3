use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::sync::atomic::{AtomicU16, Ordering};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tracing::{error, info};

static RUNTIME_COUNTER: AtomicU16 = AtomicU16::new(1);

#[derive(Debug, Serialize, Deserialize)]
pub struct AllOutbounds {
    pub outbounds: Option<Vec<OutboundInfo>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OutboundInfo {
    pub tag: String,
    pub protocol: String,
    pub sub_outbounds_tag: Option<Vec<String>>,
}

pub struct ProxyManager {
    status: ProxyStatus,
    config_content: Option<String>,
    start_time: Option<u64>,
    leaf_rt: Option<Arc<leaf::RuntimeManager>>,
    stat_manager: Option<Arc<tokio::sync::RwLock<leaf::app::stat_manager::StatManager>>>,
    api_base_url: String,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
enum ProxyStatus {
    Disconnected,
    Connecting,
    Connected,
    Error(String),
}

impl ProxyManager {
    pub fn new() -> Self {
        Self {
            status: ProxyStatus::Disconnected,
            config_content: None,
            start_time: None,
            leaf_rt: None,
            stat_manager: None,
            api_base_url: "http://127.0.0.1:9090".to_string(),
        }
    }

    pub fn get_status(&self) -> String {
        match &self.status {
            ProxyStatus::Disconnected => "disconnected".to_string(),
            ProxyStatus::Connecting => "connecting".to_string(),
            ProxyStatus::Connected => "connected".to_string(),
            ProxyStatus::Error(msg) => format!("error: {}", msg),
        }
    }

    pub async fn start(&mut self) -> Result<(), String> {
        if matches!(self.status, ProxyStatus::Connected) {
            info!("Proxy is already connected");
            return Ok(()); // Already running
        }

        if self.config_content.is_none() {
            error!("No configuration available for proxy startup");
            return Err("No configuration available".to_string());
        }

        self.status = ProxyStatus::Connecting;
        info!("Starting leaf proxy engine...");

        // Set API_LISTEN environment variable for leaf API server
        std::env::set_var("API_LISTEN", "127.0.0.1:9090");

        let config_content = self.config_content.as_ref().unwrap().clone();
        let runtime_id = RUNTIME_COUNTER.fetch_add(1, Ordering::SeqCst);

        // Parse JSON config and convert to leaf internal config
        let config = match leaf::config::json::from_string(&config_content) {
            Ok(cfg) => cfg,
            Err(e) => {
                let error_msg = format!("Invalid proxy configuration: {}", e);
                error!("{}", error_msg);
                self.status = ProxyStatus::Error(error_msg.clone());
                return Err(error_msg);
            }
        };

        // Start leaf proxy in a separate thread to avoid blocking
        let start_options = leaf::StartOptions {
            config: leaf::Config::Internal(config),
            #[cfg(feature = "auto-reload")]
            auto_reload: false,
            runtime_opt: leaf::RuntimeOption::MultiThreadAuto(2 * 1024 * 1024), // 2MB stack
        };

        // Use tokio spawn to run leaf::start in a separate task
        // leaf::start blocks until shutdown, so we don't wait for it
        let rt_id = runtime_id;
        let (started_tx, started_rx) = tokio::sync::oneshot::channel();
        let _task =
            std::thread::spawn(
                move || match leaf::start(rt_id, start_options, Some(started_tx)) {
                    Ok(_) => Ok(()),
                    Err(e) => {
                        error!("Failed to start leaf proxy: {}", e);
                        Err(e)
                    }
                },
            );
        match started_rx.await {
            Ok(Ok(_)) => {}
            Ok(Err(e)) => {
                error!("Failed to start leaf proxy: {}", e);
                self.status = ProxyStatus::Error(e.to_string());
                return Err(e.to_string());
            }
            Err(e) => {
                error!("Failed to start leaf proxy: {}", e);
                self.status = ProxyStatus::Error(e.to_string());
                return Err(e.to_string());
            }
        }
        // Check if the runtime is actually running
        if let Some(rt) = leaf::get_runtime_manager(rt_id) {
            self.status = ProxyStatus::Connected;
            self.leaf_rt = Some(rt.clone());
            self.start_time = Some(
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            );

            // Get stat manager for traffic monitoring
            if let Some(manager) = leaf::RUNTIME_MANAGER.lock().unwrap().get(&runtime_id) {
                self.stat_manager = Some(manager.stat_manager());
            }

            info!(
                "Leaf proxy started successfully with runtime ID: {}",
                runtime_id
            );
            Ok(())
        } else {
            let error_msg = "Leaf proxy failed to start properly".to_string();
            error!("{}", error_msg);
            self.status = ProxyStatus::Error(error_msg.clone());
            Err(error_msg)
        }
    }

    pub async fn stop(&mut self) -> Result<(), String> {
        if matches!(self.status, ProxyStatus::Disconnected) {
            return Ok(()); // Already stopped
        }

        if let Some(rt) = self.leaf_rt.take() {
            info!("Stopping leaf proxy");
            rt.shutdown().await;
        }

        self.status = ProxyStatus::Disconnected;
        self.start_time = None;
        self.stat_manager = None;

        Ok(())
    }

    pub async fn update_config(&mut self, config: String) -> Result<String, String> {
        // Stop current runner if running
        if matches!(self.status, ProxyStatus::Connected) {
            self.stop().await?;
        }

        // Update config
        self.config_content = Some(config);
        Ok("Configuration updated".to_string())
    }

    pub fn get_stats(&self) -> Value {
        if matches!(self.status, ProxyStatus::Connected) {
            // Try to get real stats from leaf if available
            if let Some(_stat_manager) = &self.stat_manager {
                // For now, we'll return basic stats since accessing the stat manager
                // requires async operations and this is a sync function
                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();

                if let Some(start_time) = self.start_time {
                    let elapsed = now - start_time;

                    return json!({
                        "status": self.get_status(),
                        "uptime": elapsed,
                        "upload_bytes": 0, // TODO: Get from leaf stat manager
                        "download_bytes": 0, // TODO: Get from leaf stat manager
                        "upload_speed": 0,
                        "download_speed": 0,
                        "connections": []
                    });
                }
            } else {
                // Fallback to basic status
                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();

                if let Some(start_time) = self.start_time {
                    let elapsed = now - start_time;

                    return json!({
                        "status": self.get_status(),
                        "uptime": elapsed,
                        "upload_bytes": 0,
                        "download_bytes": 0,
                        "upload_speed": 0,
                        "download_speed": 0,
                        "connections": []
                    });
                }
            }
        }

        json!({
            "status": self.get_status(),
            "uptime": 0,
            "upload_bytes": 0,
            "download_bytes": 0,
            "upload_speed": 0,
            "download_speed": 0,
            "connections": []
        })
    }

    /// Get detailed proxy statistics (async version for accessing leaf stats)
    pub async fn get_detailed_stats(&self) -> Value {
        if matches!(self.status, ProxyStatus::Connected) {
            if let Some(stat_manager) = &self.stat_manager {
                // Access the stat manager to get real traffic data
                let stats = stat_manager.read().await;

                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();

                let uptime = if let Some(start_time) = self.start_time {
                    now - start_time
                } else {
                    0
                };

                // Calculate total stats from all counters
                let total_upload_bytes: u64 = stats.counters.iter().map(|c| c.bytes_sent()).sum();
                let total_download_bytes: u64 =
                    stats.counters.iter().map(|c| c.bytes_recvd()).sum();
                let active_connections = stats
                    .counters
                    .iter()
                    .filter(|c| !c.recv_completed() && !c.send_completed())
                    .count();

                return json!({
                    "status": self.get_status(),
                    "uptime": uptime,
                    "upload_bytes": total_upload_bytes,
                    "download_bytes": total_download_bytes,
                    "upload_speed": 0, // TODO: Calculate speed from byte differences over time
                    "download_speed": 0, // TODO: Calculate speed from byte differences over time
                    "connections": active_connections
                });
            }
        }

        self.get_stats()
    }

    /// Get all available outbound tags and their selectable nodes from leaf API
    pub async fn get_all_outbound_nodes(&self) -> Result<AllOutbounds, String> {
        if !matches!(self.status, ProxyStatus::Connected) {
            return Err("Proxy is not connected".to_string());
        }

        if let Some(rt) = &self.leaf_rt {
            let all_outbounds = rt
                .get_all_outbound_info()
                .await
                .map_err(|e| e.to_string())?;
            let all_outbounds = AllOutbounds {
                outbounds: Some(
                    all_outbounds
                        .into_iter()
                        .map(|x| OutboundInfo {
                            tag: x.tag().to_string(),
                            protocol: x.protocol().to_string(),
                            sub_outbounds_tag: if x.sub_handlers().is_empty() {
                                None
                            } else {
                                Some(x.sub_handlers().iter().map(|x| x.clone()).collect())
                            },
                        })
                        .collect(),
                ),
            };
            Ok(all_outbounds)
        } else {
            Err("Proxy is not connected".to_string())
        }
    }

    /// Get available proxy nodes (simplified for backwards compatibility)
    pub async fn get_proxy_nodes(&self) -> Result<Vec<(String, Vec<String>)>, String> {
        match self.get_all_outbound_nodes().await {
            Ok(data) => {
                let mut result = Vec::new();

                if let Some(outbounds) = data.outbounds {
                    for outbound in outbounds {
                        // Only include select protocol outbounds
                        if outbound.protocol == "select" {
                            if let Some(sub_outbounds) = outbound.sub_outbounds_tag {
                                result.push((outbound.tag, sub_outbounds));
                            } else {
                                // If no sub_outbounds, add as empty list
                                result.push((outbound.tag, vec![]));
                            }
                        }
                    }
                }

                Ok(result)
            }
            Err(_) => Ok(vec![]),
        }
    }

    /// Get available proxy nodes for a specific outbound
    pub async fn get_proxy_nodes_for_outbound(
        &self,
        outbound: &str,
    ) -> Result<Vec<String>, String> {
        if !matches!(self.status, ProxyStatus::Connected) {
            return Err("Proxy is not connected".to_string());
        }

        if let Some(rt) = &self.leaf_rt {
            if let Ok(select_list) = rt.get_outbound_selects(&outbound).await {
                return Ok(select_list);
            } else {
                return Err("Failed to get selected node".to_string());
            }
        }
        Err("Proxy is not connected".to_string())
    }

    /// Get currently selected proxy node
    pub async fn get_selected_node(&self, outbound: &str) -> Result<Option<String>, String> {
        if !matches!(self.status, ProxyStatus::Connected) {
            return Err("Proxy is not connected".to_string());
        }

        if let Some(rt) = &self.leaf_rt {
            if let Ok(selected) = rt.get_outbound_selected(&outbound).await {
                return Ok(Some(selected));
            } else {
                return Err("Failed to get selected node".to_string());
            }
        }
        Err("Proxy is not connected".to_string())
    }

    /// Set proxy node selection
    pub async fn set_selected_node(&self, outbound: &str, select: &str) -> Result<(), String> {
        if !matches!(self.status, ProxyStatus::Connected) {
            return Err("Proxy is not connected".to_string());
        }

        if let Some(rt) = &self.leaf_rt {
            if let Ok(_selected) = rt.set_outbound_selected(&outbound, select).await {
                return Ok(());
            } else {
                return Err("Failed to set selected node".to_string());
            }
        }
        Err("Proxy is not connected".to_string())
    }

    /// Set global target for global mode
    pub async fn set_global_target(&self, target: Option<String>) -> Result<(), String> {
        info!("set_global_target: {:?}", target);
        if !matches!(self.status, ProxyStatus::Connected) {
            return Err("Proxy is not connected".to_string());
        }

        if let Some(rt) = &self.leaf_rt {
            if rt.set_global_target(target).await {
                rt.cancel_all_sessions().await;
            }
            return Ok(());
        }
        Err("Proxy is not connected".to_string())
    }

    /// Get global target for global mode
    pub async fn get_global_target(&self) -> Result<Option<String>, String> {
        if !matches!(self.status, ProxyStatus::Connected) {
            return Err("Proxy is not connected".to_string());
        }

        if let Some(rt) = &self.leaf_rt {
            if let Ok(target) = rt.get_global_target().await {
                return Ok(target);
            } else {
                return Err("Failed to get global target".to_string());
            }
        }
        Err("Proxy is not connected".to_string())
    }
}

impl Default for ProxyManager {
    fn default() -> Self {
        Self::new()
    }
}

impl Drop for ProxyManager {
    fn drop(&mut self) {
        // Ensure proxy is stopped when ProxyManager is dropped
        if matches!(self.status, ProxyStatus::Connected) {
            if let Some(rt) = self.leaf_rt.take() {
                info!("ProxyManager dropped, stopping leaf proxy");
                // Use block_on to wait for the async stop operation
                let rt_clone = rt.clone();
                std::thread::spawn(move || {
                    tokio::runtime::Runtime::new()
                        .unwrap()
                        .block_on(async move {
                            rt_clone.shutdown().await;
                        });
                });
            }
        }
    }
}
