<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proxy Layout Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .chevron-down {
            width: 12px;
            height: 12px;
            border-right: 2px solid #6b7280;
            border-bottom: 2px solid #6b7280;
            transform: rotate(45deg);
        }
        .chevron-right {
            width: 12px;
            height: 12px;
            border-right: 2px solid #6b7280;
            border-bottom: 2px solid #6b7280;
            transform: rotate(-45deg);
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Proxy Layout Test - Fixed Version</h1>
        
        <!-- Global Mode Section -->
        <div class="bg-white overflow-hidden shadow rounded-lg mb-8">
            <div class="px-6 py-3 border-b border-gray-200">
                <h3 class="text-base font-medium text-gray-900 flex items-center">
                    Global Mode - Proxy Groups
                </h3>
            </div>
            <div class="px-6 py-3">
                <div class="space-y-4">
                    <!-- Proxy Groups Section -->
                    <div class="space-y-2">
                        <h4 class="text-sm font-medium text-gray-700 flex items-center">
                            <span class="w-1 h-4 bg-gray-400 rounded-full mr-2"></span>
                            代理组
                        </h4>
                        
                        <!-- Proxy Group 1 - Collapsed -->
                        <div class="border border-gray-200 rounded-lg overflow-hidden">
                            <div class="flex items-center justify-between p-3 cursor-pointer transition-colors bg-gray-50 hover:bg-gray-100">
                                <div class="flex items-center space-x-2">
                                    <button class="p-1 hover:bg-gray-200 rounded transition-colors">
                                        <div class="chevron-right"></div>
                                    </button>
                                    <div class="w-2 h-2 rounded-full bg-gray-300"></div>
                                    <h4 class="text-sm font-medium text-gray-900">Proxy</h4>
                                    <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">select</span>
                                </div>
                                <div class="text-xs text-gray-500">
                                    2 节点 • direct
                                </div>
                            </div>
                        </div>
                        
                        <!-- Proxy Group 2 - Expanded (This shows the fix) -->
                        <div class="border border-blue-500 rounded-lg overflow-hidden bg-blue-50">
                            <div class="flex items-center justify-between p-3 cursor-pointer transition-colors bg-blue-50 hover:bg-blue-100">
                                <div class="flex items-center space-x-2">
                                    <button class="p-1 hover:bg-gray-200 rounded transition-colors">
                                        <div class="chevron-down"></div>
                                    </button>
                                    <div class="w-2 h-2 rounded-full bg-blue-500"></div>
                                    <h4 class="text-sm font-medium text-gray-900">FailOver</h4>
                                    <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">failover</span>
                                    <span class="text-xs text-blue-600 font-medium bg-blue-100 px-2 py-1 rounded">已选择</span>
                                </div>
                                <div class="text-xs text-gray-500">
                                    2 节点 • direct
                                </div>
                            </div>
                            
                            <!-- Child Nodes - This should appear directly under the parent -->
                            <div class="border-t border-gray-200 bg-gray-50">
                                <div class="p-3">
                                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                                        <div class="flex items-center justify-between p-2 rounded-lg border border-gray-200 bg-white cursor-not-allowed opacity-60">
                                            <div class="flex items-center space-x-2">
                                                <div class="w-2 h-2 rounded-full bg-green-500"></div>
                                                <div class="min-w-0">
                                                    <h6 class="text-xs font-medium text-gray-900 truncate">FailOver</h6>
                                                    <p class="text-xs text-gray-500">0ms</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between p-2 rounded-lg border border-blue-400 bg-blue-50 cursor-pointer hover:border-gray-300 hover:bg-gray-50">
                                            <div class="flex items-center space-x-2">
                                                <div class="w-2 h-2 rounded-full bg-green-500"></div>
                                                <div class="min-w-0">
                                                    <h6 class="text-xs font-medium text-gray-900 truncate">direct</h6>
                                                    <p class="text-xs text-gray-500">0ms</p>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-1">
                                                <div class="w-1 h-1 bg-blue-500 rounded-full"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            <strong>✅ Layout Fix Applied:</strong> Child proxy nodes now appear directly underneath their parent proxy group when expanded, instead of at the bottom of the page.
        </div>
        
        <div class="mt-4 text-sm text-gray-600">
            <p><strong>Key Changes Made:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
                <li>Added <code>overflow-hidden</code> to the main proxy group container</li>
                <li>Restructured child nodes container with proper border and background styling</li>
                <li>Wrapped the grid in a properly padded container</li>
                <li>Applied consistent styling to both global mode and rule mode</li>
            </ul>
        </div>
    </div>
</body>
</html>
