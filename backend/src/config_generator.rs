// Dynamic configuration generation system with template management and placeholder replacement

use std::collections::HashMap;
use uuid::Uuid;
use regex::Regex;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::entities::{users, subscriptions, plans, system_settings, config_templates};
use crate::repositories::{SystemSettingRepository, ConfigTemplateRepository, Repository};
use crate::errors::{AppError, AppResult};

/// Configuration template with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub template: String,
    pub version: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_active: bool,
}

/// User context for configuration generation
#[derive(Debug, Clone)]
pub struct UserConfigContext {
    pub user_id: Uuid,
    pub username: String,
    pub email: Option<String>,
    pub subscription: Option<ConfigSubscriptionInfo>,
    pub proxy_credentials: Option<ProxyCredentials>,
    pub server_endpoints: Vec<String>,
    pub system_settings: HashMap<String, String>,
}

/// Subscription information for configuration context
#[derive(Debug, Clone)]
pub struct ConfigSubscriptionInfo {
    pub id: Uuid,
    pub plan_id: Uuid,
    pub plan_name: String,
    pub start_date: DateTime<Utc>,
    pub end_date: DateTime<Utc>,
    pub total_traffic_gb: rust_decimal::Decimal,
    pub used_traffic_gb: rust_decimal::Decimal,
    pub max_concurrent_devices: i32,
    pub proxy_username: Option<String>,
    pub proxy_password: Option<String>,
    pub proxy_port: Option<i32>,
}

/// Proxy credentials for VPN connection
#[derive(Debug, Clone)]
pub struct ProxyCredentials {
    pub username: String,
    pub password: String,
    pub port: i32,
    pub server_endpoint: String,
}

/// Configuration generation result
#[derive(Debug, Serialize)]
pub struct GeneratedConfig {
    pub config: String,
    pub metadata: ConfigMetadata,
}

/// Metadata about generated configuration
#[derive(Debug, Serialize)]
pub struct ConfigMetadata {
    pub user_id: Uuid,
    pub generated_at: DateTime<Utc>,
    pub template_version: String,
    pub placeholders_replaced: Vec<String>,
    pub config_hash: String,
}

/// Configuration generator with advanced placeholder replacement
pub struct ConfigGenerator {
    /// Regex pattern for matching placeholders
    placeholder_pattern: Regex,
    /// Available placeholders and their descriptions
    available_placeholders: HashMap<String, String>,
}

impl ConfigGenerator {
    /// Create new configuration generator
    pub fn new() -> Self {
        let mut available_placeholders = HashMap::new();
        available_placeholders.insert("username".to_string(), "User's username".to_string());
        available_placeholders.insert("user_id".to_string(), "User's UUID".to_string());
        available_placeholders.insert("email".to_string(), "User's email address".to_string());
        available_placeholders.insert("proxy_username".to_string(), "Proxy server username".to_string());
        available_placeholders.insert("proxy_password".to_string(), "Proxy server password".to_string());
        available_placeholders.insert("proxy_port".to_string(), "Proxy server port".to_string());
        available_placeholders.insert("server_endpoint".to_string(), "Primary server endpoint".to_string());
        available_placeholders.insert("server_endpoints".to_string(), "All server endpoints (comma-separated)".to_string());
        available_placeholders.insert("plan_name".to_string(), "Subscription plan name".to_string());
        available_placeholders.insert("subscription_id".to_string(), "Subscription UUID".to_string());
        available_placeholders.insert("subscription_expires".to_string(), "Subscription expiration date".to_string());
        available_placeholders.insert("traffic_limit".to_string(), "Traffic limit in GB".to_string());
        available_placeholders.insert("traffic_used".to_string(), "Used traffic in GB".to_string());
        available_placeholders.insert("traffic_remaining".to_string(), "Remaining traffic in GB".to_string());
        available_placeholders.insert("device_limit".to_string(), "Maximum concurrent devices".to_string());
        available_placeholders.insert("config_hash".to_string(), "Configuration hash for validation".to_string());
        available_placeholders.insert("generated_at".to_string(), "Configuration generation timestamp".to_string());

        Self {
            placeholder_pattern: Regex::new(r"\{\{([^}]+)\}\}").unwrap(),
            available_placeholders,
        }
    }

    /// Generate configuration from template and user context
    pub fn generate_config(
        &self,
        template: &str,
        context: &UserConfigContext,
    ) -> AppResult<GeneratedConfig> {
        let generated_at = Utc::now();
        let mut placeholders_replaced = Vec::new();
        
        // Build replacement map
        let mut replacements = HashMap::new();
        
        // User information
        replacements.insert("username", context.username.clone());
        replacements.insert("user_id", context.user_id.to_string());
        replacements.insert("email", context.email.clone().unwrap_or_else(|| "".to_string()));
        
        // Server endpoints
        if !context.server_endpoints.is_empty() {
            replacements.insert("server_endpoint", context.server_endpoints[0].clone());
            replacements.insert("server_endpoints", context.server_endpoints.join(","));
        } else {
            replacements.insert("server_endpoint", "server.vpn.example.com:443".to_string());
            replacements.insert("server_endpoints", "server.vpn.example.com:443".to_string());
        }
        
        // Subscription information
        if let Some(subscription) = &context.subscription {
            replacements.insert("plan_name", subscription.plan_name.clone());
            replacements.insert("subscription_id", subscription.id.to_string());
            replacements.insert("subscription_expires", subscription.end_date.format("%Y-%m-%d %H:%M:%S UTC").to_string());
            replacements.insert("traffic_limit", subscription.total_traffic_gb.to_string());
            replacements.insert("traffic_used", subscription.used_traffic_gb.to_string());
            replacements.insert("traffic_remaining", (subscription.total_traffic_gb - subscription.used_traffic_gb).to_string());
            replacements.insert("device_limit", subscription.max_concurrent_devices.to_string());
            
            // Proxy credentials
            if let Some(proxy_username) = &subscription.proxy_username {
                replacements.insert("proxy_username", proxy_username.clone());
            }
            if let Some(proxy_password) = &subscription.proxy_password {
                replacements.insert("proxy_password", proxy_password.clone());
            }
            if let Some(proxy_port) = subscription.proxy_port {
                replacements.insert("proxy_port", proxy_port.to_string());
            }
        }
        
        // System settings
        for (key, value) in &context.system_settings {
            replacements.insert(key, value.clone());
        }
        
        // Metadata
        replacements.insert("generated_at", generated_at.format("%Y-%m-%d %H:%M:%S UTC").to_string());
        
        // Process template and replace placeholders
        let mut processed_config = template.to_string();
        
        for captures in self.placeholder_pattern.captures_iter(template) {
            let full_match = captures.get(0).unwrap().as_str();
            let placeholder_name = captures.get(1).unwrap().as_str();
            
            if let Some(replacement_value) = replacements.get(placeholder_name) {
                processed_config = processed_config.replace(full_match, replacement_value);
                placeholders_replaced.push(placeholder_name.to_string());
            } else {
                // Log warning about unknown placeholder but don't fail
                tracing::warn!("Unknown placeholder: {}", placeholder_name);
            }
        }
        
        // Generate configuration hash
        let config_hash = format!("{:x}", md5::compute(processed_config.as_bytes()));
        
        // Final replacement of config_hash placeholder
        processed_config = processed_config.replace("{{config_hash}}", &config_hash);
        
        let metadata = ConfigMetadata {
            user_id: context.user_id,
            generated_at,
            template_version: "1.0".to_string(),
            placeholders_replaced,
            config_hash: config_hash.clone(),
        };
        
        Ok(GeneratedConfig {
            config: processed_config,
            metadata,
        })
    }
    
    /// Validate template syntax
    pub fn validate_template(&self, template: &str) -> AppResult<Vec<String>> {
        let mut placeholders = Vec::new();
        let mut unknown_placeholders = Vec::new();
        
        for captures in self.placeholder_pattern.captures_iter(template) {
            let placeholder_name = captures.get(1).unwrap().as_str();
            placeholders.push(placeholder_name.to_string());
            
            if !self.available_placeholders.contains_key(placeholder_name) {
                unknown_placeholders.push(placeholder_name.to_string());
            }
        }
        
        if !unknown_placeholders.is_empty() {
            return Err(AppError::validation(format!(
                "Unknown placeholders: {}",
                unknown_placeholders.join(", ")
            )));
        }
        
        Ok(placeholders)
    }
    
    /// Get available placeholders
    pub fn get_available_placeholders(&self) -> &HashMap<String, String> {
        &self.available_placeholders
    }
    
    /// Extract placeholders from template
    pub fn extract_placeholders(&self, template: &str) -> Vec<String> {
        let mut placeholders = Vec::new();
        
        for captures in self.placeholder_pattern.captures_iter(template) {
            let placeholder_name = captures.get(1).unwrap().as_str();
            if !placeholders.contains(&placeholder_name.to_string()) {
                placeholders.push(placeholder_name.to_string());
            }
        }
        
        placeholders
    }
}

impl Default for ConfigGenerator {
    fn default() -> Self {
        Self::new()
    }
}

/// Configuration template manager
pub struct ConfigTemplateManager {
    generator: ConfigGenerator,
}

impl ConfigTemplateManager {
    /// Create new template manager
    pub fn new() -> Self {
        Self {
            generator: ConfigGenerator::new(),
        }
    }
    
    /// Generate user configuration from plan template
    pub async fn generate_user_config(
        &self,
        user: &users::Model,
        subscription: Option<&subscriptions::Model>,
        plan: Option<&plans::Model>,
        system_settings_repo: &SystemSettingRepository,
    ) -> AppResult<GeneratedConfig> {
        // Get template from plan or use default
        let template = if let Some(plan) = plan {
            &plan.config_template
        } else {
            // Default template
            DEFAULT_CONFIG_TEMPLATE
        };
        
        // Build user context
        let mut context = UserConfigContext {
            user_id: user.id,
            username: user.username.clone(),
            email: user.email.clone(),
            subscription: None,
            proxy_credentials: None,
            server_endpoints: Vec::new(),
            system_settings: HashMap::new(),
        };
        
        // Add subscription information
        if let Some(subscription) = subscription {
            if let Some(plan) = plan {
                context.subscription = Some(ConfigSubscriptionInfo {
                    id: subscription.id,
                    plan_id: subscription.plan_id,
                    plan_name: plan.name.clone(),
                    start_date: subscription.start_date.into(),
                    end_date: subscription.end_date.into(),
                    total_traffic_gb: subscription.total_traffic_gb,
                    used_traffic_gb: subscription.used_traffic_gb,
                    max_concurrent_devices: subscription.max_concurrent_devices,
                    proxy_username: subscription.proxy_username.clone(),
                    proxy_password: subscription.proxy_password.clone(),
                    proxy_port: subscription.proxy_port,
                });
            }
        }
        
        // Load system settings
        let settings = system_settings_repo.find_all().await?;
        for setting in settings {
            context.system_settings.insert(setting.key, setting.value.unwrap_or_default());
        }
        
        // Get server endpoints from system settings
        if let Some(endpoints) = context.system_settings.get("server_endpoints") {
            context.server_endpoints = endpoints.split(',').map(|s| s.trim().to_string()).collect();
        }
        
        // Generate configuration
        self.generator.generate_config(template, &context)
    }
    
    /// Generate user configuration from database template
    pub async fn generate_user_config_from_template(
        &self,
        user: &users::Model,
        subscription: Option<&subscriptions::Model>,
        plan: Option<&plans::Model>,
        config_template: &config_templates::Model,
        system_settings_repo: &SystemSettingRepository,
    ) -> AppResult<GeneratedConfig> {
        // Validate template is active
        if !config_template.is_active {
            return Err(AppError::validation("Configuration template is not active"));
        }
        
        // Build user context
        let mut context = UserConfigContext {
            user_id: user.id,
            username: user.username.clone(),
            email: user.email.clone(),
            subscription: None,
            proxy_credentials: None,
            server_endpoints: Vec::new(),
            system_settings: HashMap::new(),
        };
        
        // Add subscription information
        if let Some(subscription) = subscription {
            if let Some(plan) = plan {
                context.subscription = Some(ConfigSubscriptionInfo {
                    id: subscription.id,
                    plan_id: subscription.plan_id,
                    plan_name: plan.name.clone(),
                    start_date: subscription.start_date.into(),
                    end_date: subscription.end_date.into(),
                    total_traffic_gb: subscription.total_traffic_gb,
                    used_traffic_gb: subscription.used_traffic_gb,
                    max_concurrent_devices: subscription.max_concurrent_devices,
                    proxy_username: subscription.proxy_username.clone(),
                    proxy_password: subscription.proxy_password.clone(),
                    proxy_port: subscription.proxy_port,
                });
            }
        }
        
        // Load system settings
        let settings = system_settings_repo.find_all().await?;
        for setting in settings {
            context.system_settings.insert(setting.key, setting.value.unwrap_or_default());
        }
        
        // Get server endpoints from system settings
        if let Some(endpoints) = context.system_settings.get("server_endpoints") {
            context.server_endpoints = endpoints.split(',').map(|s| s.trim().to_string()).collect();
        }
        
        // Generate configuration using database template
        let mut result = self.generator.generate_config(&config_template.template_content, &context)?;
        
        // Update metadata with template information
        result.metadata.template_version = config_template.version.clone();
        
        Ok(result)
    }
    
    /// Generate user configuration with template selection logic
    pub async fn generate_user_config_with_template_selection(
        &self,
        user: &users::Model,
        subscription: Option<&subscriptions::Model>,
        plan: Option<&plans::Model>,
        template_type: Option<&str>,
        system_settings_repo: &SystemSettingRepository,
        config_template_repo: &ConfigTemplateRepository,
    ) -> AppResult<GeneratedConfig> {
        // Determine which template to use
        let config_template = if let Some(plan) = plan {
            if let Some(template_id) = plan.config_template_id {
                // Plan has a specific template assigned
                config_template_repo.find_by_id(template_id).await?
            } else {
                // Plan doesn't have a specific template, use system default for the type
                let template_type = template_type.unwrap_or("leaf");
                config_template_repo.find_system_default_by_type(template_type).await?
            }
        } else {
            // No plan specified, use system default
            let template_type = template_type.unwrap_or("leaf");
            config_template_repo.find_system_default_by_type(template_type).await?
        };
        
        if let Some(template) = config_template {
            // Generate using database template
            self.generate_user_config_from_template(
                user,
                subscription,
                plan,
                &template,
                system_settings_repo,
            ).await
        } else {
            // Fallback to legacy plan template or default
            self.generate_user_config(
                user,
                subscription,
                plan,
                system_settings_repo,
            ).await
        }
    }
    
    /// Validate configuration template
    pub fn validate_template(&self, template: &str) -> AppResult<Vec<String>> {
        self.generator.validate_template(template)
    }
    
    /// Get available placeholders
    pub fn get_available_placeholders(&self) -> &HashMap<String, String> {
        self.generator.get_available_placeholders()
    }
}

impl Default for ConfigTemplateManager {
    fn default() -> Self {
        Self::new()
    }
}

/// Default configuration template for leaf proxy
const DEFAULT_CONFIG_TEMPLATE: &str = r#"
[General]
loglevel = info
dns-server = *******, *******
tun-fd = 
always-real-ip = 
interface = 
rt-mark = 

[Proxy]
proxy = socks, {{proxy_username}}:{{proxy_password}}@{{server_endpoint}}, udp-relay=true

[Rule]
FINAL, proxy

# Generated at: {{generated_at}}
# User: {{username}} ({{user_id}})
# Plan: {{plan_name}}
# Subscription expires: {{subscription_expires}}
# Traffic: {{traffic_used}}/{{traffic_limit}} GB
# Config hash: {{config_hash}}
"#;

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_config_generator_basic() {
        let generator = ConfigGenerator::new();
        
        let template = "username: {{username}}, user_id: {{user_id}}";
        let context = UserConfigContext {
            user_id: Uuid::new_v4(),
            username: "testuser".to_string(),
            email: None,
            subscription: None,
            proxy_credentials: None,
            server_endpoints: Vec::new(),
            system_settings: HashMap::new(),
        };
        
        let result = generator.generate_config(template, &context).unwrap();
        assert!(result.config.contains("username: testuser"));
        assert!(result.config.contains(&format!("user_id: {}", context.user_id)));
    }
    
    #[test]
    fn test_placeholder_extraction() {
        let generator = ConfigGenerator::new();
        
        let template = "{{username}} connects to {{server_endpoint}} with {{proxy_password}}";
        let placeholders = generator.extract_placeholders(template);
        
        assert_eq!(placeholders.len(), 3);
        assert!(placeholders.contains(&"username".to_string()));
        assert!(placeholders.contains(&"server_endpoint".to_string()));
        assert!(placeholders.contains(&"proxy_password".to_string()));
    }
    
    #[test]
    fn test_template_validation() {
        let generator = ConfigGenerator::new();
        
        let valid_template = "{{username}} {{server_endpoint}}";
        assert!(generator.validate_template(valid_template).is_ok());
        
        let invalid_template = "{{username}} {{invalid_placeholder}}";
        assert!(generator.validate_template(invalid_template).is_err());
    }
}