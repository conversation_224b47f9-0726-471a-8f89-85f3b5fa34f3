use axum::{
    extract::{Path, Query, State},
    response::Json,
    routing::{get, post, put, patch},
    Router,
};
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::sync::Arc;
use chrono::{DateTime, Utc};

use crate::auth::UserContext;
use crate::errors::{AppError, AppResult, ApiResponse, success};
use crate::services::subscription_management::{
    SubscriptionManagementService, SubscriptionInfo, SubscriptionValidationResult,
    CreateSubscriptionRequest, UpdateSubscriptionRequest, RenewSubscriptionRequest
};

/// Query parameters for listing subscriptions
#[derive(Debug, Deserialize)]
pub struct ListSubscriptionsQuery {
    pub user_id: Option<Uuid>,
    pub status: Option<String>,
    pub expiring_days: Option<i32>,
}

/// Response for subscription operations
#[derive(Debug, Serialize)]
pub struct SubscriptionResponse {
    pub subscription: SubscriptionInfo,
}

/// Response for subscription list
#[derive(Debug, Serialize)]
pub struct SubscriptionListResponse {
    pub subscriptions: Vec<SubscriptionInfo>,
    pub total_count: usize,
}

/// Response for subscription validation
#[derive(Debug, Serialize)]
pub struct ValidationResponse {
    pub validation: SubscriptionValidationResult,
}

/// GET /api/user/subscriptions - Get current user's subscriptions
pub async fn get_user_subscriptions(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<SubscriptionListResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    match subscription_service.get_user_subscriptions(user_context.user_id).await {
        Ok(subscriptions) => {
            let response = SubscriptionListResponse {
                total_count: subscriptions.len(),
                subscriptions,
            };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get user subscriptions: {}", e);
            Err(AppError::internal(&format!("Failed to get subscriptions: {}", e)))
        }
    }
}

/// GET /api/user/subscriptions/:id - Get specific subscription for current user
pub async fn get_user_subscription(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<SubscriptionResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    match subscription_service.get_subscription(subscription_id).await {
        Ok(subscription) => {
            // Verify the subscription belongs to the current user
            if subscription.user_id != user_context.user_id {
                return Err(AppError::authorization("Access denied to this subscription"));
            }
            
            let response = SubscriptionResponse { subscription };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to get subscription: {}", e)))
        }
    }
}

/// GET /api/user/subscriptions/validation - Validate current user's subscription
pub async fn validate_user_subscription(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<ValidationResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    match subscription_service.validate_user_subscription(user_context.user_id).await {
        Ok(validation) => {
            let response = ValidationResponse { validation };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to validate subscription for user {}: {}", user_context.user_id, e);
            Err(AppError::internal(&format!("Failed to validate subscription: {}", e)))
        }
    }
}

/// POST /api/user/subscriptions/:id/renew - Renew user's subscription
pub async fn renew_user_subscription(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    user_context: UserContext,
    Json(request): Json<RenewSubscriptionRequest>,
) -> AppResult<Json<ApiResponse<SubscriptionResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    // First verify the subscription belongs to the current user
    match subscription_service.get_subscription(subscription_id).await {
        Ok(subscription) => {
            if subscription.user_id != user_context.user_id {
                return Err(AppError::authorization("Access denied to this subscription"));
            }
        }
        Err(e) => {
            tracing::error!("Failed to get subscription {}: {}", subscription_id, e);
            return Err(AppError::internal(&format!("Failed to get subscription: {}", e)));
        }
    }
    
    match subscription_service.renew_subscription(subscription_id, request).await {
        Ok(subscription) => {
            let response = SubscriptionResponse { subscription };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to renew subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to renew subscription: {}", e)))
        }
    }
}

/// GET /api/admin/subscriptions - Get all subscriptions (admin only)
pub async fn get_all_subscriptions(
    State(db): State<DatabaseConnection>,
    Query(query): Query<ListSubscriptionsQuery>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<SubscriptionListResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    let subscriptions = if let Some(user_id) = query.user_id {
        // Get subscriptions for specific user
        subscription_service.get_user_subscriptions(user_id).await
    } else if let Some(expiring_days) = query.expiring_days {
        // Get expiring subscriptions
        subscription_service.get_expiring_subscriptions(expiring_days).await
    } else {
        // Get all subscriptions
        subscription_service.get_all_subscriptions().await
    };
    
    match subscriptions {
        Ok(mut subscriptions) => {
            // Filter by status if specified
            if let Some(status) = &query.status {
                subscriptions.retain(|s| s.status.to_string().to_lowercase() == status.to_lowercase());
            }
            
            let response = SubscriptionListResponse {
                total_count: subscriptions.len(),
                subscriptions,
            };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get subscriptions: {}", e);
            Err(AppError::internal(&format!("Failed to get subscriptions: {}", e)))
        }
    }
}

/// GET /api/admin/subscriptions/:id - Get specific subscription (admin only)
pub async fn get_subscription(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<SubscriptionResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    match subscription_service.get_subscription(subscription_id).await {
        Ok(subscription) => {
            let response = SubscriptionResponse { subscription };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to get subscription: {}", e)))
        }
    }
}

/// POST /api/admin/subscriptions - Create new subscription (admin only)
pub async fn create_subscription(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Json(request): Json<CreateSubscriptionRequest>,
) -> AppResult<Json<ApiResponse<SubscriptionResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    match subscription_service.create_subscription(request).await {
        Ok(subscription) => {
            tracing::info!("Admin {} created subscription {}", user_context.username, subscription.id);
            
            let response = SubscriptionResponse { subscription };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to create subscription: {}", e);
            Err(AppError::internal(&format!("Failed to create subscription: {}", e)))
        }
    }
}

/// PUT /api/admin/subscriptions/:id - Update subscription (admin only)
pub async fn update_subscription(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    user_context: UserContext,
    Json(request): Json<UpdateSubscriptionRequest>,
) -> AppResult<Json<ApiResponse<SubscriptionResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    match subscription_service.update_subscription(subscription_id, request).await {
        Ok(subscription) => {
            tracing::info!("Admin {} updated subscription {}", user_context.username, subscription_id);
            
            let response = SubscriptionResponse { subscription };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to update subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to update subscription: {}", e)))
        }
    }
}

/// POST /api/admin/subscriptions/:id/renew - Renew subscription (admin only)
pub async fn renew_subscription(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    user_context: UserContext,
    Json(request): Json<RenewSubscriptionRequest>,
) -> AppResult<Json<ApiResponse<SubscriptionResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    match subscription_service.renew_subscription(subscription_id, request).await {
        Ok(subscription) => {
            tracing::info!("Admin {} renewed subscription {}", user_context.username, subscription_id);
            
            let response = SubscriptionResponse { subscription };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to renew subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to renew subscription: {}", e)))
        }
    }
}

/// PATCH /api/admin/subscriptions/:id/suspend - Suspend subscription (admin only)
pub async fn suspend_subscription(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<SubscriptionResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    match subscription_service.suspend_subscription(subscription_id).await {
        Ok(subscription) => {
            tracing::info!("Admin {} suspended subscription {}", user_context.username, subscription_id);
            
            let response = SubscriptionResponse { subscription };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to suspend subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to suspend subscription: {}", e)))
        }
    }
}

/// PATCH /api/admin/subscriptions/:id/resume - Resume subscription (admin only)
pub async fn resume_subscription(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<SubscriptionResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    match subscription_service.resume_subscription(subscription_id).await {
        Ok(subscription) => {
            tracing::info!("Admin {} resumed subscription {}", user_context.username, subscription_id);
            
            let response = SubscriptionResponse { subscription };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to resume subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to resume subscription: {}", e)))
        }
    }
}

/// PATCH /api/admin/subscriptions/:id/cancel - Cancel subscription (admin only)
pub async fn cancel_subscription(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<SubscriptionResponse>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    match subscription_service.cancel_subscription(subscription_id).await {
        Ok(subscription) => {
            tracing::info!("Admin {} cancelled subscription {}", user_context.username, subscription_id);
            
            let response = SubscriptionResponse { subscription };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to cancel subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to cancel subscription: {}", e)))
        }
    }
}

/// POST /api/admin/subscriptions/process-expired - Process expired subscriptions (admin only)
pub async fn process_expired_subscriptions(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let subscription_service = SubscriptionManagementService::new(Arc::new(db));
    
    match subscription_service.handle_expired_subscriptions().await {
        Ok(processed_count) => {
            tracing::info!("Admin {} processed {} expired subscriptions", user_context.username, processed_count);
            
            Ok(Json(success(serde_json::json!({
                "message": "Expired subscriptions processed successfully",
                "processed_count": processed_count
            }))))
        }
        Err(e) => {
            tracing::error!("Failed to process expired subscriptions: {}", e);
            Err(AppError::internal(&format!("Failed to process expired subscriptions: {}", e)))
        }
    }
}

/// Create user subscription routes
pub fn user_subscription_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/", get(get_user_subscriptions))
        .route("/:id", get(get_user_subscription))
        .route("/:id/renew", post(renew_user_subscription))
        .route("/validation", get(validate_user_subscription))
}

/// Create admin subscription routes
pub fn admin_subscription_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/", get(get_all_subscriptions).post(create_subscription))
        .route("/:id", get(get_subscription).put(update_subscription))
        .route("/:id/renew", post(renew_subscription))
        .route("/:id/suspend", patch(suspend_subscription))
        .route("/:id/resume", patch(resume_subscription))
        .route("/:id/cancel", patch(cancel_subscription))
        .route("/process-expired", post(process_expired_subscriptions))
}