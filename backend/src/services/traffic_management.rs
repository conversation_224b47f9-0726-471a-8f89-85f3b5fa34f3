use std::sync::Arc;
use std::time::Duration;
use tokio::time;
use reqwest::Client;
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use tracing::{info, error, warn};
use chrono::{Datelike, Timelike};

use crate::repositories::{SubscriptionRepository, Repository};
use crate::entities::subscriptions::{self, ActiveModel};
use sea_orm::{ActiveModelTrait, Set};

#[derive(Debug, Serialize, Deserialize)]
pub struct TrafficResponse {
    pub user_id: String,
    pub bytes_uploaded: i64,
    pub bytes_downloaded: i64,
    pub last_updated: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TrafficConfig {
    pub api_url: String,
    pub sync_interval_minutes: u64,
    pub timeout_seconds: u64,
}

impl Default for TrafficConfig {
    fn default() -> Self {
        Self {
            api_url: std::env::var("TRAFFIC_API_URL")
                .unwrap_or_else(|_| "http://localhost:8080".to_string()),
            sync_interval_minutes: std::env::var("TRAFFIC_SYNC_INTERVAL")
                .unwrap_or_else(|_| "5".to_string())
                .parse()
                .unwrap_or(5),
            timeout_seconds: std::env::var("TRAFFIC_TIMEOUT")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
        }
    }
}

pub struct TrafficManagementService {
    client: Client,
    config: TrafficConfig,
    db: Arc<DatabaseConnection>,
    subscription_repo: SubscriptionRepository,
}

impl TrafficManagementService {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        let config = TrafficConfig::default();
        let client = Client::builder()
            .timeout(Duration::from_secs(config.timeout_seconds))
            .build()
            .expect("Failed to create HTTP client");

        let subscription_repo = SubscriptionRepository::new(db.clone());

        Self {
            client,
            config,
            db,
            subscription_repo,
        }
    }

    pub fn new_with_config(db: Arc<DatabaseConnection>, config: TrafficConfig) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.timeout_seconds))
            .build()
            .expect("Failed to create HTTP client");

        let subscription_repo = SubscriptionRepository::new(db.clone());

        Self {
            client,
            config,
            db,
            subscription_repo,
        }
    }

    /// 启动流量同步任务
    pub async fn start_sync_task(&self) {
        let mut interval = time::interval(Duration::from_secs(self.config.sync_interval_minutes * 60));
        
        info!("Starting traffic sync task with interval: {} minutes", self.config.sync_interval_minutes);
        
        loop {
            interval.tick().await;
            
            if let Err(e) = self.sync_all_user_traffic().await {
                error!("Failed to sync user traffic: {}", e);
            }
        }
    }

    /// 启动智能流量重置任务（基于订阅billing cycle）
    pub async fn start_monthly_reset_task(&self) {
        let mut interval = time::interval(Duration::from_secs(3600)); // 检查间隔：每小时
        
        info!("Starting intelligent traffic reset task based on billing cycles");
        
        loop {
            interval.tick().await;
            
            match self.process_billing_cycle_resets().await {
                Ok(count) => {
                    if count > 0 {
                        info!("Billing cycle reset completed. Reset {} subscriptions", count);
                    }
                }
                Err(e) => {
                    error!("Failed to process billing cycle resets: {}", e);
                }
            }
        }
    }

    /// 处理基于billing cycle的流量重置
    pub async fn process_billing_cycle_resets(&self) -> Result<u32, Box<dyn std::error::Error>> {
        let active_subscriptions = self.subscription_repo
            .find_active_subscriptions()
            .await
            .map_err(|e| format!("Failed to get active subscriptions: {}", e))?;

        let mut reset_count = 0;
        let now = chrono::Utc::now();

        for subscription in active_subscriptions {
            if self.should_reset_subscription_traffic(&subscription, now).await? {
                match self.reset_subscription_traffic(subscription.id).await {
                    Ok(_) => {
                        reset_count += 1;
                        info!("Reset traffic for subscription {} (billing cycle)", subscription.id);
                    }
                    Err(e) => {
                        warn!("Failed to reset traffic for subscription {}: {}", subscription.id, e);
                    }
                }
            }
        }

        Ok(reset_count)
    }

    /// 检查订阅是否需要进行流量重置
    async fn should_reset_subscription_traffic(
        &self,
        subscription: &crate::entities::subscriptions::Model,
        now: chrono::DateTime<chrono::Utc>,
    ) -> Result<bool, Box<dyn std::error::Error>> {
        // 获取订阅的计划信息以确定billing cycle
        let plan_repo = crate::repositories::PlanRepository::new(self.db.clone());
        let plan = plan_repo.find_by_id(subscription.plan_id).await?
            .ok_or("Plan not found")?;

        // 计算下一个billing cycle重置日期
        let next_reset_date = self.calculate_next_billing_cycle_reset(subscription, &plan)?;
        
        // 检查上次流量同步时间，避免重复重置
        let last_sync: chrono::DateTime<chrono::Utc> = subscription.last_traffic_sync.into();
        let time_since_last_sync = now - last_sync;
        
        // 如果距离上次同步时间小于23小时，不重置（避免重复）
        if time_since_last_sync.num_hours() < 23 {
            return Ok(false);
        }

        // 检查是否到了重置时间（允许1小时的误差）
        let time_to_reset = next_reset_date - now;
        Ok(time_to_reset.num_hours().abs() <= 1)
    }

    /// 计算下一个billing cycle重置日期
    fn calculate_next_billing_cycle_reset(
        &self,
        subscription: &crate::entities::subscriptions::Model,
        plan: &crate::entities::plans::Model,
    ) -> Result<chrono::DateTime<chrono::Utc>, Box<dyn std::error::Error>> {
        let start_date: chrono::DateTime<chrono::Utc> = subscription.start_date.into();
        let now = chrono::Utc::now();
        
        // 根据计划duration_days确定billing cycle类型
        let cycle_days = plan.duration_days;
        
        // 计算从订阅开始到现在经过了多少个完整的billing cycle
        let days_since_start = (now - start_date).num_days();
        let completed_cycles = days_since_start / cycle_days as i64;
        
        // 计算下一个billing cycle的开始时间
        let next_cycle_start = start_date + chrono::Duration::days((completed_cycles + 1) * cycle_days as i64);
        
        Ok(next_cycle_start)
    }

    /// 获取订阅的billing cycle信息
    pub async fn get_subscription_billing_info(&self, subscription_id: Uuid) -> Result<BillingCycleInfo, Box<dyn std::error::Error>> {
        let subscription = self.subscription_repo
            .find_by_id(subscription_id)
            .await?
            .ok_or("Subscription not found")?;

        let plan_repo = crate::repositories::PlanRepository::new(self.db.clone());
        let plan = plan_repo.find_by_id(subscription.plan_id).await?
            .ok_or("Plan not found")?;

        let start_date: chrono::DateTime<chrono::Utc> = subscription.start_date.into();
        let now = chrono::Utc::now();
        
        let cycle_days = plan.duration_days;
        let days_since_start = (now - start_date).num_days();
        let completed_cycles = days_since_start / cycle_days as i64;
        let current_cycle_start = start_date + chrono::Duration::days(completed_cycles * cycle_days as i64);
        let next_cycle_start = start_date + chrono::Duration::days((completed_cycles + 1) * cycle_days as i64);
        
        let days_until_reset = (next_cycle_start - now).num_days();
        
        Ok(BillingCycleInfo {
            subscription_id,
            plan_name: plan.name,
            billing_cycle_days: cycle_days,
            current_cycle_start,
            next_cycle_start,
            days_until_reset,
            completed_cycles: completed_cycles as u32,
            last_traffic_sync: subscription.last_traffic_sync.into(),
        })
    }

    /// 强制重置指定订阅的billing cycle
    pub async fn force_billing_cycle_reset(&self, subscription_id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        // 重置流量
        self.reset_subscription_traffic(subscription_id).await?;
        
        // 更新billing cycle信息
        let updated_subscription = ActiveModel {
            id: Set(subscription_id),
            last_traffic_sync: Set(chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())),
            ..Default::default()
        };

        self.subscription_repo
            .update(subscription_id, updated_subscription)
            .await?;

        info!("Force reset billing cycle for subscription {}", subscription_id);
        Ok(())
    }

    /// 同步所有用户流量
    pub async fn sync_all_user_traffic(&self) -> Result<(), Box<dyn std::error::Error>> {
        info!("Starting traffic sync for all users");
        
        // 获取所有活跃订阅
        let active_subscriptions = self.subscription_repo.find_active_subscriptions().await
            .map_err(|e| format!("Failed to get active subscriptions: {}", e))?;

        let mut success_count = 0;
        let mut error_count = 0;

        for subscription in active_subscriptions {
            match self.sync_user_traffic(subscription.user_id).await {
                Ok(_) => success_count += 1,
                Err(e) => {
                    error_count += 1;
                    warn!("Failed to sync traffic for user {}: {}", subscription.user_id, e);
                }
            }
        }

        info!("Traffic sync completed. Success: {}, Errors: {}", success_count, error_count);
        Ok(())
    }

    /// 同步单个用户流量
    pub async fn sync_user_traffic(&self, user_id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        // 从外部API获取流量数据
        let traffic_data = self.get_user_traffic_from_api(user_id).await?;
        
        // 更新数据库中的订阅流量信息
        self.update_subscription_traffic(user_id, &traffic_data).await?;
        
        Ok(())
    }

    /// 从外部API获取用户流量数据
    async fn get_user_traffic_from_api(&self, user_id: Uuid) -> Result<TrafficResponse, Box<dyn std::error::Error>> {
        let url = format!("{}/api/traffic/{}", self.config.api_url, user_id);
        
        let response = self.client
            .get(&url)
            .send()
            .await
            .map_err(|e| format!("HTTP request failed: {}", e))?;

        if !response.status().is_success() {
            return Err(format!("API returned status: {}", response.status()).into());
        }

        let traffic_data: TrafficResponse = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        Ok(traffic_data)
    }

    /// 更新订阅流量信息
    async fn update_subscription_traffic(&self, user_id: Uuid, traffic_data: &TrafficResponse) -> Result<(), Box<dyn std::error::Error>> {
        // 获取用户的活跃订阅
        let active_subscriptions = self.subscription_repo
            .find_active_by_user_id(user_id)
            .await
            .map_err(|e| format!("Failed to get user subscriptions: {}", e))?;

        if active_subscriptions.is_empty() {
            warn!("No active subscriptions found for user {}", user_id);
            return Ok(());
        }

        // 计算总流量（上传+下载）并转换为GB
        let total_traffic_bytes = traffic_data.bytes_uploaded + traffic_data.bytes_downloaded;
        let total_traffic_gb = rust_decimal::Decimal::from(total_traffic_bytes) / rust_decimal::Decimal::from(1024 * 1024 * 1024);

        // 更新所有活跃订阅的流量使用情况
        for subscription in active_subscriptions {
            let updated_subscription = ActiveModel {
                id: Set(subscription.id),
                used_traffic_gb: Set(total_traffic_gb),
                last_traffic_sync: Set(chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())),
                ..Default::default()
            };

            self.subscription_repo
                .update(subscription.id, updated_subscription)
                .await
                .map_err(|e| format!("Failed to update subscription {}: {}", subscription.id, e))?;

            info!("Updated traffic for user {} subscription {}: {} GB", 
                  user_id, subscription.id, total_traffic_gb);
        }

        Ok(())
    }

    /// 检查用户是否超过流量限制
    pub async fn check_user_traffic_limit(&self, user_id: Uuid) -> Result<bool, Box<dyn std::error::Error>> {
        let active_subscriptions = self.subscription_repo
            .find_active_by_user_id(user_id)
            .await
            .map_err(|e| format!("Failed to get user subscriptions: {}", e))?;

        for subscription in active_subscriptions {
            if subscription.is_traffic_exceeded() {
                return Ok(true);
            }
        }

        Ok(false)
    }

    /// 获取用户流量使用情况
    pub async fn get_user_traffic_usage(&self, user_id: Uuid) -> Result<Vec<TrafficUsage>, Box<dyn std::error::Error>> {
        let active_subscriptions = self.subscription_repo
            .find_active_by_user_id(user_id)
            .await
            .map_err(|e| format!("Failed to get user subscriptions: {}", e))?;

        let mut usage_list = Vec::new();

        for subscription in active_subscriptions {
            let usage = TrafficUsage {
                subscription_id: subscription.id,
                total_traffic_gb: subscription.total_traffic_gb,
                used_traffic_gb: subscription.used_traffic_gb,
                remaining_traffic_gb: subscription.remaining_traffic_gb(),
                usage_percentage: subscription.traffic_usage_percentage(),
                is_exceeded: subscription.is_traffic_exceeded(),
                last_sync: subscription.last_traffic_sync,
            };
            usage_list.push(usage);
        }

        Ok(usage_list)
    }

    /// 重置用户流量使用情况
    pub async fn reset_user_traffic(&self, user_id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        let active_subscriptions = self.subscription_repo
            .find_active_by_user_id(user_id)
            .await
            .map_err(|e| format!("Failed to get user subscriptions: {}", e))?;

        for subscription in active_subscriptions {
            let updated_subscription = ActiveModel {
                id: Set(subscription.id),
                used_traffic_gb: Set(rust_decimal::Decimal::ZERO),
                last_traffic_sync: Set(chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())),
                ..Default::default()
            };

            self.subscription_repo
                .update(subscription.id, updated_subscription)
                .await
                .map_err(|e| format!("Failed to reset traffic for subscription {}: {}", subscription.id, e))?;

            info!("Reset traffic for user {} subscription {}", user_id, subscription.id);
        }

        Ok(())
    }

    /// 重置指定订阅的流量使用情况
    pub async fn reset_subscription_traffic(&self, subscription_id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        let subscription = self.subscription_repo
            .find_by_id(subscription_id)
            .await
            .map_err(|e| format!("Failed to get subscription: {}", e))?
            .ok_or("Subscription not found")?;

        let updated_subscription = ActiveModel {
            id: Set(subscription.id),
            used_traffic_gb: Set(rust_decimal::Decimal::ZERO),
            last_traffic_sync: Set(chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())),
            ..Default::default()
        };

        self.subscription_repo
            .update(subscription.id, updated_subscription)
            .await
            .map_err(|e| format!("Failed to reset traffic for subscription {}: {}", subscription.id, e))?;

        info!("Reset traffic for subscription {}", subscription_id);
        Ok(())
    }

    /// 重置所有用户的流量使用情况（通常用于月度重置）
    pub async fn reset_all_traffic(&self) -> Result<u32, Box<dyn std::error::Error>> {
        let active_subscriptions = self.subscription_repo
            .find_active_subscriptions()
            .await
            .map_err(|e| format!("Failed to get active subscriptions: {}", e))?;

        let mut reset_count = 0;

        for subscription in active_subscriptions {
            let updated_subscription = ActiveModel {
                id: Set(subscription.id),
                used_traffic_gb: Set(rust_decimal::Decimal::ZERO),
                last_traffic_sync: Set(chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())),
                ..Default::default()
            };

            match self.subscription_repo.update(subscription.id, updated_subscription).await {
                Ok(_) => {
                    reset_count += 1;
                    info!("Reset traffic for subscription {}", subscription.id);
                }
                Err(e) => {
                    warn!("Failed to reset traffic for subscription {}: {}", subscription.id, e);
                }
            }
        }

        info!("Reset traffic for {} subscriptions", reset_count);
        Ok(reset_count)
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TrafficUsage {
    pub subscription_id: Uuid,
    pub total_traffic_gb: rust_decimal::Decimal,
    pub used_traffic_gb: rust_decimal::Decimal,
    pub remaining_traffic_gb: rust_decimal::Decimal,
    pub usage_percentage: f64,
    pub is_exceeded: bool,
    pub last_sync: chrono::DateTime<chrono::FixedOffset>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BillingCycleInfo {
    pub subscription_id: Uuid,
    pub plan_name: String,
    pub billing_cycle_days: i32,
    pub current_cycle_start: chrono::DateTime<chrono::Utc>,
    pub next_cycle_start: chrono::DateTime<chrono::Utc>,
    pub days_until_reset: i64,
    pub completed_cycles: u32,
    pub last_traffic_sync: chrono::DateTime<chrono::Utc>,
}