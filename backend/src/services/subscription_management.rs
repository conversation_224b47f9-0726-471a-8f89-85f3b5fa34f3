use std::sync::Arc;
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use tracing::{info, warn, error};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;

use crate::repositories::{SubscriptionRepository, UserRepository, PlanRepository, Repository};
use crate::entities::{subscriptions::{self, ActiveModel}, enums::SubscriptionStatus, plans};
use sea_orm::{Set, ActiveModelTrait};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriptionInfo {
    pub id: Uuid,
    pub user_id: Uuid,
    pub plan_id: Uuid,
    pub plan_name: String,
    pub status: SubscriptionStatus,
    pub start_date: DateTime<Utc>,
    pub end_date: DateTime<Utc>,
    pub total_traffic_gb: Decimal,
    pub used_traffic_gb: Decimal,
    pub remaining_traffic_gb: Decimal,
    pub max_concurrent_devices: i32,
    pub current_devices_count: i32,
    pub auto_renew: bool,
    pub is_active: bool,
    pub is_expired: bool,
    pub is_traffic_exceeded: bool,
    pub days_remaining: i64,
    pub usage_percentage: f64,
    pub proxy_username: Option<String>,
    pub proxy_port: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SubscriptionValidationResult {
    pub is_valid: bool,
    pub can_access_vpn: bool,
    pub subscription_id: Option<Uuid>,
    pub error_message: Option<String>,
    pub remaining_traffic_gb: Option<Decimal>,
    pub days_remaining: Option<i64>,
}

#[derive(Debug, Deserialize)]
pub struct CreateSubscriptionRequest {
    pub user_id: Uuid,
    pub plan_id: Uuid,
    pub duration_days: i32,
    pub auto_renew: bool,
}

#[derive(Debug, Deserialize)]
pub struct UpdateSubscriptionRequest {
    pub status: Option<SubscriptionStatus>,
    pub end_date: Option<DateTime<Utc>>,
    pub auto_renew: Option<bool>,
    pub used_traffic_gb: Option<Decimal>,
    pub max_concurrent_devices: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct RenewSubscriptionRequest {
    pub duration_days: i32,
    pub plan_id: Option<Uuid>, // 可选择更换套餐
}

pub struct SubscriptionManagementService {
    db: Arc<DatabaseConnection>,
    subscription_repo: SubscriptionRepository,
    user_repo: UserRepository,
    plan_repo: PlanRepository,
}

impl SubscriptionManagementService {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        let subscription_repo = SubscriptionRepository::new(db.clone());
        let user_repo = UserRepository::new(db.clone());
        let plan_repo = PlanRepository::new(db.clone());
        
        Self {
            db,
            subscription_repo,
            user_repo,
            plan_repo,
        }
    }

    /// 验证用户订阅状态
    pub async fn validate_user_subscription(&self, user_id: Uuid) -> Result<SubscriptionValidationResult, Box<dyn std::error::Error>> {
        let active_subscriptions = self.subscription_repo
            .find_active_by_user_id(user_id)
            .await
            .map_err(|e| format!("Failed to get user subscriptions: {}", e))?;

        if active_subscriptions.is_empty() {
            return Ok(SubscriptionValidationResult {
                is_valid: false,
                can_access_vpn: false,
                subscription_id: None,
                error_message: Some("No active subscription found".to_string()),
                remaining_traffic_gb: None,
                days_remaining: None,
            });
        }

        // 找到最佳的订阅（优先级：未过期 > 流量未耗尽 > 结束日期最晚）
        let best_subscription = active_subscriptions
            .iter()
            .filter(|s| s.is_active())
            .max_by(|a, b| {
                // 先比较是否过期
                let a_expired = a.is_expired();
                let b_expired = b.is_expired();
                if a_expired != b_expired {
                    return b_expired.cmp(&a_expired);
                }
                
                // 再比较流量是否耗尽
                let a_traffic_exceeded = a.is_traffic_exceeded();
                let b_traffic_exceeded = b.is_traffic_exceeded();
                if a_traffic_exceeded != b_traffic_exceeded {
                    return b_traffic_exceeded.cmp(&a_traffic_exceeded);
                }
                
                // 最后比较结束时间
                a.end_date.cmp(&b.end_date)
            });

        if let Some(subscription) = best_subscription {
            let can_access = subscription.can_connect();
            
            Ok(SubscriptionValidationResult {
                is_valid: true,
                can_access_vpn: can_access,
                subscription_id: Some(subscription.id),
                error_message: if !can_access {
                    Some(subscription.status_display())
                } else {
                    None
                },
                remaining_traffic_gb: Some(subscription.remaining_traffic_gb()),
                days_remaining: Some(subscription.remaining_days()),
            })
        } else {
            Ok(SubscriptionValidationResult {
                is_valid: false,
                can_access_vpn: false,
                subscription_id: None,
                error_message: Some("No valid subscription found".to_string()),
                remaining_traffic_gb: None,
                days_remaining: None,
            })
        }
    }

    /// 获取用户的所有订阅信息
    pub async fn get_user_subscriptions(&self, user_id: Uuid) -> Result<Vec<SubscriptionInfo>, Box<dyn std::error::Error>> {
        let subscriptions = self.subscription_repo
            .find_by_user_id(user_id)
            .await
            .map_err(|e| format!("Failed to get user subscriptions: {}", e))?;

        let mut subscription_infos = Vec::new();

        for subscription in subscriptions {
            let plan = self.plan_repo
                .find_by_id(subscription.plan_id)
                .await
                .map_err(|e| format!("Failed to get plan info: {}", e))?
                .unwrap_or_else(|| plans::Model::default());

            let subscription_info = SubscriptionInfo {
                id: subscription.id,
                user_id: subscription.user_id,
                plan_id: subscription.plan_id,
                plan_name: plan.name,
                status: subscription.status.clone(),
                start_date: subscription.start_date.into(),
                end_date: subscription.end_date.into(),
                total_traffic_gb: subscription.total_traffic_gb,
                used_traffic_gb: subscription.used_traffic_gb,
                remaining_traffic_gb: subscription.remaining_traffic_gb(),
                max_concurrent_devices: subscription.max_concurrent_devices,
                current_devices_count: subscription.current_devices_count,
                auto_renew: subscription.auto_renew,
                is_active: subscription.is_active(),
                is_expired: subscription.is_expired(),
                is_traffic_exceeded: subscription.is_traffic_exceeded(),
                days_remaining: subscription.remaining_days(),
                usage_percentage: subscription.traffic_usage_percentage(),
                proxy_username: subscription.proxy_username.clone(),
                proxy_port: subscription.proxy_port,
            };

            subscription_infos.push(subscription_info);
        }

        Ok(subscription_infos)
    }

    /// 获取单个订阅信息
    pub async fn get_subscription(&self, subscription_id: Uuid) -> Result<SubscriptionInfo, Box<dyn std::error::Error>> {
        let subscription = self.subscription_repo
            .find_by_id(subscription_id)
            .await
            .map_err(|e| format!("Failed to get subscription: {}", e))?
            .ok_or("Subscription not found")?;

        let plan = self.plan_repo
            .find_by_id(subscription.plan_id)
            .await
            .map_err(|e| format!("Failed to get plan info: {}", e))?
            .unwrap_or_default();

        let subscription_info = SubscriptionInfo {
            id: subscription.id,
            user_id: subscription.user_id,
            plan_id: subscription.plan_id,
            plan_name: plan.name,
            status: subscription.status.clone(),
            start_date: subscription.start_date.into(),
            end_date: subscription.end_date.into(),
            total_traffic_gb: subscription.total_traffic_gb,
            used_traffic_gb: subscription.used_traffic_gb,
            remaining_traffic_gb: subscription.remaining_traffic_gb(),
            max_concurrent_devices: subscription.max_concurrent_devices,
            current_devices_count: subscription.current_devices_count,
            auto_renew: subscription.auto_renew,
            is_active: subscription.is_active(),
            is_expired: subscription.is_expired(),
            is_traffic_exceeded: subscription.is_traffic_exceeded(),
            days_remaining: subscription.remaining_days(),
            usage_percentage: subscription.traffic_usage_percentage(),
            proxy_username: subscription.proxy_username.clone(),
            proxy_port: subscription.proxy_port,
        };

        Ok(subscription_info)
    }

    /// 创建新订阅
    pub async fn create_subscription(&self, request: CreateSubscriptionRequest) -> Result<SubscriptionInfo, Box<dyn std::error::Error>> {
        // 验证用户存在
        let _user = self.user_repo
            .find_by_id(request.user_id)
            .await
            .map_err(|e| format!("Failed to get user: {}", e))?
            .ok_or("User not found")?;

        // 验证套餐存在
        let plan = self.plan_repo
            .find_by_id(request.plan_id)
            .await
            .map_err(|e| format!("Failed to get plan: {}", e))?
            .ok_or("Plan not found")?;

        let now = Utc::now();
        let end_date = now + chrono::Duration::days(request.duration_days as i64);

        // 生成代理凭证
        let proxy_username = format!("user_{}", request.user_id.to_string().replace("-", "")[..8].to_lowercase());
        let proxy_password = format!("pass_{}", uuid::Uuid::new_v4().to_string().replace("-", "")[..12].to_lowercase());
        let proxy_port = 1080; // 默认端口

        let new_subscription = ActiveModel {
            id: Set(Uuid::new_v4()),
            user_id: Set(request.user_id),
            plan_id: Set(request.plan_id),
            start_date: Set(now.into()),
            end_date: Set(end_date.into()),
            total_traffic_gb: Set(plan.traffic_limit_gb.unwrap_or(Decimal::new(10, 0))),
            used_traffic_gb: Set(Decimal::ZERO),
            max_concurrent_devices: Set(plan.max_concurrent_devices),
            current_devices_count: Set(0),
            status: Set(SubscriptionStatus::Active),
            auto_renew: Set(request.auto_renew),
            auto_renew_plan_id: Set(Some(request.plan_id)),
            proxy_username: Set(Some(proxy_username)),
            proxy_password: Set(Some(proxy_password)),
            proxy_port: Set(Some(proxy_port)),
            proxy_config_hash: Set(None),
            created_at: Set(now.into()),
            updated_at: Set(now.into()),
            last_traffic_sync: Set(now.into()),
            total_connections_count: Set(0),
            total_session_time: Set(0),
        };

        let subscription = self.subscription_repo
            .create(new_subscription)
            .await
            .map_err(|e| format!("Failed to create subscription: {}", e))?;

        info!("Created subscription {} for user {}", subscription.id, request.user_id);

        // 返回创建的订阅信息
        self.get_subscription(subscription.id).await
    }

    /// 更新订阅
    pub async fn update_subscription(&self, subscription_id: Uuid, request: UpdateSubscriptionRequest) -> Result<SubscriptionInfo, Box<dyn std::error::Error>> {
        let existing_subscription = self.subscription_repo
            .find_by_id(subscription_id)
            .await
            .map_err(|e| format!("Failed to get subscription: {}", e))?
            .ok_or("Subscription not found")?;

        let updated_subscription = ActiveModel {
            id: Set(subscription_id),
            status: Set(request.status.unwrap_or(existing_subscription.status)),
            end_date: Set(request.end_date.map(|d| d.into()).unwrap_or(existing_subscription.end_date)),
            auto_renew: Set(request.auto_renew.unwrap_or(existing_subscription.auto_renew)),
            used_traffic_gb: Set(request.used_traffic_gb.unwrap_or(existing_subscription.used_traffic_gb)),
            max_concurrent_devices: Set(request.max_concurrent_devices.unwrap_or(existing_subscription.max_concurrent_devices)),
            updated_at: Set(Utc::now().into()),
            ..Default::default()
        };

        self.subscription_repo
            .update(subscription_id, updated_subscription)
            .await
            .map_err(|e| format!("Failed to update subscription: {}", e))?;

        info!("Updated subscription {}", subscription_id);

        // 返回更新后的订阅信息
        self.get_subscription(subscription_id).await
    }

    /// 续订订阅
    pub async fn renew_subscription(&self, subscription_id: Uuid, request: RenewSubscriptionRequest) -> Result<SubscriptionInfo, Box<dyn std::error::Error>> {
        let existing_subscription = self.subscription_repo
            .find_by_id(subscription_id)
            .await
            .map_err(|e| format!("Failed to get subscription: {}", e))?
            .ok_or("Subscription not found")?;

        let plan_id = request.plan_id.unwrap_or(existing_subscription.plan_id);
        let plan = self.plan_repo
            .find_by_id(plan_id)
            .await
            .map_err(|e| format!("Failed to get plan: {}", e))?
            .ok_or("Plan not found")?;

        // 计算新的结束日期
        let current_end_date: DateTime<Utc> = existing_subscription.end_date.into();
        let now = Utc::now();
        let new_end_date = std::cmp::max(current_end_date, now) + chrono::Duration::days(request.duration_days as i64);

        let updated_subscription = ActiveModel {
            id: Set(subscription_id),
            plan_id: Set(plan_id),
            end_date: Set(new_end_date.into()),
            total_traffic_gb: Set(plan.traffic_limit_gb.unwrap_or(Decimal::new(10, 0))),
            used_traffic_gb: Set(Decimal::ZERO), // 续订时重置流量
            max_concurrent_devices: Set(plan.max_concurrent_devices),
            status: Set(SubscriptionStatus::Active),
            updated_at: Set(Utc::now().into()),
            last_traffic_sync: Set(Utc::now().into()),
            ..Default::default()
        };

        self.subscription_repo
            .update(subscription_id, updated_subscription)
            .await
            .map_err(|e| format!("Failed to renew subscription: {}", e))?;

        info!("Renewed subscription {} for {} days", subscription_id, request.duration_days);

        // 返回续订后的订阅信息
        self.get_subscription(subscription_id).await
    }

    /// 暂停订阅
    pub async fn suspend_subscription(&self, subscription_id: Uuid) -> Result<SubscriptionInfo, Box<dyn std::error::Error>> {
        let request = UpdateSubscriptionRequest {
            status: Some(SubscriptionStatus::Suspended),
            end_date: None,
            auto_renew: None,
            used_traffic_gb: None,
            max_concurrent_devices: None,
        };

        self.update_subscription(subscription_id, request).await
    }

    /// 恢复订阅
    pub async fn resume_subscription(&self, subscription_id: Uuid) -> Result<SubscriptionInfo, Box<dyn std::error::Error>> {
        let request = UpdateSubscriptionRequest {
            status: Some(SubscriptionStatus::Active),
            end_date: None,
            auto_renew: None,
            used_traffic_gb: None,
            max_concurrent_devices: None,
        };

        self.update_subscription(subscription_id, request).await
    }

    /// 取消订阅
    pub async fn cancel_subscription(&self, subscription_id: Uuid) -> Result<SubscriptionInfo, Box<dyn std::error::Error>> {
        let request = UpdateSubscriptionRequest {
            status: Some(SubscriptionStatus::Cancelled),
            auto_renew: Some(false),
            end_date: None,
            used_traffic_gb: None,
            max_concurrent_devices: None,
        };

        self.update_subscription(subscription_id, request).await
    }

    /// 获取即将过期的订阅
    pub async fn get_expiring_subscriptions(&self, days_before: i32) -> Result<Vec<SubscriptionInfo>, Box<dyn std::error::Error>> {
        let active_subscriptions = self.subscription_repo
            .find_active_subscriptions()
            .await
            .map_err(|e| format!("Failed to get active subscriptions: {}", e))?;

        let mut expiring_subscriptions = Vec::new();

        for subscription in active_subscriptions {
            let days_remaining = subscription.remaining_days();
            if days_remaining <= days_before as i64 && days_remaining > 0 {
                let subscription_info = self.get_subscription(subscription.id).await?;
                expiring_subscriptions.push(subscription_info);
            }
        }

        Ok(expiring_subscriptions)
    }

    /// 处理过期订阅
    pub async fn handle_expired_subscriptions(&self) -> Result<u32, Box<dyn std::error::Error>> {
        let expired_subscriptions = self.subscription_repo
            .find_expired()
            .await
            .map_err(|e| format!("Failed to get expired subscriptions: {}", e))?;

        let mut processed_count = 0;

        for subscription in expired_subscriptions {
            let updated_subscription = ActiveModel {
                id: Set(subscription.id),
                status: Set(SubscriptionStatus::Expired),
                updated_at: Set(Utc::now().into()),
                ..Default::default()
            };

            match self.subscription_repo.update(subscription.id, updated_subscription).await {
                Ok(_) => {
                    processed_count += 1;
                    info!("Marked subscription {} as expired", subscription.id);
                }
                Err(e) => {
                    error!("Failed to update expired subscription {}: {}", subscription.id, e);
                }
            }
        }

        info!("Processed {} expired subscriptions", processed_count);
        Ok(processed_count)
    }

    /// 获取所有订阅（管理员用）
    pub async fn get_all_subscriptions(&self) -> Result<Vec<SubscriptionInfo>, Box<dyn std::error::Error>> {
        let subscriptions = self.subscription_repo
            .find_all()
            .await
            .map_err(|e| format!("Failed to get all subscriptions: {}", e))?;

        let mut subscription_infos = Vec::new();

        for subscription in subscriptions {
            let plan = self.plan_repo
                .find_by_id(subscription.plan_id)
                .await
                .map_err(|e| format!("Failed to get plan info: {}", e))?
                .unwrap_or_else(|| plans::Model::default());

            let subscription_info = SubscriptionInfo {
                id: subscription.id,
                user_id: subscription.user_id,
                plan_id: subscription.plan_id,
                plan_name: plan.name,
                status: subscription.status.clone(),
                start_date: subscription.start_date.into(),
                end_date: subscription.end_date.into(),
                total_traffic_gb: subscription.total_traffic_gb,
                used_traffic_gb: subscription.used_traffic_gb,
                remaining_traffic_gb: subscription.remaining_traffic_gb(),
                max_concurrent_devices: subscription.max_concurrent_devices,
                current_devices_count: subscription.current_devices_count,
                auto_renew: subscription.auto_renew,
                is_active: subscription.is_active(),
                is_expired: subscription.is_expired(),
                is_traffic_exceeded: subscription.is_traffic_exceeded(),
                days_remaining: subscription.remaining_days(),
                usage_percentage: subscription.traffic_usage_percentage(),
                proxy_username: subscription.proxy_username.clone(),
                proxy_port: subscription.proxy_port,
            };

            subscription_infos.push(subscription_info);
        }

        Ok(subscription_infos)
    }
}