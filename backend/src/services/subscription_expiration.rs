use std::sync::Arc;
use std::time::Duration;
use tokio::time;
use sea_orm::{DatabaseConnection, TransactionTrait};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use tracing::{info, error, warn};

use crate::repositories::{SubscriptionRepository, UserRepository, ConnectionLogRepository, Repository};
use crate::entities::subscriptions::{self, ActiveModel};
use crate::entities::users::ActiveModel as UserActiveModel;
use crate::entities::enums::SubscriptionStatus;
use crate::services::user_management::UserManagementService;
use sea_orm::{ActiveModelTrait, Set};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ExpirationConfig {
    pub grace_period_days: i32,
    pub cleanup_warning_days: i32,
    pub final_cleanup_days: i32,
    pub notification_enabled: bool,
    pub data_retention_days: i32,
}

impl Default for ExpirationConfig {
    fn default() -> Self {
        Self {
            grace_period_days: std::env::var("SUBSCRIPTION_GRACE_PERIOD_DAYS")
                .unwrap_or_else(|_| "7".to_string())
                .parse()
                .unwrap_or(7),
            cleanup_warning_days: std::env::var("CLEANUP_WARNING_DAYS")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
            final_cleanup_days: std::env::var("FINAL_CLEANUP_DAYS")
                .unwrap_or_else(|_| "90".to_string())
                .parse()
                .unwrap_or(90),
            notification_enabled: std::env::var("EXPIRATION_NOTIFICATIONS")
                .unwrap_or_else(|_| "true".to_string())
                .parse()
                .unwrap_or(true),
            data_retention_days: std::env::var("DATA_RETENTION_DAYS")
                .unwrap_or_else(|_| "365".to_string())
                .parse()
                .unwrap_or(365),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExpirationProcessResult {
    pub expired_subscriptions: u32,
    pub deactivated_users: u32,
    pub cleaned_up_users: u32,
    pub notifications_sent: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CleanupReport {
    pub total_subscriptions_processed: u32,
    pub expired_subscriptions: u32,
    pub users_deactivated: u32,
    pub users_cleaned_up: u32,
    pub data_archived: u32,
    pub processing_time_ms: u64,
}

pub struct SubscriptionExpirationService {
    db: Arc<DatabaseConnection>,
    subscription_repo: SubscriptionRepository,
    user_repo: UserRepository,
    connection_log_repo: ConnectionLogRepository,
    user_management_service: UserManagementService,
    config: ExpirationConfig,
}

impl SubscriptionExpirationService {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        let subscription_repo = SubscriptionRepository::new(db.clone());
        let user_repo = UserRepository::new(db.clone());
        let connection_log_repo = ConnectionLogRepository::new(db.clone());
        let user_management_service = UserManagementService::new(db.clone());
        let config = ExpirationConfig::default();

        Self {
            db,
            subscription_repo,
            user_repo,
            connection_log_repo,
            user_management_service,
            config,
        }
    }

    pub fn new_with_config(db: Arc<DatabaseConnection>, config: ExpirationConfig) -> Self {
        let subscription_repo = SubscriptionRepository::new(db.clone());
        let user_repo = UserRepository::new(db.clone());
        let connection_log_repo = ConnectionLogRepository::new(db.clone());
        let user_management_service = UserManagementService::new(db.clone());

        Self {
            db,
            subscription_repo,
            user_repo,
            connection_log_repo,
            user_management_service,
            config,
        }
    }

    /// 启动定期订阅过期处理任务
    pub async fn start_expiration_task(&self) {
        let mut interval = time::interval(Duration::from_secs(3600 * 6)); // 每6小时检查一次
        
        info!("Starting subscription expiration task. Checking every 6 hours");
        
        loop {
            interval.tick().await;
            
            match self.process_all_expirations().await {
                Ok(result) => {
                    info!("Expiration processing completed. Expired: {}, Deactivated: {}, Cleaned up: {}, Notifications: {}", 
                          result.expired_subscriptions, result.deactivated_users, result.cleaned_up_users, result.notifications_sent);
                }
                Err(e) => {
                    error!("Failed to process subscription expirations: {}", e);
                }
            }
        }
    }

    /// 处理所有过期相关任务
    pub async fn process_all_expirations(&self) -> Result<ExpirationProcessResult, Box<dyn std::error::Error + Send + Sync>> {
        let start_time = std::time::Instant::now();
        
        // 1. 处理过期的订阅
        let expired_count = self.process_expired_subscriptions().await?;
        
        // 2. 处理需要停用的用户
        let deactivated_count = self.process_user_deactivation().await?;
        
        // 3. 处理需要清理的用户
        let cleaned_up_count = self.process_user_cleanup().await?;
        
        // 4. 发送通知（如果启用）
        let notifications_sent = if self.config.notification_enabled {
            self.send_expiration_notifications().await?
        } else {
            0
        };
        
        let processing_time = start_time.elapsed();
        info!("Expiration processing completed in {:?}", processing_time);
        
        Ok(ExpirationProcessResult {
            expired_subscriptions: expired_count,
            deactivated_users: deactivated_count,
            cleaned_up_users: cleaned_up_count,
            notifications_sent,
        })
    }

    /// 处理过期的订阅
    async fn process_expired_subscriptions(&self) -> Result<u32, Box<dyn std::error::Error + Send + Sync>> {
        let now = chrono::Utc::now();
        let mut expired_count = 0;
        
        // 查找所有活跃但已过期的订阅
        let active_subscriptions = self.subscription_repo.find_active_subscriptions().await?;
        
        for subscription in active_subscriptions {
            let end_date: chrono::DateTime<chrono::Utc> = subscription.end_date.into();
            
            // 检查是否已过期
            if now > end_date {
                // 更新订阅状态为过期
                let updated_subscription = ActiveModel {
                    id: Set(subscription.id),
                    status: Set(SubscriptionStatus::Expired),
                    updated_at: Set(now.with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())),
                    ..Default::default()
                };
                
                self.subscription_repo.update(subscription.id, updated_subscription).await?;
                
                // 从外部用户管理系统中暂停用户
                if let Err(e) = self.user_management_service.suspend_user(subscription.user_id).await {
                    warn!("Failed to suspend user {} in external system: {}", subscription.user_id, e);
                }
                
                expired_count += 1;
                info!("Expired subscription {} for user {}", subscription.id, subscription.user_id);
            }
        }
        
        Ok(expired_count)
    }

    /// 处理用户停用（在宽限期后）
    async fn process_user_deactivation(&self) -> Result<u32, Box<dyn std::error::Error + Send + Sync>> {
        let now = chrono::Utc::now();
        let grace_period = chrono::Duration::days(self.config.grace_period_days as i64);
        let cutoff_time = now - grace_period;
        
        let mut deactivated_count = 0;
        
        // 查找所有在宽限期后仍无活跃订阅的用户
        let users = self.user_repo.find_all().await?;
        
        for user in users {
            if user.is_admin {
                continue; // 跳过管理员账户
            }
            
            // 检查用户是否有活跃订阅
            let active_subscriptions = self.subscription_repo.find_active_by_user_id(user.id).await?;
            
            if active_subscriptions.is_empty() {
                // 检查最后一个订阅的过期时间
                let user_subscriptions = self.subscription_repo.find_by_user_id(user.id).await?;
                
                if let Some(last_subscription) = user_subscriptions.iter().max_by_key(|s| s.end_date) {
                    let end_date: chrono::DateTime<chrono::Utc> = last_subscription.end_date.into();
                    
                    if end_date < cutoff_time && user.is_active {
                        // 停用用户
                        let updated_user = UserActiveModel {
                            id: Set(user.id),
                            is_active: Set(false),
                            updated_at: Set(now.with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())),
                            ..Default::default()
                        };
                        
                        self.user_repo.update(user.id, updated_user).await?;
                        
                        // 从外部用户管理系统中移除用户
                        if let Err(e) = self.user_management_service.remove_user(user.id).await {
                            warn!("Failed to remove user {} from external system: {}", user.id, e);
                        }
                        
                        deactivated_count += 1;
                        info!("Deactivated user {} after grace period", user.id);
                    }
                }
            }
        }
        
        Ok(deactivated_count)
    }

    /// 处理用户数据清理
    async fn process_user_cleanup(&self) -> Result<u32, Box<dyn std::error::Error + Send + Sync>> {
        let now = chrono::Utc::now();
        let cleanup_period = chrono::Duration::days(self.config.final_cleanup_days as i64);
        let cutoff_time = now - cleanup_period;
        
        let mut cleaned_up_count = 0;
        
        // 查找长期未活跃的用户
        let inactive_users = self.user_repo.find_inactive_users().await?;
        
        for user in inactive_users {
            if user.is_admin {
                continue; // 跳过管理员账户
            }
            
            let updated_at: chrono::DateTime<chrono::Utc> = user.updated_at.into();
            
            if updated_at < cutoff_time {
                // 使用事务确保数据完整性
                let txn = self.db.begin().await?;
                
                match self.cleanup_user_data(&txn, user.id).await {
                    Ok(_) => {
                        txn.commit().await?;
                        cleaned_up_count += 1;
                        info!("Cleaned up user {} data", user.id);
                    }
                    Err(e) => {
                        txn.rollback().await?;
                        warn!("Failed to cleanup user {} data: {}", user.id, e);
                    }
                }
            }
        }
        
        Ok(cleaned_up_count)
    }

    /// 清理用户数据
    async fn cleanup_user_data(
        &self,
        txn: &sea_orm::DatabaseTransaction,
        user_id: Uuid,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 根据数据保留策略清理或归档数据
        let data_retention_period = chrono::Duration::days(self.config.data_retention_days as i64);
        let now = chrono::Utc::now();
        let retention_cutoff = now - data_retention_period;
        
        // 删除旧的连接日志
        let old_logs = self.connection_log_repo.find_by_user_id(user_id).await?;
        for log in old_logs {
            let created_at: chrono::DateTime<chrono::Utc> = log.connected_at.into();
            if created_at < retention_cutoff {
                self.connection_log_repo.delete(log.id).await?;
            }
        }
        
        // 删除过期的订阅记录（保留最近的订阅记录用于审计）
        let expired_subscriptions = self.subscription_repo.find_by_user_id(user_id).await?;
        for subscription in expired_subscriptions {
            if subscription.status == SubscriptionStatus::Expired {
                let updated_at: chrono::DateTime<chrono::Utc> = subscription.updated_at.into();
                if updated_at < retention_cutoff {
                    self.subscription_repo.delete(subscription.id).await?;
                }
            }
        }
        
        // 注意：不删除用户记录本身，只标记为已清理
        // 这样可以保留账户历史记录用于审计目的
        
        Ok(())
    }

    /// 发送过期通知
    async fn send_expiration_notifications(&self) -> Result<u32, Box<dyn std::error::Error + Send + Sync>> {
        let now = chrono::Utc::now();
        let warning_period = chrono::Duration::days(self.config.cleanup_warning_days as i64);
        let warning_cutoff = now + warning_period;
        
        let mut notifications_sent = 0;
        
        // 查找即将过期的订阅
        let active_subscriptions = self.subscription_repo.find_active_subscriptions().await?;
        
        for subscription in active_subscriptions {
            let end_date: chrono::DateTime<chrono::Utc> = subscription.end_date.into();
            
            // 检查是否需要发送过期警告
            if end_date < warning_cutoff && end_date > now {
                let days_until_expiry = (end_date - now).num_days();
                
                // 这里可以集成邮件服务或其他通知系统
                // 目前只记录日志
                info!("Subscription {} for user {} expires in {} days", 
                      subscription.id, subscription.user_id, days_until_expiry);
                
                notifications_sent += 1;
            }
        }
        
        Ok(notifications_sent)
    }

    /// 获取过期统计信息
    pub async fn get_expiration_stats(&self) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        let now = chrono::Utc::now();
        let warning_period = chrono::Duration::days(self.config.cleanup_warning_days as i64);
        let grace_period = chrono::Duration::days(self.config.grace_period_days as i64);
        
        let all_subscriptions = self.subscription_repo.find_all().await?;
        let all_users = self.user_repo.find_all().await?;
        
        let mut stats = serde_json::json!({
            "total_subscriptions": all_subscriptions.len(),
            "total_users": all_users.len(),
            "expired_subscriptions": 0,
            "expiring_soon": 0,
            "inactive_users": 0,
            "users_in_grace_period": 0,
            "users_due_for_cleanup": 0,
            "config": self.config
        });
        
        let mut expired_count = 0;
        let mut expiring_soon_count = 0;
        
        for subscription in all_subscriptions {
            let end_date: chrono::DateTime<chrono::Utc> = subscription.end_date.into();
            
            if end_date < now {
                expired_count += 1;
            } else if end_date < (now + warning_period) {
                expiring_soon_count += 1;
            }
        }
        
        stats["expired_subscriptions"] = expired_count.into();
        stats["expiring_soon"] = expiring_soon_count.into();
        stats["inactive_users"] = all_users.iter().filter(|u| !u.is_active).count().into();
        
        Ok(stats)
    }

    /// 手动触发过期处理
    pub async fn trigger_expiration_processing(&self) -> Result<ExpirationProcessResult, Box<dyn std::error::Error + Send + Sync>> {
        info!("Manually triggering expiration processing");
        self.process_all_expirations().await
    }

    /// 手动触发用户清理
    pub async fn trigger_user_cleanup(&self) -> Result<u32, Box<dyn std::error::Error + Send + Sync>> {
        info!("Manually triggering user cleanup");
        self.process_user_cleanup().await
    }
}