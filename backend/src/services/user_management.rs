use std::sync::Arc;
use std::time::Duration;
use reqwest::Client;
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use tracing::{info, error, warn};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserManagementConfig {
    pub api_url: String,
    pub api_key: String,
    pub timeout_seconds: u64,
    pub max_retries: u32,
    pub retry_delay_seconds: u64,
}

impl Default for UserManagementConfig {
    fn default() -> Self {
        Self {
            api_url: std::env::var("USER_MANAGEMENT_API_URL")
                .unwrap_or_else(|_| "http://localhost:8090".to_string()),
            api_key: std::env::var("USER_MANAGEMENT_API_KEY")
                .unwrap_or_else(|_| "default_api_key".to_string()),
            timeout_seconds: std::env::var("USER_MANAGEMENT_TIMEOUT")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
            max_retries: std::env::var("USER_MANAGEMENT_MAX_RETRIES")
                .unwrap_or_else(|_| "3".to_string())
                .parse()
                .unwrap_or(3),
            retry_delay_seconds: std::env::var("USER_MANAGEMENT_RETRY_DELAY")
                .unwrap_or_else(|_| "5".to_string())
                .parse()
                .unwrap_or(5),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserManagementRequest {
    pub user_id: String,
    pub action: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserManagementResponse {
    pub success: bool,
    pub message: String,
    pub user_id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, thiserror::Error)]
pub enum UserManagementError {
    #[error("HTTP request failed: {0}")]
    HttpError(#[from] reqwest::Error),
    #[error("API returned error: {0}")]
    ApiError(String),
    #[error("User not found: {0}")]
    UserNotFound(String),
    #[error("Authentication failed")]
    AuthenticationError,
    #[error("Network error: {0}")]
    NetworkError(String),
    #[error("Timeout error")]
    TimeoutError,
    #[error("Too many retries")]
    TooManyRetries,
}

pub struct UserManagementService {
    client: Client,
    config: UserManagementConfig,
    _db: Arc<DatabaseConnection>,
}

impl UserManagementService {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        let config = UserManagementConfig::default();
        let client = Client::builder()
            .timeout(Duration::from_secs(config.timeout_seconds))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            config,
            _db: db,
        }
    }

    pub fn new_with_config(db: Arc<DatabaseConnection>, config: UserManagementConfig) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.timeout_seconds))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            config,
            _db: db,
        }
    }

    /// 添加用户到外部系统
    pub async fn add_user(&self, user_id: Uuid) -> Result<(), UserManagementError> {
        let request = UserManagementRequest {
            user_id: user_id.to_string(),
            action: "add".to_string(),
            timestamp: chrono::Utc::now(),
        };

        self.make_request("/api/users/add", &request).await?;
        info!("Successfully added user {} to external system", user_id);
        Ok(())
    }

    /// 从外部系统移除用户
    pub async fn remove_user(&self, user_id: Uuid) -> Result<(), UserManagementError> {
        let request = UserManagementRequest {
            user_id: user_id.to_string(),
            action: "remove".to_string(),
            timestamp: chrono::Utc::now(),
        };

        self.make_request("/api/users/remove", &request).await?;
        info!("Successfully removed user {} from external system", user_id);
        Ok(())
    }

    /// 暂停用户
    pub async fn suspend_user(&self, user_id: Uuid) -> Result<(), UserManagementError> {
        let request = UserManagementRequest {
            user_id: user_id.to_string(),
            action: "suspend".to_string(),
            timestamp: chrono::Utc::now(),
        };

        self.make_request("/api/users/suspend", &request).await?;
        info!("Successfully suspended user {} in external system", user_id);
        Ok(())
    }

    /// 恢复用户
    pub async fn resume_user(&self, user_id: Uuid) -> Result<(), UserManagementError> {
        let request = UserManagementRequest {
            user_id: user_id.to_string(),
            action: "resume".to_string(),
            timestamp: chrono::Utc::now(),
        };

        self.make_request("/api/users/resume", &request).await?;
        info!("Successfully resumed user {} in external system", user_id);
        Ok(())
    }

    /// 发送HTTP请求到外部API
    async fn make_request(
        &self,
        endpoint: &str,
        request: &UserManagementRequest,
    ) -> Result<UserManagementResponse, UserManagementError> {
        let url = format!("{}{}", self.config.api_url, endpoint);
        let mut retries = 0;

        loop {
            match self
                .client
                .post(&url)
                .header("Authorization", format!("Bearer {}", self.config.api_key))
                .header("Content-Type", "application/json")
                .json(request)
                .send()
                .await
            {
                Ok(response) => {
                    if response.status().is_success() {
                        match response.json::<UserManagementResponse>().await {
                            Ok(api_response) => {
                                if api_response.success {
                                    return Ok(api_response);
                                } else {
                                    return Err(UserManagementError::ApiError(api_response.message));
                                }
                            }
                            Err(e) => {
                                warn!("Failed to parse API response: {}", e);
                                if retries >= self.config.max_retries {
                                    return Err(UserManagementError::ApiError(format!(
                                        "Failed to parse response: {}",
                                        e
                                    )));
                                }
                            }
                        }
                    } else if response.status() == 401 {
                        return Err(UserManagementError::AuthenticationError);
                    } else if response.status() == 404 {
                        return Err(UserManagementError::UserNotFound(request.user_id.clone()));
                    } else {
                        warn!("API returned status {}", response.status());
                        if retries >= self.config.max_retries {
                            return Err(UserManagementError::ApiError(format!(
                                "API returned status: {}",
                                response.status()
                            )));
                        }
                    }
                }
                Err(e) => {
                    if e.is_timeout() {
                        warn!("Request timeout for user {}", request.user_id);
                        if retries >= self.config.max_retries {
                            return Err(UserManagementError::TimeoutError);
                        }
                    } else if e.is_connect() {
                        warn!("Connection error for user {}: {}", request.user_id, e);
                        if retries >= self.config.max_retries {
                            return Err(UserManagementError::NetworkError(e.to_string()));
                        }
                    } else {
                        warn!("HTTP error for user {}: {}", request.user_id, e);
                        if retries >= self.config.max_retries {
                            return Err(UserManagementError::HttpError(e));
                        }
                    }
                }
            }

            retries += 1;
            if retries > self.config.max_retries {
                return Err(UserManagementError::TooManyRetries);
            }

            // 等待后重试
            tokio::time::sleep(Duration::from_secs(self.config.retry_delay_seconds)).await;
            warn!("Retrying request to {} (attempt {}/{})", url, retries, self.config.max_retries);
        }
    }

    /// 测试与外部API的连接
    pub async fn test_connection(&self) -> Result<(), UserManagementError> {
        let url = format!("{}/api/health", self.config.api_url);
        
        match self
            .client
            .get(&url)
            .header("Authorization", format!("Bearer {}", self.config.api_key))
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_success() {
                    info!("User management API connection test successful");
                    Ok(())
                } else {
                    Err(UserManagementError::ApiError(format!(
                        "Health check failed with status: {}",
                        response.status()
                    )))
                }
            }
            Err(e) => {
                error!("User management API connection test failed: {}", e);
                Err(UserManagementError::HttpError(e))
            }
        }
    }
}