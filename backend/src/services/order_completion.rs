use std::sync::Arc;
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use tracing::{info, warn, error};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use sea_orm::{Set, ActiveModelTrait, TransactionTrait};

use crate::repositories::{OrderRepository, SubscriptionRepository, PlanRepository, UserRepository, Repository};
use crate::entities::{orders, subscriptions, enums::{OrderStatus, SubscriptionStatus}};
use crate::services::subscription_management::{SubscriptionManagementService, CreateSubscriptionRequest};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderCompletionRequest {
    pub order_id: Uuid,
    pub payment_reference: Option<String>,
    pub payment_proof_url: Option<String>,
    pub processed_by: Option<Uuid>,
    pub notes: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OrderCompletionResult {
    pub order_id: Uuid,
    pub order_number: String,
    pub subscription_id: Option<Uuid>,
    pub status: OrderStatus,
    pub activated_at: DateTime<Utc>,
    pub subscription_start_date: Option<DateTime<Utc>>,
    pub subscription_end_date: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderActivationError {
    pub error_type: String,
    pub message: String,
    pub order_id: Uuid,
    pub rollback_successful: bool,
}

pub struct OrderCompletionService {
    db: Arc<DatabaseConnection>,
    order_repo: OrderRepository,
    subscription_repo: SubscriptionRepository,
    plan_repo: PlanRepository,
    user_repo: UserRepository,
    subscription_service: SubscriptionManagementService,
}

impl OrderCompletionService {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        let order_repo = OrderRepository::new(db.clone());
        let subscription_repo = SubscriptionRepository::new(db.clone());
        let plan_repo = PlanRepository::new(db.clone());
        let user_repo = UserRepository::new(db.clone());
        let subscription_service = SubscriptionManagementService::new(db.clone());
        
        Self {
            db,
            order_repo,
            subscription_repo,
            plan_repo,
            user_repo,
            subscription_service,
        }
    }

    /// Complete order and activate subscription in a transaction
    pub async fn complete_order(&self, request: OrderCompletionRequest) -> Result<OrderCompletionResult, Box<dyn std::error::Error + Send + Sync>> {
        let txn = self.db.begin().await?;
        
        match self.complete_order_internal(&txn, request.clone()).await {
            Ok(result) => {
                txn.commit().await?;
                info!("Order {} completed successfully with subscription {}", 
                      result.order_id, 
                      result.subscription_id.map(|id| id.to_string()).unwrap_or_else(|| "None".to_string()));
                Ok(result)
            }
            Err(e) => {
                // Try to rollback the transaction
                if let Err(rollback_error) = txn.rollback().await {
                    error!("Failed to rollback transaction for order {}: {}", request.order_id, rollback_error);
                    return Err(format!("Order completion failed and rollback failed: {}", e).into());
                }
                
                error!("Order completion failed for order {}: {}", request.order_id, e);
                Err(e)
            }
        }
    }

    async fn complete_order_internal(
        &self,
        txn: &sea_orm::DatabaseTransaction,
        request: OrderCompletionRequest,
    ) -> Result<OrderCompletionResult, Box<dyn std::error::Error + Send + Sync>> {
        // 1. Validate order exists and is in valid state
        let order = self.order_repo
            .find_by_id(request.order_id)
            .await?
            .ok_or("Order not found")?;

        if order.status != OrderStatus::PendingPayment {
            return Err(format!("Order {} is not in pending payment state, current status: {}", 
                             order.id, order.status).into());
        }

        // Check if order has expired
        let order_expires_at: DateTime<Utc> = order.expires_at.into();
        if Utc::now() > order_expires_at {
            return Err(format!("Order {} has expired", order.id).into());
        }

        // 2. Validate plan exists
        let plan = self.plan_repo
            .find_by_id(order.plan_id)
            .await?
            .ok_or("Plan not found")?;

        if !plan.is_available() {
            return Err(format!("Plan {} is no longer available", plan.id).into());
        }

        // 3. Validate user exists and is active
        let user = self.user_repo
            .find_by_id(order.user_id)
            .await?
            .ok_or("User not found")?;

        if user.is_banned {
            return Err(format!("User {} is banned", user.id).into());
        }

        if !user.is_active {
            return Err(format!("User {} is not active", user.id).into());
        }

        // 4. Update order status to processing
        let processing_order = orders::ActiveModel {
            id: Set(order.id),
            status: Set(OrderStatus::Processing),
            payment_reference: Set(request.payment_reference.clone()),
            payment_proof_url: Set(request.payment_proof_url.clone()),
            processed_by: Set(request.processed_by),
            processed_at: Set(Some(Utc::now().into())),
            notes: Set(request.notes.clone()),
            updated_at: Set(Utc::now().into()),
            ..Default::default()
        };

        self.order_repo.update(order.id, processing_order).await?;

        // 5. Create subscription for each quantity
        let mut subscription_ids = Vec::new();
        let mut subscription_start_date = None;
        let mut subscription_end_date = None;

        for i in 0..order.quantity {
            let subscription_request = CreateSubscriptionRequest {
                user_id: order.user_id,
                plan_id: order.plan_id,
                duration_days: plan.duration_days,
                auto_renew: false, // Default to false for manual orders
            };

            let subscription_info = self.subscription_service
                .create_subscription(subscription_request)
                .await
                .map_err(|e| format!("Failed to create subscription: {}", e))?;

            subscription_ids.push(subscription_info.id);
            
            // Store first subscription dates for response
            if i == 0 {
                subscription_start_date = Some(subscription_info.start_date);
                subscription_end_date = Some(subscription_info.end_date);
            }

            info!("Created subscription {} for order {} (quantity {}/{})", 
                  subscription_info.id, order.id, i + 1, order.quantity);
        }

        // 6. Update order status to completed
        let completed_order = orders::ActiveModel {
            id: Set(order.id),
            status: Set(OrderStatus::Completed),
            updated_at: Set(Utc::now().into()),
            ..Default::default()
        };

        self.order_repo.update(order.id, completed_order).await?;

        // 7. Log completion
        info!("Order {} completed successfully with {} subscriptions", 
              order.id, subscription_ids.len());

        Ok(OrderCompletionResult {
            order_id: order.id,
            order_number: order.order_number,
            subscription_id: subscription_ids.first().copied(),
            status: OrderStatus::Completed,
            activated_at: Utc::now(),
            subscription_start_date,
            subscription_end_date,
        })
    }

    /// Cancel order and clean up any associated data
    pub async fn cancel_order(&self, order_id: Uuid, reason: Option<String>) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let txn = self.db.begin().await?;
        
        match self.cancel_order_internal(&txn, order_id, reason).await {
            Ok(()) => {
                txn.commit().await?;
                info!("Order {} cancelled successfully", order_id);
                Ok(())
            }
            Err(e) => {
                if let Err(rollback_error) = txn.rollback().await {
                    error!("Failed to rollback transaction for order cancellation {}: {}", order_id, rollback_error);
                }
                error!("Order cancellation failed for order {}: {}", order_id, e);
                Err(e)
            }
        }
    }

    async fn cancel_order_internal(
        &self,
        _txn: &sea_orm::DatabaseTransaction,
        order_id: Uuid,
        reason: Option<String>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Get order
        let order = self.order_repo
            .find_by_id(order_id)
            .await?
            .ok_or("Order not found")?;

        // Can only cancel pending orders
        if order.status != OrderStatus::PendingPayment {
            return Err(format!("Cannot cancel order {} in status {}", order_id, order.status).into());
        }

        // Update order status to cancelled
        let cancelled_order = orders::ActiveModel {
            id: Set(order.id),
            status: Set(OrderStatus::Cancelled),
            notes: Set(reason),
            updated_at: Set(Utc::now().into()),
            ..Default::default()
        };

        self.order_repo.update(order.id, cancelled_order).await?;

        info!("Order {} cancelled", order_id);
        Ok(())
    }

    /// Process expired orders
    pub async fn process_expired_orders(&self) -> Result<u32, Box<dyn std::error::Error + Send + Sync>> {
        let expired_orders = self.order_repo
            .find_expired()
            .await?;

        let mut processed_count = 0;

        for order in expired_orders {
            if order.status == OrderStatus::PendingPayment {
                match self.cancel_order(order.id, Some("Order expired".to_string())).await {
                    Ok(_) => {
                        processed_count += 1;
                        info!("Automatically cancelled expired order {}", order.id);
                    }
                    Err(e) => {
                        error!("Failed to cancel expired order {}: {}", order.id, e);
                    }
                }
            }
        }

        info!("Processed {} expired orders", processed_count);
        Ok(processed_count)
    }

    /// Check if order can be completed
    pub async fn can_complete_order(&self, order_id: Uuid) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        let order = self.order_repo
            .find_by_id(order_id)
            .await?
            .ok_or("Order not found")?;

        // Check order status
        if order.status != OrderStatus::PendingPayment {
            return Ok(false);
        }

        // Check if order has expired
        let order_expires_at: DateTime<Utc> = order.expires_at.into();
        if Utc::now() > order_expires_at {
            return Ok(false);
        }

        // Check if plan is still available
        let plan = self.plan_repo
            .find_by_id(order.plan_id)
            .await?
            .ok_or("Plan not found")?;

        if !plan.is_available() {
            return Ok(false);
        }

        // Check if user is still active
        let user = self.user_repo
            .find_by_id(order.user_id)
            .await?
            .ok_or("User not found")?;

        if user.is_banned || !user.is_active {
            return Ok(false);
        }

        Ok(true)
    }

    /// Get order completion statistics
    pub async fn get_completion_stats(&self) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        let all_orders = self.order_repo.find_all().await?;
        
        let mut stats = serde_json::Map::new();
        let mut status_counts = serde_json::Map::new();
        let mut total_revenue = Decimal::ZERO;
        let mut completed_count = 0;
        
        for order in all_orders {
            let status_str = order.status.to_string();
            let count = status_counts.get(&status_str)
                .and_then(|v| v.as_u64())
                .unwrap_or(0) + 1;
            status_counts.insert(status_str, serde_json::Value::Number(count.into()));
            
            if order.status == OrderStatus::Completed {
                total_revenue += order.amount;
                completed_count += 1;
            }
        }
        
        stats.insert("status_counts".to_string(), serde_json::Value::Object(status_counts));
        stats.insert("total_revenue".to_string(), serde_json::Value::String(total_revenue.to_string()));
        stats.insert("completed_orders".to_string(), serde_json::Value::Number(completed_count.into()));
        
        Ok(serde_json::Value::Object(stats))
    }

    /// Start background task to process expired orders
    pub async fn start_expired_orders_task(&self) {
        let service = Self::new(self.db.clone());
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(3600)); // Run every hour
            loop {
                interval.tick().await;
                
                match service.process_expired_orders().await {
                    Ok(count) => {
                        if count > 0 {
                            info!("Processed {} expired orders", count);
                        }
                    }
                    Err(e) => {
                        error!("Failed to process expired orders: {}", e);
                    }
                }
            }
        });
    }
}