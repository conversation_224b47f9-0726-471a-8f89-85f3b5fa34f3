use axum::{
    extract::{Path, State},
    response::Json,
    routing::{get, post},
    Router,
};
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::sync::Arc;

use crate::auth::UserContext;
use crate::errors::{AppError, AppResult, ApiResponse, success};
use crate::services::traffic_management::{TrafficManagementService, TrafficUsage, BillingCycleInfo};
use crate::repositories::{SubscriptionRepository, Repository};

/// Response for traffic sync operation
#[derive(Debug, Serialize)]
pub struct TrafficSyncResponse {
    pub message: String,
    pub synced_users: u32,
}

/// GET /api/admin/traffic/sync - Manually trigger traffic sync for all users
pub async fn sync_all_traffic(
    State(db): State<DatabaseConnection>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<TrafficSyncResponse>>> {
    let traffic_service = TrafficManagementService::new(Arc::new(db));
    
    match traffic_service.sync_all_user_traffic().await {
        Ok(_) => {
            let response = TrafficSyncResponse {
                message: "Traffic sync completed successfully".to_string(),
                synced_users: 0, // TODO: Return actual count
            };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to sync traffic: {}", e);
            Err(AppError::internal(&format!("Failed to sync traffic: {}", e)))
        }
    }
}

/// POST /api/admin/traffic/sync/:user_id - Manually trigger traffic sync for specific user
pub async fn sync_user_traffic(
    State(db): State<DatabaseConnection>,
    Path(user_id): Path<Uuid>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let traffic_service = TrafficManagementService::new(Arc::new(db));
    
    match traffic_service.sync_user_traffic(user_id).await {
        Ok(_) => {
            Ok(Json(success(serde_json::json!({
                "message": "Traffic sync completed successfully",
                "user_id": user_id
            }))))
        }
        Err(e) => {
            tracing::error!("Failed to sync traffic for user {}: {}", user_id, e);
            Err(AppError::internal(&format!("Failed to sync traffic: {}", e)))
        }
    }
}

/// GET /api/user/traffic/usage - Get current user's traffic usage
pub async fn get_user_traffic_usage(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<Vec<TrafficUsage>>>> {
    let traffic_service = TrafficManagementService::new(Arc::new(db));
    
    match traffic_service.get_user_traffic_usage(user_context.user_id).await {
        Ok(usage) => Ok(Json(success(usage))),
        Err(e) => {
            tracing::error!("Failed to get traffic usage for user {}: {}", user_context.user_id, e);
            Err(AppError::internal(&format!("Failed to get traffic usage: {}", e)))
        }
    }
}

/// GET /api/user/traffic/check-limit - Check if user has exceeded traffic limit
pub async fn check_traffic_limit(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let traffic_service = TrafficManagementService::new(Arc::new(db));
    
    match traffic_service.check_user_traffic_limit(user_context.user_id).await {
        Ok(is_exceeded) => {
            Ok(Json(success(serde_json::json!({
                "user_id": user_context.user_id,
                "is_traffic_exceeded": is_exceeded
            }))))
        }
        Err(e) => {
            tracing::error!("Failed to check traffic limit for user {}: {}", user_context.user_id, e);
            Err(AppError::internal(&format!("Failed to check traffic limit: {}", e)))
        }
    }
}

/// GET /api/user/traffic/billing-cycle/:subscription_id - Get billing cycle information for user's subscription
pub async fn get_user_billing_cycle_info(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<BillingCycleInfo>>> {
    let db_arc = Arc::new(db);
    let traffic_service = TrafficManagementService::new(db_arc.clone());
    
    // First verify that the subscription belongs to the user
    let subscription_repo = SubscriptionRepository::new(db_arc.clone());
    let subscription = subscription_repo.find_by_id(subscription_id).await
        .map_err(|e| AppError::internal(&format!("Failed to get subscription: {}", e)))?
        .ok_or_else(|| AppError::not_found("Subscription not found"))?;
    
    if subscription.user_id != user_context.user_id {
        return Err(AppError::authorization("Access denied"));
    }
    
    match traffic_service.get_subscription_billing_info(subscription_id).await {
        Ok(billing_info) => Ok(Json(success(billing_info))),
        Err(e) => {
            tracing::error!("Failed to get billing cycle info for subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to get billing cycle info: {}", e)))
        }
    }
}

/// POST /api/admin/traffic/reset - Reset traffic for all users
pub async fn reset_all_traffic(
    State(db): State<DatabaseConnection>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<TrafficSyncResponse>>> {
    let traffic_service = TrafficManagementService::new(Arc::new(db));
    
    match traffic_service.reset_all_traffic().await {
        Ok(reset_count) => {
            let response = TrafficSyncResponse {
                message: format!("Successfully reset traffic for {} subscriptions", reset_count),
                synced_users: reset_count,
            };
            Ok(Json(success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to reset all traffic: {}", e);
            Err(AppError::internal(&format!("Failed to reset traffic: {}", e)))
        }
    }
}

/// POST /api/admin/traffic/reset/:user_id - Reset traffic for specific user
pub async fn reset_user_traffic(
    State(db): State<DatabaseConnection>,
    Path(user_id): Path<Uuid>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let traffic_service = TrafficManagementService::new(Arc::new(db));
    
    match traffic_service.reset_user_traffic(user_id).await {
        Ok(_) => {
            Ok(Json(success(serde_json::json!({
                "message": "Traffic reset successfully",
                "user_id": user_id
            }))))
        }
        Err(e) => {
            tracing::error!("Failed to reset traffic for user {}: {}", user_id, e);
            Err(AppError::internal(&format!("Failed to reset traffic: {}", e)))
        }
    }
}

/// POST /api/admin/traffic/reset/subscription/:subscription_id - Reset traffic for specific subscription
pub async fn reset_subscription_traffic(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let traffic_service = TrafficManagementService::new(Arc::new(db));
    
    match traffic_service.reset_subscription_traffic(subscription_id).await {
        Ok(_) => {
            Ok(Json(success(serde_json::json!({
                "message": "Subscription traffic reset successfully",
                "subscription_id": subscription_id
            }))))
        }
        Err(e) => {
            tracing::error!("Failed to reset traffic for subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to reset traffic: {}", e)))
        }
    }
}

/// GET /api/admin/traffic/billing-cycle/:subscription_id - Get billing cycle information for subscription
pub async fn get_billing_cycle_info(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<BillingCycleInfo>>> {
    let traffic_service = TrafficManagementService::new(Arc::new(db));
    
    match traffic_service.get_subscription_billing_info(subscription_id).await {
        Ok(billing_info) => Ok(Json(success(billing_info))),
        Err(e) => {
            tracing::error!("Failed to get billing cycle info for subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to get billing cycle info: {}", e)))
        }
    }
}

/// POST /api/admin/traffic/billing-cycle/:subscription_id/reset - Force reset billing cycle for subscription
pub async fn force_billing_cycle_reset(
    State(db): State<DatabaseConnection>,
    Path(subscription_id): Path<Uuid>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let traffic_service = TrafficManagementService::new(Arc::new(db));
    
    match traffic_service.force_billing_cycle_reset(subscription_id).await {
        Ok(_) => {
            Ok(Json(success(serde_json::json!({
                "message": "Billing cycle reset successfully",
                "subscription_id": subscription_id,
                "reset_by": user_context.username,
                "reset_at": chrono::Utc::now()
            }))))
        }
        Err(e) => {
            tracing::error!("Failed to force reset billing cycle for subscription {}: {}", subscription_id, e);
            Err(AppError::internal(&format!("Failed to reset billing cycle: {}", e)))
        }
    }
}

/// POST /api/admin/traffic/billing-cycle/process - Manually trigger billing cycle reset process
pub async fn process_billing_cycle_resets(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let traffic_service = TrafficManagementService::new(Arc::new(db));
    
    match traffic_service.process_billing_cycle_resets().await {
        Ok(reset_count) => {
            Ok(Json(success(serde_json::json!({
                "message": format!("Processed {} billing cycle resets", reset_count),
                "reset_count": reset_count,
                "processed_by": user_context.username,
                "processed_at": chrono::Utc::now()
            }))))
        }
        Err(e) => {
            tracing::error!("Failed to process billing cycle resets: {}", e);
            Err(AppError::internal(&format!("Failed to process billing cycle resets: {}", e)))
        }
    }
}

/// Create admin traffic management routes
pub fn admin_traffic_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/sync", get(sync_all_traffic))
        .route("/sync/:user_id", post(sync_user_traffic))
        .route("/reset", post(reset_all_traffic))
        .route("/reset/:user_id", post(reset_user_traffic))
        .route("/reset/subscription/:subscription_id", post(reset_subscription_traffic))
        .route("/billing-cycle/:subscription_id", get(get_billing_cycle_info))
        .route("/billing-cycle/:subscription_id/reset", post(force_billing_cycle_reset))
        .route("/billing-cycle/process", post(process_billing_cycle_resets))
}

/// Create user traffic routes
pub fn user_traffic_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/usage", get(get_user_traffic_usage))
        .route("/check-limit", get(check_traffic_limit))
        .route("/billing-cycle/:subscription_id", get(get_user_billing_cycle_info))
}