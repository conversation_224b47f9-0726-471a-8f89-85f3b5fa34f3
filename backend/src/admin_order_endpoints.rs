// Admin order management endpoints
use axum::{
    extract::{State, Path, Query},
    response::Json,
    routing::{get, post, put},
    Router,
};
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::sync::Arc;
use std::collections::HashMap;
use crate::repositories::{PlanRepository, UserRepository};

use crate::errors::{AppError, AppResult, ApiResponse, success};
use crate::repositories::{OrderRepository, Repository};
use crate::services::order_completion::{OrderCompletionService, OrderCompletionRequest, OrderCompletionResult};
use crate::entities::enums::OrderStatus;
use crate::order_endpoints::{OrderResponse, OrderListResponse, OrderPlanInfo, OrderUserInfo};
use crate::auth::UserContext;

#[derive(Debug, Deserialize)]
pub struct AdminOrderQuery {
    pub status: Option<String>,
    pub user_id: Option<Uuid>,
    pub plan_id: Option<Uuid>,
    pub page: Option<u32>,
    pub limit: Option<u32>,
}

#[derive(Debug, Deserialize)]
pub struct CompleteOrderRequest {
    pub payment_reference: Option<String>,
    pub payment_proof_url: Option<String>,
    pub notes: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CancelOrderRequest {
    pub reason: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct OrderCompletionResponse {
    pub order_id: Uuid,
    pub order_number: String,
    pub subscription_id: Option<Uuid>,
    pub status: String,
    pub activated_at: chrono::DateTime<chrono::Utc>,
    pub subscription_start_date: Option<chrono::DateTime<chrono::Utc>>,
    pub subscription_end_date: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Serialize)]
pub struct OrderStatsResponse {
    pub status_counts: HashMap<String, u64>,
    pub total_revenue: String,
    pub completed_orders: u64,
}

/// GET /api/admin/orders - Get all orders with filtering
pub async fn get_all_orders(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Query(query): Query<AdminOrderQuery>,
) -> AppResult<Json<ApiResponse<OrderListResponse>>> {
    let order_repo = OrderRepository::new(Arc::new(db.clone()));
    
    // Get all orders with optional filtering
    let orders = if let Some(status) = query.status {
        let order_status = match status.as_str() {
            "pending_payment" => OrderStatus::PendingPayment,
            "pending" => OrderStatus::Pending,
            "paid" => OrderStatus::Paid,
            "processing" => OrderStatus::Processing,
            "completed" => OrderStatus::Completed,
            "cancelled" => OrderStatus::Cancelled,
            "refunded" => OrderStatus::Refunded,
            "failed" => OrderStatus::Failed,
            _ => return Err(AppError::bad_request("Invalid order status")),
        };
        order_repo.find_by_status(order_status).await?
    } else if let Some(user_id) = query.user_id {
        order_repo.find_by_user_id(user_id).await?
    } else {
        order_repo.find_all().await?
    };

    // Convert to response format
    let mut order_responses = Vec::new();
    
    for order in orders {
        // Get related data
        let plan_repo = PlanRepository::new(Arc::new(db.clone()));
        let user_repo = UserRepository::new(Arc::new(db.clone()));
        
        let plan = plan_repo.find_by_id(order.plan_id).await?
            .ok_or_else(|| AppError::not_found("Plan not found"))?;
        
        let user = user_repo.find_by_id(order.user_id).await?
            .ok_or_else(|| AppError::not_found("User not found"))?;
        
        order_responses.push(OrderResponse {
            id: order.id,
            order_number: order.order_number,
            plan: OrderPlanInfo {
                id: plan.id,
                name: plan.name,
                description: plan.description,
                price: plan.price,
                duration_days: plan.duration_days,
            },
            user: OrderUserInfo {
                id: user.id,
                username: user.username,
                email: user.email,
            },
            amount: order.amount,
            currency: order.currency,
            quantity: order.quantity,
            status: order.status.to_string(),
            payment_method: order.payment_method.map(|pm| pm.to_string()),
            payment_reference: order.payment_reference,
            notes: order.notes,
            created_at: order.created_at.into(),
            updated_at: order.updated_at.into(),
            expires_at: order.expires_at.into(),
        });
    }

    let response = OrderListResponse {
        total_count: order_responses.len(),
        orders: order_responses,
    };

    Ok(Json(success(response)))
}

/// GET /api/admin/orders/:order_id - Get specific order details
pub async fn get_order_details(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Path(order_id): Path<Uuid>,
) -> AppResult<Json<ApiResponse<OrderResponse>>> {
    let order_repo = OrderRepository::new(Arc::new(db.clone()));
    let plan_repo = PlanRepository::new(Arc::new(db.clone()));
    let user_repo = UserRepository::new(Arc::new(db.clone()));
    
    let order = order_repo.find_by_id(order_id).await?
        .ok_or_else(|| AppError::not_found("Order not found"))?;

    let plan = plan_repo.find_by_id(order.plan_id).await?
        .ok_or_else(|| AppError::not_found("Plan not found"))?;
    
    let user = user_repo.find_by_id(order.user_id).await?
        .ok_or_else(|| AppError::not_found("User not found"))?;

    let response = OrderResponse {
        id: order.id,
        order_number: order.order_number,
        plan: OrderPlanInfo {
            id: plan.id,
            name: plan.name,
            description: plan.description,
            price: plan.price,
            duration_days: plan.duration_days,
        },
        user: OrderUserInfo {
            id: user.id,
            username: user.username,
            email: user.email,
        },
        amount: order.amount,
        currency: order.currency,
        quantity: order.quantity,
        status: order.status.to_string(),
        payment_method: order.payment_method.map(|pm| pm.to_string()),
        payment_reference: order.payment_reference,
        notes: order.notes,
        created_at: order.created_at.into(),
        updated_at: order.updated_at.into(),
        expires_at: order.expires_at.into(),
    };

    Ok(Json(success(response)))
}

/// POST /api/admin/orders/:order_id/complete - Complete an order and activate subscription
pub async fn complete_order(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Path(order_id): Path<Uuid>,
    Json(request): Json<CompleteOrderRequest>,
) -> AppResult<Json<ApiResponse<OrderCompletionResponse>>> {
    let order_completion_service = OrderCompletionService::new(Arc::new(db));
    
    let completion_request = OrderCompletionRequest {
        order_id,
        payment_reference: request.payment_reference,
        payment_proof_url: request.payment_proof_url,
        processed_by: Some(user_context.user_id),
        notes: request.notes,
    };

    let result = order_completion_service.complete_order(completion_request).await
        .map_err(|e| AppError::internal(format!("Failed to complete order: {}", e)))?;

    let response = OrderCompletionResponse {
        order_id: result.order_id,
        order_number: result.order_number,
        subscription_id: result.subscription_id,
        status: result.status.to_string(),
        activated_at: result.activated_at,
        subscription_start_date: result.subscription_start_date,
        subscription_end_date: result.subscription_end_date,
    };

    Ok(Json(success(response)))
}

/// POST /api/admin/orders/:order_id/cancel - Cancel an order
pub async fn cancel_order(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Path(order_id): Path<Uuid>,
    Json(request): Json<CancelOrderRequest>,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let order_completion_service = OrderCompletionService::new(Arc::new(db));
    
    order_completion_service.cancel_order(order_id, request.reason).await
        .map_err(|e| AppError::internal(format!("Failed to cancel order: {}", e)))?;

    Ok(Json(success(serde_json::json!({
        "message": "Order cancelled successfully",
        "order_id": order_id,
        "cancelled_by": user_context.username,
        "cancelled_at": chrono::Utc::now()
    }))))
}

/// GET /api/admin/orders/stats - Get order completion statistics
pub async fn get_order_stats(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let order_completion_service = OrderCompletionService::new(Arc::new(db));
    
    let stats = order_completion_service.get_completion_stats().await
        .map_err(|e| AppError::internal(format!("Failed to get order stats: {}", e)))?;

    Ok(Json(success(stats)))
}

/// GET /api/admin/orders/:order_id/can-complete - Check if order can be completed
pub async fn can_complete_order(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Path(order_id): Path<Uuid>,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let order_completion_service = OrderCompletionService::new(Arc::new(db));
    
    let can_complete = order_completion_service.can_complete_order(order_id).await
        .map_err(|e| AppError::internal(format!("Failed to check order status: {}", e)))?;

    Ok(Json(success(serde_json::json!({
        "order_id": order_id,
        "can_complete": can_complete,
        "checked_at": chrono::Utc::now()
    }))))
}

/// POST /api/admin/orders/process-expired - Process expired orders
pub async fn process_expired_orders(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let order_completion_service = OrderCompletionService::new(Arc::new(db));
    
    let processed_count = order_completion_service.process_expired_orders().await
        .map_err(|e| AppError::internal(format!("Failed to process expired orders: {}", e)))?;

    Ok(Json(success(serde_json::json!({
        "processed_count": processed_count,
        "processed_by": user_context.username,
        "processed_at": chrono::Utc::now()
    }))))
}

/// Create admin order routes
pub fn admin_order_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/", get(get_all_orders))
        .route("/:order_id", get(get_order_details))
        .route("/:order_id/complete", post(complete_order))
        .route("/:order_id/cancel", post(cancel_order))
        .route("/:order_id/can-complete", get(can_complete_order))
        .route("/stats", get(get_order_stats))
        .route("/process-expired", post(process_expired_orders))
}