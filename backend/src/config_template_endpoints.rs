// Configuration template CRUD API endpoints

use axum::{
    extract::{Path, Query, State},
    response::Json,
    routing::{get, post, put, delete},
    Router,
};
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::auth::UserContext;
use crate::errors::{AppError, AppResult, ApiResponse, success};
use crate::repositories::{ConfigTemplateRepository, Repository};
use crate::entities::config_templates;

/// Request structure for creating configuration template
#[derive(Debug, Deserialize)]
pub struct CreateConfigTemplateRequest {
    pub name: String,
    pub description: Option<String>,
    pub template_content: String,
    pub template_type: String,
    pub version: Option<String>,
    pub is_system_default: Option<bool>,
    pub sort_order: Option<i32>,
}

/// Request structure for updating configuration template
#[derive(Debug, Deserialize)]
pub struct UpdateConfigTemplateRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub template_content: Option<String>,
    pub template_type: Option<String>,
    pub version: Option<String>,
    pub is_active: Option<bool>,
    pub is_system_default: Option<bool>,
    pub sort_order: Option<i32>,
}

/// Request structure for duplicating configuration template
#[derive(Debug, Deserialize)]
pub struct DuplicateConfigTemplateRequest {
    pub new_name: String,
}

/// Response structure for configuration template
#[derive(Debug, Serialize)]
pub struct ConfigTemplateResponse {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub template_content: String,
    pub template_type: String,
    pub version: String,
    pub is_active: bool,
    pub is_system_default: bool,
    pub sort_order: i32,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub created_by: Uuid,
    pub updated_by: Uuid,
    pub type_display: String,
    pub status_display: String,
    pub preview: String,
    pub placeholder_count: usize,
}

impl From<config_templates::Model> for ConfigTemplateResponse {
    fn from(model: config_templates::Model) -> Self {
        Self {
            id: model.id,
            name: model.name.clone(),
            description: model.description.clone(),
            template_content: model.template_content.clone(),
            template_type: model.template_type.clone(),
            version: model.version.clone(),
            is_active: model.is_active,
            is_system_default: model.is_system_default,
            sort_order: model.sort_order,
            created_at: model.created_at.into(),
            updated_at: model.updated_at.into(),
            created_by: model.created_by,
            updated_by: model.updated_by,
            type_display: model.type_display(),
            status_display: model.status_display(),
            preview: model.get_preview(),
            placeholder_count: model.count_placeholders(),
        }
    }
}

/// Response structure for configuration template list
#[derive(Debug, Serialize)]
pub struct ConfigTemplateListResponse {
    pub templates: Vec<ConfigTemplateResponse>,
    pub total: usize,
}

/// Response structure for configuration template stats
#[derive(Debug, Serialize)]
pub struct ConfigTemplateStatsResponse {
    pub total_count: u64,
    pub active_count: u64,
    pub system_default_count: u64,
    pub template_types: Vec<String>,
}

/// Query parameters for listing configuration templates
#[derive(Debug, Deserialize)]
pub struct ListConfigTemplatesQuery {
    pub template_type: Option<String>,
    pub is_active: Option<bool>,
    pub is_system_default: Option<bool>,
}

/// GET /api/admin/config-templates - List all configuration templates
pub async fn list_config_templates(
    State(db): State<DatabaseConnection>,
    Query(query): Query<ListConfigTemplatesQuery>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<ConfigTemplateListResponse>>> {
    let config_template_repo = ConfigTemplateRepository::new(std::sync::Arc::new(db));
    
    let templates = if let Some(template_type) = query.template_type {
        config_template_repo.find_by_type(&template_type).await?
    } else if query.is_active == Some(true) {
        config_template_repo.find_active_templates().await?
    } else {
        config_template_repo.find_all_ordered().await?
    };
    
    let response = ConfigTemplateListResponse {
        total: templates.len(),
        templates: templates.into_iter().map(ConfigTemplateResponse::from).collect(),
    };
    
    Ok(Json(success(response)))
}

/// GET /api/admin/config-templates/:id - Get configuration template by ID
pub async fn get_config_template(
    State(db): State<DatabaseConnection>,
    Path(id): Path<Uuid>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<ConfigTemplateResponse>>> {
    let config_template_repo = ConfigTemplateRepository::new(std::sync::Arc::new(db));
    
    let template = config_template_repo.find_by_id(id).await?
        .ok_or(AppError::not_found("Configuration template not found"))?;
    
    let response = ConfigTemplateResponse::from(template);
    Ok(Json(success(response)))
}

/// POST /api/admin/config-templates - Create new configuration template
pub async fn create_config_template(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Json(request): Json<CreateConfigTemplateRequest>,
) -> AppResult<Json<ApiResponse<ConfigTemplateResponse>>> {
    let config_template_repo = ConfigTemplateRepository::new(std::sync::Arc::new(db));
    
    // Validate template name is unique
    if !config_template_repo.validate_template_name(&request.name, None).await? {
        return Err(AppError::validation("Template name already exists"));
    }
    
    // Validate template content
    let template_model = config_templates::Model {
        id: Uuid::new_v4(),
        name: request.name.clone(),
        description: request.description.clone(),
        template_content: request.template_content.clone(),
        template_type: request.template_type.clone(),
        version: request.version.clone().unwrap_or_else(|| "1.0".to_string()),
        is_active: true,
        is_system_default: request.is_system_default.unwrap_or(false),
        sort_order: request.sort_order.unwrap_or(0),
        created_at: chrono::Utc::now().into(),
        updated_at: chrono::Utc::now().into(),
        created_by: user_context.user_id,
        updated_by: user_context.user_id,
    };
    
    if let Err(error) = template_model.validate_template() {
        return Err(AppError::validation(&error));
    }
    
    let template = config_template_repo.create_template(
        &request.name,
        request.description.as_deref(),
        &request.template_content,
        &request.template_type,
        request.version.as_deref(),
        request.is_system_default.unwrap_or(false),
        request.sort_order.unwrap_or(0),
        user_context.user_id,
    ).await?;
    
    let response = ConfigTemplateResponse::from(template);
    Ok(Json(success(response)))
}

/// PUT /api/admin/config-templates/:id - Update configuration template
pub async fn update_config_template(
    State(db): State<DatabaseConnection>,
    Path(id): Path<Uuid>,
    user_context: UserContext,
    Json(request): Json<UpdateConfigTemplateRequest>,
) -> AppResult<Json<ApiResponse<ConfigTemplateResponse>>> {
    let config_template_repo = ConfigTemplateRepository::new(std::sync::Arc::new(db));
    
    // Check if template exists
    let _existing = config_template_repo.find_by_id(id).await?
        .ok_or(AppError::not_found("Configuration template not found"))?;
    
    // Validate template name is unique if provided
    if let Some(name) = &request.name {
        if !config_template_repo.validate_template_name(name, Some(id)).await? {
            return Err(AppError::validation("Template name already exists"));
        }
    }
    
    // Validate template content if provided
    if let Some(template_content) = &request.template_content {
        if template_content.is_empty() {
            return Err(AppError::validation("Template content cannot be empty"));
        }
        if !template_content.contains("{{") {
            return Err(AppError::validation("Template should contain at least one placeholder"));
        }
    }
    
    let template = config_template_repo.update_template(
        id,
        request.name.as_deref(),
        request.description.as_deref(),
        request.template_content.as_deref(),
        request.template_type.as_deref(),
        request.version.as_deref(),
        request.is_active,
        request.is_system_default,
        request.sort_order,
        user_context.user_id,
    ).await?;
    
    let response = ConfigTemplateResponse::from(template);
    Ok(Json(success(response)))
}

/// DELETE /api/admin/config-templates/:id - Delete configuration template
pub async fn delete_config_template(
    State(db): State<DatabaseConnection>,
    Path(id): Path<Uuid>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<()>>> {
    let config_template_repo = ConfigTemplateRepository::new(std::sync::Arc::new(db));
    
    // Check if template exists
    let _existing = config_template_repo.find_by_id(id).await?
        .ok_or(AppError::not_found("Configuration template not found"))?;
    
    config_template_repo.delete(id).await?;
    
    Ok(Json(success(())))
}

/// POST /api/admin/config-templates/:id/duplicate - Duplicate configuration template
pub async fn duplicate_config_template(
    State(db): State<DatabaseConnection>,
    Path(id): Path<Uuid>,
    user_context: UserContext,
    Json(request): Json<DuplicateConfigTemplateRequest>,
) -> AppResult<Json<ApiResponse<ConfigTemplateResponse>>> {
    let config_template_repo = ConfigTemplateRepository::new(std::sync::Arc::new(db));
    
    // Validate new template name is unique
    if !config_template_repo.validate_template_name(&request.new_name, None).await? {
        return Err(AppError::validation("Template name already exists"));
    }
    
    let template = config_template_repo.duplicate_template(
        id,
        &request.new_name,
        user_context.user_id,
    ).await?;
    
    let response = ConfigTemplateResponse::from(template);
    Ok(Json(success(response)))
}

/// PUT /api/admin/config-templates/:id/set-system-default - Set as system default
pub async fn set_system_default(
    State(db): State<DatabaseConnection>,
    Path(id): Path<Uuid>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<ConfigTemplateResponse>>> {
    let config_template_repo = ConfigTemplateRepository::new(std::sync::Arc::new(db));
    
    // Get template to determine type
    let existing = config_template_repo.find_by_id(id).await?
        .ok_or(AppError::not_found("Configuration template not found"))?;
    
    let template = config_template_repo.set_system_default(
        id,
        &existing.template_type,
        user_context.user_id,
    ).await?;
    
    let response = ConfigTemplateResponse::from(template);
    Ok(Json(success(response)))
}

/// PUT /api/admin/config-templates/:id/toggle-active - Toggle active status
pub async fn toggle_active(
    State(db): State<DatabaseConnection>,
    Path(id): Path<Uuid>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<ConfigTemplateResponse>>> {
    let config_template_repo = ConfigTemplateRepository::new(std::sync::Arc::new(db));
    
    let template = config_template_repo.toggle_active(id, user_context.user_id).await?;
    
    let response = ConfigTemplateResponse::from(template);
    Ok(Json(success(response)))
}

/// GET /api/admin/config-templates/stats - Get configuration template statistics
pub async fn get_config_template_stats(
    State(db): State<DatabaseConnection>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<ConfigTemplateStatsResponse>>> {
    let config_template_repo = ConfigTemplateRepository::new(std::sync::Arc::new(db));
    
    let (total_count, active_count, system_default_count) = config_template_repo.get_template_stats().await?;
    let template_types = config_template_repo.get_template_types().await?;
    
    let response = ConfigTemplateStatsResponse {
        total_count,
        active_count,
        system_default_count,
        template_types,
    };
    
    Ok(Json(success(response)))
}

/// GET /api/config-templates/types/:type - Get active templates by type (public endpoint)
pub async fn get_templates_by_type(
    State(db): State<DatabaseConnection>,
    Path(template_type): Path<String>,
) -> AppResult<Json<ApiResponse<ConfigTemplateListResponse>>> {
    let config_template_repo = ConfigTemplateRepository::new(std::sync::Arc::new(db));
    
    let templates = config_template_repo.find_by_type(&template_type).await?;
    
    let response = ConfigTemplateListResponse {
        total: templates.len(),
        templates: templates.into_iter().map(ConfigTemplateResponse::from).collect(),
    };
    
    Ok(Json(success(response)))
}

/// GET /api/config-templates/types/:type/default - Get system default template by type (public endpoint)
pub async fn get_system_default_by_type(
    State(db): State<DatabaseConnection>,
    Path(template_type): Path<String>,
) -> AppResult<Json<ApiResponse<ConfigTemplateResponse>>> {
    let config_template_repo = ConfigTemplateRepository::new(std::sync::Arc::new(db));
    
    let template = config_template_repo.find_system_default_by_type(&template_type).await?
        .ok_or(AppError::not_found("No system default template found for this type"))?;
    
    let response = ConfigTemplateResponse::from(template);
    Ok(Json(success(response)))
}

/// Create configuration template management routes (admin only)
pub fn admin_config_template_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/", get(list_config_templates).post(create_config_template))
        .route("/:id", get(get_config_template).put(update_config_template).delete(delete_config_template))
        .route("/:id/duplicate", post(duplicate_config_template))
        .route("/:id/set-system-default", put(set_system_default))
        .route("/:id/toggle-active", put(toggle_active))
        .route("/stats", get(get_config_template_stats))
}