// Subscription plans API endpoints

use axum::{
    extract::{State, Path},
    response::Json,
    routing::{get, post, put, delete},
    Router,
};
use sea_orm::{DatabaseConnection, ActiveModelTrait, Set};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use rust_decimal::Decimal;
use std::sync::Arc;
use crate::errors::{AppError, AppResult, ApiResponse, success};
use crate::repositories::{PlanRepository, Repository};
use crate::entities::plans;

/// Plan response structure for API
#[derive(Debug, Serialize, Clone)]
pub struct PlanResponse {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub price: Decimal,
    pub currency: String,
    pub duration_days: i32,
    pub duration_display: String,
    pub traffic_limit_gb: Option<Decimal>,
    pub traffic_limit_display: String,
    pub max_concurrent_devices: i32,
    pub max_concurrent_connections: i32,
    pub speed_limit_upload: Option<i32>,
    pub speed_limit_download: Option<i32>,
    pub speed_limit_display: String,
    pub priority_level: i32,
    pub is_active: bool,
    pub is_featured: bool,
    pub sort_order: i32,
    pub features: Vec<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

/// Plans list response with metadata
#[derive(Debug, Serialize)]
pub struct PlansListResponse {
    pub plans: Vec<PlanResponse>,
    pub total_count: usize,
    pub featured_plans: Vec<PlanResponse>,
    pub currencies: Vec<String>,
}

/// Create plan request structure
#[derive(Debug, Deserialize)]
pub struct CreatePlanRequest {
    pub name: String,
    pub description: Option<String>,
    pub price: Decimal,
    pub currency: Option<String>,
    pub duration_days: i32,
    pub traffic_limit_gb: Option<Decimal>,
    pub max_concurrent_devices: Option<i32>,
    pub max_concurrent_connections: Option<i32>,
    pub speed_limit_upload: Option<i32>,
    pub speed_limit_download: Option<i32>,
    pub priority_level: Option<i32>,
    pub config_template: String,
    pub is_active: Option<bool>,
    pub is_featured: Option<bool>,
    pub sort_order: Option<i32>,
}

/// Update plan request structure
#[derive(Debug, Deserialize)]
pub struct UpdatePlanRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub price: Option<Decimal>,
    pub currency: Option<String>,
    pub duration_days: Option<i32>,
    pub traffic_limit_gb: Option<Decimal>,
    pub max_concurrent_devices: Option<i32>,
    pub max_concurrent_connections: Option<i32>,
    pub speed_limit_upload: Option<i32>,
    pub speed_limit_download: Option<i32>,
    pub priority_level: Option<i32>,
    pub config_template: Option<String>,
    pub is_active: Option<bool>,
    pub is_featured: Option<bool>,
    pub sort_order: Option<i32>,
}

/// GET /api/plans/ - Get all available subscription plans
pub async fn get_plans(
    State(db): State<DatabaseConnection>,
) -> AppResult<Json<ApiResponse<PlansListResponse>>> {
    let plan_repo = PlanRepository::new(Arc::new(db));
    
    // Get all active plans
    let plans = plan_repo.find_active_plans().await?;
    
    // Convert to response format
    let plan_responses: Vec<PlanResponse> = plans
        .into_iter()
        .map(|plan| PlanResponse {
            id: plan.id,
            name: plan.name.clone(),
            description: plan.description.clone(),
            price: plan.price,
            currency: plan.currency.clone(),
            duration_days: plan.duration_days,
            duration_display: plan.duration_display(),
            traffic_limit_gb: plan.traffic_limit_gb,
            traffic_limit_display: plan.traffic_limit_display(),
            max_concurrent_devices: plan.max_concurrent_devices,
            max_concurrent_connections: plan.max_concurrent_connections,
            speed_limit_upload: plan.speed_limit_upload,
            speed_limit_download: plan.speed_limit_download,
            speed_limit_display: plan.speed_limit_display(),
            priority_level: plan.priority_level,
            is_active: plan.is_active,
            is_featured: plan.is_featured,
            sort_order: plan.sort_order,
            features: plan.get_features(),
            created_at: plan.created_at.into(),
            updated_at: plan.updated_at.into(),
        })
        .collect();
    
    // Separate featured plans
    let featured_plans: Vec<PlanResponse> = plan_responses
        .iter()
        .filter(|p| p.is_featured)
        .cloned()
        .collect();
    
    // Get unique currencies
    let currencies: Vec<String> = plan_responses
        .iter()
        .map(|p| p.currency.clone())
        .collect::<std::collections::HashSet<_>>()
        .into_iter()
        .collect();
    
    let response = PlansListResponse {
        total_count: plan_responses.len(),
        featured_plans,
        currencies,
        plans: plan_responses,
    };
    
    Ok(Json(success(response)))
}

/// GET /api/plans/:id - Get specific plan details
pub async fn get_plan_by_id(
    State(db): State<DatabaseConnection>,
    axum::extract::Path(plan_id): axum::extract::Path<Uuid>,
) -> AppResult<Json<ApiResponse<PlanResponse>>> {
    let plan_repo = PlanRepository::new(Arc::new(db));
    
    let plan = plan_repo.find_by_id(plan_id).await?
        .ok_or_else(|| AppError::not_found("Plan not found"))?;
    
    // Check if plan is active
    if !plan.is_available() {
        return Err(AppError::not_found("Plan not available"));
    }
    
    let response = PlanResponse {
        id: plan.id,
        name: plan.name.clone(),
        description: plan.description.clone(),
        price: plan.price,
        currency: plan.currency.clone(),
        duration_days: plan.duration_days,
        duration_display: plan.duration_display(),
        traffic_limit_gb: plan.traffic_limit_gb,
        traffic_limit_display: plan.traffic_limit_display(),
        max_concurrent_devices: plan.max_concurrent_devices,
        max_concurrent_connections: plan.max_concurrent_connections,
        speed_limit_upload: plan.speed_limit_upload,
        speed_limit_download: plan.speed_limit_download,
        speed_limit_display: plan.speed_limit_display(),
        priority_level: plan.priority_level,
        is_active: plan.is_active,
        is_featured: plan.is_featured,
        sort_order: plan.sort_order,
        features: plan.get_features(),
        created_at: plan.created_at.into(),
        updated_at: plan.updated_at.into(),
    };
    
    Ok(Json(success(response)))
}

/// POST /api/plans/ - Create a new subscription plan (Admin only)
pub async fn create_plan(
    State(db): State<DatabaseConnection>,
    Json(request): Json<CreatePlanRequest>,
) -> AppResult<Json<ApiResponse<PlanResponse>>> {
    let plan_repo = PlanRepository::new(Arc::new(db));
    
    // Check if plan name already exists
    if let Some(_) = plan_repo.find_by_name(&request.name).await? {
        return Err(AppError::conflict("Plan with this name already exists"));
    }
    
    // Create new plan
    let new_plan = plans::ActiveModel {
        id: Set(Uuid::new_v4()),
        name: Set(request.name),
        description: Set(request.description),
        price: Set(request.price),
        currency: Set(request.currency.unwrap_or_else(|| "USD".to_string())),
        duration_days: Set(request.duration_days),
        traffic_limit_gb: Set(request.traffic_limit_gb),
        max_concurrent_devices: Set(request.max_concurrent_devices.unwrap_or(1)),
        max_concurrent_connections: Set(request.max_concurrent_connections.unwrap_or(10)),
        speed_limit_upload: Set(request.speed_limit_upload),
        speed_limit_download: Set(request.speed_limit_download),
        priority_level: Set(request.priority_level.unwrap_or(1)),
        config_template: Set(request.config_template),
        config_template_id: Set(None),
        is_active: Set(request.is_active.unwrap_or(true)),
        is_featured: Set(request.is_featured.unwrap_or(false)),
        sort_order: Set(request.sort_order.unwrap_or(0)),
        created_at: Set(chrono::Utc::now().into()),
        updated_at: Set(chrono::Utc::now().into()),
    };
    
    let plan = plan_repo.create(new_plan).await
        .map_err(|e| AppError::internal(format!("Failed to create plan: {}", e)))?;
    
    let response = PlanResponse {
        id: plan.id,
        name: plan.name.clone(),
        description: plan.description.clone(),
        price: plan.price,
        currency: plan.currency.clone(),
        duration_days: plan.duration_days,
        duration_display: plan.duration_display(),
        traffic_limit_gb: plan.traffic_limit_gb,
        traffic_limit_display: plan.traffic_limit_display(),
        max_concurrent_devices: plan.max_concurrent_devices,
        max_concurrent_connections: plan.max_concurrent_connections,
        speed_limit_upload: plan.speed_limit_upload,
        speed_limit_download: plan.speed_limit_download,
        speed_limit_display: plan.speed_limit_display(),
        priority_level: plan.priority_level,
        is_active: plan.is_active,
        is_featured: plan.is_featured,
        sort_order: plan.sort_order,
        features: plan.get_features(),
        created_at: plan.created_at.into(),
        updated_at: plan.updated_at.into(),
    };
    
    Ok(Json(success(response)))
}

/// PUT /api/plans/:id - Update an existing subscription plan (Admin only)
pub async fn update_plan(
    State(db): State<DatabaseConnection>,
    Path(plan_id): Path<Uuid>,
    Json(request): Json<UpdatePlanRequest>,
) -> AppResult<Json<ApiResponse<PlanResponse>>> {
    let plan_repo = PlanRepository::new(Arc::new(db));
    
    // Check if plan exists
    let existing_plan = plan_repo.find_by_id(plan_id).await?
        .ok_or_else(|| AppError::not_found("Plan not found"))?;
    
    // Check if name conflict exists (if name is being updated)
    if let Some(ref new_name) = request.name {
        if new_name != &existing_plan.name {
            if let Some(_) = plan_repo.find_by_name(new_name).await? {
                return Err(AppError::conflict("Plan with this name already exists"));
            }
        }
    }
    
    // Create updated plan
    let updated_plan = plans::ActiveModel {
        id: Set(plan_id),
        name: Set(request.name.unwrap_or(existing_plan.name)),
        description: Set(request.description.or(existing_plan.description)),
        price: Set(request.price.unwrap_or(existing_plan.price)),
        currency: Set(request.currency.unwrap_or(existing_plan.currency)),
        duration_days: Set(request.duration_days.unwrap_or(existing_plan.duration_days)),
        traffic_limit_gb: Set(request.traffic_limit_gb.or(existing_plan.traffic_limit_gb)),
        max_concurrent_devices: Set(request.max_concurrent_devices.unwrap_or(existing_plan.max_concurrent_devices)),
        max_concurrent_connections: Set(request.max_concurrent_connections.unwrap_or(existing_plan.max_concurrent_connections)),
        speed_limit_upload: Set(request.speed_limit_upload.or(existing_plan.speed_limit_upload)),
        speed_limit_download: Set(request.speed_limit_download.or(existing_plan.speed_limit_download)),
        priority_level: Set(request.priority_level.unwrap_or(existing_plan.priority_level)),
        config_template: Set(request.config_template.unwrap_or(existing_plan.config_template)),
        config_template_id: Set(existing_plan.config_template_id),
        is_active: Set(request.is_active.unwrap_or(existing_plan.is_active)),
        is_featured: Set(request.is_featured.unwrap_or(existing_plan.is_featured)),
        sort_order: Set(request.sort_order.unwrap_or(existing_plan.sort_order)),
        created_at: Set(existing_plan.created_at),
        updated_at: Set(chrono::Utc::now().into()),
    };
    
    let plan = plan_repo.update(plan_id, updated_plan).await
        .map_err(|e| AppError::internal(format!("Failed to update plan: {}", e)))?;
    
    let response = PlanResponse {
        id: plan.id,
        name: plan.name.clone(),
        description: plan.description.clone(),
        price: plan.price,
        currency: plan.currency.clone(),
        duration_days: plan.duration_days,
        duration_display: plan.duration_display(),
        traffic_limit_gb: plan.traffic_limit_gb,
        traffic_limit_display: plan.traffic_limit_display(),
        max_concurrent_devices: plan.max_concurrent_devices,
        max_concurrent_connections: plan.max_concurrent_connections,
        speed_limit_upload: plan.speed_limit_upload,
        speed_limit_download: plan.speed_limit_download,
        speed_limit_display: plan.speed_limit_display(),
        priority_level: plan.priority_level,
        is_active: plan.is_active,
        is_featured: plan.is_featured,
        sort_order: plan.sort_order,
        features: plan.get_features(),
        created_at: plan.created_at.into(),
        updated_at: plan.updated_at.into(),
    };
    
    Ok(Json(success(response)))
}

/// DELETE /api/plans/:id - Delete a subscription plan (Admin only)
pub async fn delete_plan(
    State(db): State<DatabaseConnection>,
    Path(plan_id): Path<Uuid>,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let plan_repo = PlanRepository::new(Arc::new(db));
    
    // Check if plan exists
    let _plan = plan_repo.find_by_id(plan_id).await?
        .ok_or_else(|| AppError::not_found("Plan not found"))?;
    
    // TODO: Check if plan has active subscriptions before deleting
    // This would require checking with SubscriptionRepository
    
    plan_repo.delete(plan_id).await
        .map_err(|e| AppError::internal(format!("Failed to delete plan: {}", e)))?;
    
    Ok(Json(success(serde_json::json!({
        "message": "Plan deleted successfully",
        "plan_id": plan_id
    }))))
}

/// Create public plan routes (no authentication required)
pub fn public_plan_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/", get(get_plans))
        .route("/:id", get(get_plan_by_id))
}

/// Create admin plan routes (admin authentication required)
pub fn admin_plan_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/", post(create_plan))
        .route("/:id", put(update_plan).delete(delete_plan))
}