use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};
use std::fmt;

#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "subscription_status")]
pub enum SubscriptionStatus {
    #[sea_orm(string_value = "active")]
    Active,
    #[sea_orm(string_value = "expired")]
    Expired,
    #[sea_orm(string_value = "cancelled")]
    Cancelled,
    #[sea_orm(string_value = "suspended")]
    Suspended,
}

impl fmt::Display for SubscriptionStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SubscriptionStatus::Active => write!(f, "active"),
            SubscriptionStatus::Expired => write!(f, "expired"),
            SubscriptionStatus::Cancelled => write!(f, "cancelled"),
            SubscriptionStatus::Suspended => write!(f, "suspended"),
        }
    }
}

#[derive(Debug, <PERSON>lone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "order_status")]
pub enum OrderStatus {
    #[sea_orm(string_value = "pending_payment")]
    PendingPayment,
    #[sea_orm(string_value = "pending")]
    Pending,
    #[sea_orm(string_value = "paid")]
    Paid,
    #[sea_orm(string_value = "processing")]
    Processing,
    #[sea_orm(string_value = "completed")]
    Completed,
    #[sea_orm(string_value = "cancelled")]
    Cancelled,
    #[sea_orm(string_value = "refunded")]
    Refunded,
    #[sea_orm(string_value = "failed")]
    Failed,
}

impl fmt::Display for OrderStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            OrderStatus::PendingPayment => write!(f, "pending_payment"),
            OrderStatus::Pending => write!(f, "pending"),
            OrderStatus::Paid => write!(f, "paid"),
            OrderStatus::Processing => write!(f, "processing"),
            OrderStatus::Completed => write!(f, "completed"),
            OrderStatus::Cancelled => write!(f, "cancelled"),
            OrderStatus::Refunded => write!(f, "refunded"),
            OrderStatus::Failed => write!(f, "failed"),
        }
    }
}

#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "payment_method_type")]
pub enum PaymentMethodType {
    #[sea_orm(string_value = "manual")]
    Manual,
    #[sea_orm(string_value = "credit_card")]
    CreditCard,
    #[sea_orm(string_value = "paypal")]
    PayPal,
    #[sea_orm(string_value = "bank_transfer")]
    BankTransfer,
    #[sea_orm(string_value = "cryptocurrency")]
    Cryptocurrency,
    #[sea_orm(string_value = "alipay")]
    Alipay,
    #[sea_orm(string_value = "wechat_pay")]
    WechatPay,
    #[sea_orm(string_value = "wechat")]
    Wechat,
    #[sea_orm(string_value = "crypto")]
    Crypto,
}

impl fmt::Display for PaymentMethodType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            PaymentMethodType::Manual => write!(f, "manual"),
            PaymentMethodType::CreditCard => write!(f, "credit_card"),
            PaymentMethodType::PayPal => write!(f, "paypal"),
            PaymentMethodType::BankTransfer => write!(f, "bank_transfer"),
            PaymentMethodType::Cryptocurrency => write!(f, "cryptocurrency"),
            PaymentMethodType::Alipay => write!(f, "alipay"),
            PaymentMethodType::WechatPay => write!(f, "wechat_pay"),
            PaymentMethodType::Wechat => write!(f, "wechat"),
            PaymentMethodType::Crypto => write!(f, "crypto"),
        }
    }
}

#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "announcement_audience")]
pub enum AnnouncementAudience {
    #[sea_orm(string_value = "all")]
    All,
    #[sea_orm(string_value = "active_users")]
    ActiveUsers,
    #[sea_orm(string_value = "plan_specific")]
    PlanSpecific,
    #[sea_orm(string_value = "specific_plans")]
    SpecificPlans,
    #[sea_orm(string_value = "new_users")]
    NewUsers,
}

#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "announcement_type")]
pub enum AnnouncementType {
    #[sea_orm(string_value = "info")]
    Info,
    #[sea_orm(string_value = "warning")]
    Warning,
    #[sea_orm(string_value = "maintenance")]
    Maintenance,
    #[sea_orm(string_value = "promotion")]
    Promotion,
}

#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "setting_value_type")]
pub enum SettingValueType {
    #[sea_orm(string_value = "string")]
    String,
    #[sea_orm(string_value = "integer")]
    Integer,
    #[sea_orm(string_value = "decimal")]
    Decimal,
    #[sea_orm(string_value = "boolean")]
    Boolean,
    #[sea_orm(string_value = "json")]
    Json,
}