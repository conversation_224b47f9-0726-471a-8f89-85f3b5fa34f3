use sea_orm::entity::prelude::*;
use sea_orm::prelude::StringLen;
use serde::{Deserialize, Serialize};
use rust_decimal::Decimal;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "orders")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    
    #[sea_orm(column_type = "String(StringLen::N(32))", unique)]
    pub order_number: String,
    
    pub user_id: Uuid,
    pub plan_id: Uuid,
    
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub amount: Decimal,
    
    #[sea_orm(column_type = "String(StringLen::N(3))", default_value = "USD")]
    pub currency: String,
    
    #[sea_orm(default_value = "1")]
    pub quantity: i32,
    
    pub payment_method: Option<PaymentMethodType>,
    
    #[sea_orm(column_type = "String(StringLen::N(255))", nullable)]
    pub payment_reference: Option<String>,
    
    #[sea_orm(column_type = "String(StringLen::N(500))", nullable)]
    pub payment_proof_url: Option<String>,
    
    pub status: OrderStatus,
    
    pub processed_by: Option<Uuid>,
    pub processed_at: Option<ChronoDateTimeWithTimeZone>,
    
    pub notes: Option<String>,
    
    pub created_at: ChronoDateTimeWithTimeZone,
    pub updated_at: ChronoDateTimeWithTimeZone,
    pub expires_at: ChronoDateTimeWithTimeZone,
}

pub use crate::entities::enums::{OrderStatus, PaymentMethodType};

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::users::Entity",
        from = "Column::UserId",
        to = "super::users::Column::Id"
    )]
    User,
    
    #[sea_orm(
        belongs_to = "super::plans::Entity",
        from = "Column::PlanId",
        to = "super::plans::Column::Id"
    )]
    Plan,
    
    #[sea_orm(
        belongs_to = "super::users::Entity",
        from = "Column::ProcessedBy",
        to = "super::users::Column::Id"
    )]
    ProcessedByUser,
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}

impl Related<super::plans::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Plan.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Helper methods for Order model
impl Model {
    /// Check if order is expired
    pub fn is_expired(&self) -> bool {
        chrono::Utc::now().timestamp() >= self.expires_at.timestamp()
    }
    
    /// Check if order is pending payment
    pub fn is_pending_payment(&self) -> bool {
        self.status == OrderStatus::PendingPayment && !self.is_expired()
    }
    
    /// Check if order is completed
    pub fn is_completed(&self) -> bool {
        self.status == OrderStatus::Completed
    }
    
    /// Check if order can be cancelled
    pub fn can_be_cancelled(&self) -> bool {
        matches!(self.status, OrderStatus::PendingPayment | OrderStatus::Paid)
    }
    
    /// Check if order can be refunded
    pub fn can_be_refunded(&self) -> bool {
        matches!(self.status, OrderStatus::Completed | OrderStatus::Processing)
    }
    
    /// Get order status display
    pub fn status_display(&self) -> String {
        match self.status {
            OrderStatus::PendingPayment => {
                if self.is_expired() {
                    "已过期".to_string()
                } else {
                    "等待付款".to_string()
                }
            }
            OrderStatus::Pending => "待处理".to_string(),
            OrderStatus::Paid => "已付款".to_string(),
            OrderStatus::Processing => "处理中".to_string(),
            OrderStatus::Completed => "已完成".to_string(),
            OrderStatus::Cancelled => "已取消".to_string(),
            OrderStatus::Refunded => "已退款".to_string(),
            OrderStatus::Failed => "支付失败".to_string(),
        }
    }
    
    /// Get payment method display
    pub fn payment_method_display(&self) -> String {
        match self.payment_method {
            Some(PaymentMethodType::Manual) => "手动付款".to_string(),
            Some(PaymentMethodType::CreditCard) => "信用卡".to_string(),
            Some(PaymentMethodType::Alipay) => "支付宝".to_string(),
            Some(PaymentMethodType::Wechat) => "微信支付".to_string(),
            Some(PaymentMethodType::WechatPay) => "微信支付".to_string(),
            Some(PaymentMethodType::PayPal) => "PayPal".to_string(),
            Some(PaymentMethodType::Crypto) => "加密货币".to_string(),
            Some(PaymentMethodType::Cryptocurrency) => "加密货币".to_string(),
            Some(PaymentMethodType::BankTransfer) => "银行转账".to_string(),
            None => "未选择".to_string(),
        }
    }
    
    /// Get total amount considering quantity
    pub fn total_amount(&self) -> Decimal {
        self.amount * rust_decimal::Decimal::from(self.quantity)
    }
    
    /// Get remaining time until expiration
    pub fn remaining_time(&self) -> chrono::Duration {
        let now = chrono::Utc::now().timestamp();
        let expires_timestamp = self.expires_at.timestamp();
        chrono::Duration::seconds(expires_timestamp - now)
    }
    
    /// Generate order number (if not set)
    pub fn generate_order_number() -> String {
        let now = chrono::Utc::now();
        let timestamp = now.timestamp();
        let date_str = now.format("%Y%m%d").to_string();
        let random_suffix = format!("{:06}", timestamp % 1000000);
        format!("ORD-{}-{}", date_str, random_suffix)
    }
    
    /// Get payment instructions based on payment method
    pub fn get_payment_instructions(&self) -> Option<PaymentInstructions> {
        match self.payment_method {
            Some(PaymentMethodType::Manual) => Some(PaymentInstructions {
                method: "手动付款".to_string(),
                details: "请联系客服并提供订单号完成支付".to_string(),
                contact: "<EMAIL>".to_string(),
            }),
            Some(PaymentMethodType::CreditCard) => Some(PaymentInstructions {
                method: "信用卡".to_string(),
                details: "请使用信用卡完成付款".to_string(),
                contact: "".to_string(),
            }),
            Some(PaymentMethodType::Alipay) => Some(PaymentInstructions {
                method: "支付宝".to_string(),
                details: "请使用支付宝扫码付款".to_string(),
                contact: "".to_string(),
            }),
            Some(PaymentMethodType::Wechat) | Some(PaymentMethodType::WechatPay) => Some(PaymentInstructions {
                method: "微信支付".to_string(),
                details: "请使用微信扫码付款".to_string(),
                contact: "".to_string(),
            }),
            Some(PaymentMethodType::PayPal) => Some(PaymentInstructions {
                method: "PayPal".to_string(),
                details: "请使用PayPal完成付款".to_string(),
                contact: "".to_string(),
            }),
            Some(PaymentMethodType::Crypto) | Some(PaymentMethodType::Cryptocurrency) => Some(PaymentInstructions {
                method: "加密货币".to_string(),
                details: "请使用指定的加密货币地址完成付款".to_string(),
                contact: "".to_string(),
            }),
            Some(PaymentMethodType::BankTransfer) => Some(PaymentInstructions {
                method: "银行转账".to_string(),
                details: "请使用银行转账完成付款".to_string(),
                contact: "".to_string(),
            }),
            None => None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaymentInstructions {
    pub method: String,
    pub details: String,
    pub contact: String,
}