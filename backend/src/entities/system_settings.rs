use sea_orm::entity::prelude::*;
use sea_orm::{Set, prelude::StringLen};
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "system_settings")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    
    #[sea_orm(column_type = "String(StringLen::N(100))", unique)]
    pub key: String,
    
    pub value: Option<String>,
    
    pub value_type: SettingValueType,
    
    pub description: Option<String>,
    
    #[sea_orm(default_value = "false")]
    pub is_public: bool,
    
    pub created_at: ChronoDateTimeWithTimeZone,
    pub updated_at: ChronoDateTimeWithTimeZone,
}

#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "setting_value_type")]
pub enum SettingValueType {
    #[sea_orm(string_value = "string")]
    String,
    #[sea_orm(string_value = "integer")]
    Integer,
    #[sea_orm(string_value = "decimal")]
    Decimal,
    #[sea_orm(string_value = "boolean")]
    Boolean,
    #[sea_orm(string_value = "json")]
    Json,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}

// Helper methods for SystemSettings model
impl Model {
    /// Get typed value based on value_type
    pub fn get_typed_value(&self) -> Result<TypedValue, String> {
        let value = self.value.as_ref().ok_or("Value is null")?;
        
        match self.value_type {
            SettingValueType::String => Ok(TypedValue::String(value.clone())),
            SettingValueType::Integer => {
                value.parse::<i64>()
                    .map(TypedValue::Integer)
                    .map_err(|_| "Invalid integer value".to_string())
            }
            SettingValueType::Decimal => {
                value.parse::<f64>()
                    .map(TypedValue::Decimal)
                    .map_err(|_| "Invalid decimal value".to_string())
            }
            SettingValueType::Boolean => {
                value.parse::<bool>()
                    .map(TypedValue::Boolean)
                    .map_err(|_| "Invalid boolean value".to_string())
            }
            SettingValueType::Json => {
                serde_json::from_str(value)
                    .map(TypedValue::Json)
                    .map_err(|_| "Invalid JSON value".to_string())
            }
        }
    }
    
    /// Get string value
    pub fn get_string(&self) -> Option<String> {
        self.value.clone()
    }
    
    /// Get integer value
    pub fn get_integer(&self) -> Option<i64> {
        self.value.as_ref()?.parse().ok()
    }
    
    /// Get decimal value
    pub fn get_decimal(&self) -> Option<f64> {
        self.value.as_ref()?.parse().ok()
    }
    
    /// Get boolean value
    pub fn get_boolean(&self) -> Option<bool> {
        self.value.as_ref()?.parse().ok()
    }
    
    /// Get JSON value
    pub fn get_json<T: for<'de> Deserialize<'de>>(&self) -> Option<T> {
        let value = self.value.as_ref()?;
        serde_json::from_str(value).ok()
    }
    
    /// Create new setting with typed value
    pub fn new_with_typed_value(key: String, value: TypedValue, description: Option<String>, is_public: bool) -> ActiveModel {
        let (value_str, value_type) = match value {
            TypedValue::String(s) => (s, SettingValueType::String),
            TypedValue::Integer(i) => (i.to_string(), SettingValueType::Integer),
            TypedValue::Decimal(d) => (d.to_string(), SettingValueType::Decimal),
            TypedValue::Boolean(b) => (b.to_string(), SettingValueType::Boolean),
            TypedValue::Json(j) => (serde_json::to_string(&j).unwrap_or_default(), SettingValueType::Json),
        };
        
        ActiveModel {
            id: Set(Uuid::new_v4()),
            key: Set(key),
            value: Set(Some(value_str)),
            value_type: Set(value_type),
            description: Set(description),
            is_public: Set(is_public),
            created_at: Set(chrono::Utc::now().into()),
            updated_at: Set(chrono::Utc::now().into()),
        }
    }
    
    /// Get default system settings
    pub fn get_default_settings() -> Vec<(String, TypedValue, String, bool)> {
        vec![
            ("site_name".to_string(), TypedValue::String("VPN Service".to_string()), "站点名称".to_string(), true),
            ("cleanup_inactive_users_days".to_string(), TypedValue::Integer(30), "清理未购买套餐用户的天数".to_string(), false),
            ("renewal_reminder_days".to_string(), TypedValue::Integer(7), "续费提醒提前天数".to_string(), false),
            ("max_login_attempts".to_string(), TypedValue::Integer(5), "最大登录尝试次数".to_string(), false),
            ("login_lockout_minutes".to_string(), TypedValue::Integer(30), "登录锁定时间(分钟)".to_string(), false),
            ("renewal_url_template".to_string(), TypedValue::String("https://your-domain.com/user/subscribe?user_id={{user_id}}&token={{token}}".to_string()), "续费链接模板".to_string(), false),
            ("support_contact".to_string(), TypedValue::String("<EMAIL>".to_string()), "客服联系方式".to_string(), true),
            ("maintenance_mode".to_string(), TypedValue::Boolean(false), "维护模式".to_string(), true),
            ("maintenance_message".to_string(), TypedValue::String("".to_string()), "维护提示信息".to_string(), true),
        ]
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TypedValue {
    String(String),
    Integer(i64),
    Decimal(f64),
    Boolean(bool),
    Json(serde_json::Value),
}

impl TypedValue {
    pub fn as_string(&self) -> Option<&str> {
        match self {
            TypedValue::String(s) => Some(s),
            _ => None,
        }
    }
    
    pub fn as_integer(&self) -> Option<i64> {
        match self {
            TypedValue::Integer(i) => Some(*i),
            _ => None,
        }
    }
    
    pub fn as_decimal(&self) -> Option<f64> {
        match self {
            TypedValue::Decimal(d) => Some(*d),
            _ => None,
        }
    }
    
    pub fn as_boolean(&self) -> Option<bool> {
        match self {
            TypedValue::Boolean(b) => Some(*b),
            _ => None,
        }
    }
    
    pub fn as_json(&self) -> Option<&serde_json::Value> {
        match self {
            TypedValue::Json(j) => Some(j),
            _ => None,
        }
    }
}