use sea_orm::entity::prelude::*;
use sea_orm::prelude::StringLen;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "announcements")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    
    #[sea_orm(column_type = "String(StringLen::N(200))")]
    pub title: String,
    
    pub content: String,
    
    pub target_audience: AnnouncementAudience,
    
    pub target_plan_ids: Option<Json>,
    
    pub announcement_type: AnnouncementType,
    
    #[sea_orm(default_value = "false")]
    pub is_popup: bool,
    
    #[sea_orm(default_value = "false")]
    pub is_pinned: bool,
    
    #[sea_orm(default_value = "true")]
    pub is_active: bool,
    
    pub publish_at: ChronoDateTimeWithTimeZone,
    pub expires_at: Option<ChronoDateTimeWithTimeZone>,
    
    pub created_at: ChronoDateTimeWithTimeZone,
    pub updated_at: ChronoDateTimeWithTimeZone,
    
    pub created_by: Uuid,
}

pub use crate::entities::enums::{AnnouncementAudience, AnnouncementType};

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::users::Entity",
        from = "Column::CreatedBy",
        to = "super::users::Column::Id"
    )]
    Creator,
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Creator.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Helper methods for Announcement model
impl Model {
    /// Check if announcement is currently published
    pub fn is_published(&self) -> bool {
        let now = chrono::Utc::now().timestamp();
        self.is_active && 
        self.publish_at.timestamp() <= now &&
        self.expires_at.map_or(true, |exp| exp.timestamp() > now)
    }
    
    /// Check if announcement is expired
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            chrono::Utc::now().timestamp() >= expires_at.timestamp()
        } else {
            false
        }
    }
    
    /// Get announcement type display
    pub fn type_display(&self) -> String {
        match self.announcement_type {
            AnnouncementType::Info => "信息".to_string(),
            AnnouncementType::Warning => "警告".to_string(),
            AnnouncementType::Maintenance => "维护".to_string(),
            AnnouncementType::Promotion => "推广".to_string(),
        }
    }
    
    /// Get target audience display
    pub fn audience_display(&self) -> String {
        match self.target_audience {
            AnnouncementAudience::All => "所有用户".to_string(),
            AnnouncementAudience::ActiveUsers => "活跃用户".to_string(),
            AnnouncementAudience::NewUsers => "新用户".to_string(),
            AnnouncementAudience::PlanSpecific => "特定套餐用户".to_string(),
            AnnouncementAudience::SpecificPlans => "特定套餐用户".to_string(),
        }
    }
    
    /// Get target plan IDs if audience is specific plans
    pub fn get_target_plan_ids(&self) -> Vec<Uuid> {
        if let Some(json_value) = &self.target_plan_ids {
            if let Some(array) = json_value.as_array() {
                return array
                    .iter()
                    .filter_map(|v| v.as_str())
                    .filter_map(|s| s.parse::<Uuid>().ok())
                    .collect();
            }
        }
        Vec::new()
    }
    
    /// Check if announcement applies to a specific user
    pub fn applies_to_user(&self, user_subscription_status: Option<&str>, user_plan_id: Option<Uuid>) -> bool {
        if !self.is_published() {
            return false;
        }
        
        match self.target_audience {
            AnnouncementAudience::All => true,
            AnnouncementAudience::ActiveUsers => {
                user_subscription_status.map_or(false, |status| status == "active")
            }
            AnnouncementAudience::NewUsers => {
                user_subscription_status.map_or(true, |status| status == "new")
            }
            AnnouncementAudience::PlanSpecific | AnnouncementAudience::SpecificPlans => {
                if let Some(plan_id) = user_plan_id {
                    self.get_target_plan_ids().contains(&plan_id)
                } else {
                    false
                }
            }
        }
    }
    
    /// Get announcement priority score (higher = more important)
    pub fn priority_score(&self) -> u8 {
        let mut score = 0;
        
        // Type priority
        score += match self.announcement_type {
            AnnouncementType::Warning => 80,
            AnnouncementType::Maintenance => 60,
            AnnouncementType::Info => 40,
            AnnouncementType::Promotion => 20,
        };
        
        // Pinned announcements get higher priority
        if self.is_pinned {
            score += 50;
        }
        
        // Popup announcements get higher priority
        if self.is_popup {
            score += 30;
        }
        
        score
    }
}