use sea_orm::entity::prelude::*;
use sea_orm::prelude::StringLen;
use serde::{Deserialize, Serialize};
use rust_decimal::Decimal;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "plans")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    
    #[sea_orm(column_type = "String(StringLen::N(100))")]
    pub name: String,
    
    pub description: Option<String>,
    
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub price: Decimal,
    
    #[sea_orm(column_type = "String(StringLen::N(3))", default_value = "USD")]
    pub currency: String,
    
    pub duration_days: i32,
    
    #[sea_orm(column_type = "Decimal(Some((10, 3)))", nullable)]
    pub traffic_limit_gb: Option<Decimal>,
    
    #[sea_orm(default_value = "1")]
    pub max_concurrent_devices: i32,
    
    #[sea_orm(default_value = "10")]
    pub max_concurrent_connections: i32,
    
    pub speed_limit_upload: Option<i32>,
    pub speed_limit_download: Option<i32>,
    
    #[sea_orm(default_value = "1")]
    pub priority_level: i32,
    
    pub config_template: String,
    
    pub config_template_id: Option<Uuid>,
    
    #[sea_orm(default_value = "true")]
    pub is_active: bool,
    
    #[sea_orm(default_value = "false")]
    pub is_featured: bool,
    
    #[sea_orm(default_value = "0")]
    pub sort_order: i32,
    
    pub created_at: ChronoDateTimeWithTimeZone,
    pub updated_at: ChronoDateTimeWithTimeZone,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::subscriptions::Entity")]
    Subscriptions,
    
    #[sea_orm(has_many = "super::orders::Entity")]
    Orders,
    
    #[sea_orm(has_many = "super::subscriptions::Entity")]
    AutoRenewSubscriptions,
    
    #[sea_orm(belongs_to = "super::config_templates::Entity", 
              from = "Column::ConfigTemplateId", 
              to = "super::config_templates::Column::Id")]
    ConfigTemplate,
}

impl Related<super::subscriptions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Subscriptions.def()
    }
}

impl Related<super::orders::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Orders.def()
    }
}

impl Related<super::config_templates::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ConfigTemplate.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Helper methods for Plan model
impl Model {
    /// Check if plan is available for purchase
    pub fn is_available(&self) -> bool {
        self.is_active
    }
    
    /// Get plan duration in a human-readable format
    pub fn duration_display(&self) -> String {
        if self.duration_days == 0 {
            "永久".to_string()
        } else if self.duration_days == 1 {
            "1天".to_string()
        } else if self.duration_days == 7 {
            "1周".to_string()
        } else if self.duration_days == 30 {
            "1个月".to_string()
        } else if self.duration_days == 365 {
            "1年".to_string()
        } else {
            format!("{}天", self.duration_days)
        }
    }
    
    /// Get traffic limit in a human-readable format
    pub fn traffic_limit_display(&self) -> String {
        if let Some(limit) = self.traffic_limit_gb {
            if limit == rust_decimal::Decimal::from(0) {
                "无限制".to_string()
            } else {
                format!("{}GB", limit)
            }
        } else {
            "无限制".to_string()
        }
    }
    
    /// Get speed limit display
    pub fn speed_limit_display(&self) -> String {
        match (self.speed_limit_upload, self.speed_limit_download) {
            (None, None) => "无限制".to_string(),
            (Some(up), None) => format!("上传{}Mbps", up),
            (None, Some(down)) => format!("下载{}Mbps", down),
            (Some(up), Some(down)) => format!("上传{}Mbps/下载{}Mbps", up, down),
        }
    }
    
    /// Get features list based on plan configuration
    pub fn get_features(&self) -> Vec<String> {
        let mut features = Vec::new();
        
        features.push(format!("{}设备同时连接", self.max_concurrent_devices));
        features.push(format!("{}并发连接", self.max_concurrent_connections));
        features.push(format!("流量: {}", self.traffic_limit_display()));
        features.push(format!("速度: {}", self.speed_limit_display()));
        features.push(format!("有效期: {}", self.duration_display()));
        
        if self.priority_level > 5 {
            features.push("高优先级".to_string());
        }
        
        if self.is_featured {
            features.push("推荐套餐".to_string());
        }
        
        features
    }
}