use sea_orm::entity::prelude::*;
use sea_orm::prelude::StringLen;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "config_templates")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    
    #[sea_orm(column_type = "String(StringLen::N(100))", unique)]
    pub name: String,
    
    #[sea_orm(column_type = "String(StringLen::N(200))")]
    pub description: Option<String>,
    
    #[sea_orm(column_type = "Text")]
    pub template_content: String,
    
    #[sea_orm(column_type = "String(StringLen::N(50))", default_value = "leaf")]
    pub template_type: String, // leaf, v2ray, clash, etc.
    
    #[sea_orm(column_type = "String(StringLen::N(20))", default_value = "1.0")]
    pub version: String,
    
    #[sea_orm(default_value = "true")]
    pub is_active: bool,
    
    #[sea_orm(default_value = "false")]
    pub is_system_default: bool,
    
    #[sea_orm(default_value = "0")]
    pub sort_order: i32,
    
    pub created_at: ChronoDateTimeWithTimeZone,
    pub updated_at: ChronoDateTimeWithTimeZone,
    pub created_by: Uuid,
    pub updated_by: Uuid,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::plans::Entity")]
    Plans,
}

impl Related<super::plans::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Plans.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Helper methods for ConfigTemplate model
impl Model {
    /// Check if template is active
    pub fn is_available(&self) -> bool {
        self.is_active
    }
    
    /// Get template type display name
    pub fn type_display(&self) -> String {
        match self.template_type.as_str() {
            "leaf" => "Leaf配置".to_string(),
            "v2ray" => "V2Ray配置".to_string(),
            "clash" => "Clash配置".to_string(),
            "openvpn" => "OpenVPN配置".to_string(),
            "wireguard" => "WireGuard配置".to_string(),
            _ => "自定义配置".to_string(),
        }
    }
    
    /// Get template status display
    pub fn status_display(&self) -> String {
        if self.is_system_default {
            "系统默认".to_string()
        } else if self.is_active {
            "启用".to_string()
        } else {
            "禁用".to_string()
        }
    }
    
    /// Validate template content (basic check)
    pub fn validate_template(&self) -> Result<(), String> {
        if self.template_content.is_empty() {
            return Err("模板内容不能为空".to_string());
        }
        
        if self.name.is_empty() {
            return Err("模板名称不能为空".to_string());
        }
        
        // Check for basic placeholder format
        if !self.template_content.contains("{{") {
            return Err("模板应包含至少一个占位符".to_string());
        }
        
        Ok(())
    }
    
    /// Get template preview (first 200 chars)
    pub fn get_preview(&self) -> String {
        if self.template_content.len() > 200 {
            format!("{}...", &self.template_content[..200])
        } else {
            self.template_content.clone()
        }
    }
    
    /// Count placeholders in template
    pub fn count_placeholders(&self) -> usize {
        let mut count = 0;
        let mut chars = self.template_content.chars().peekable();
        
        while let Some(ch) = chars.next() {
            if ch == '{' {
                if let Some(&next_ch) = chars.peek() {
                    if next_ch == '{' {
                        count += 1;
                        chars.next(); // consume the second '{'
                    }
                }
            }
        }
        
        count
    }
    
    /// Check if template is default for a specific type
    pub fn is_default_for_type(&self, template_type: &str) -> bool {
        self.is_system_default && self.template_type == template_type
    }
}