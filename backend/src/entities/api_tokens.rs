use sea_orm::entity::prelude::*;
use sea_orm::{Set, prelude::StringLen};
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "api_tokens")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    
    pub user_id: Uuid,
    
    #[sea_orm(column_type = "String(StringLen::N(255))", unique)]
    pub token_hash: String,
    
    #[sea_orm(column_type = "String(StringLen::N(100))")]
    pub name: String,
    
    pub permissions: Option<Json>,
    
    pub expires_at: Option<ChronoDateTimeWithTimeZone>,
    pub last_used_at: Option<ChronoDateTimeWithTimeZone>,
    
    pub last_used_ip: Option<String>,
    
    #[sea_orm(default_value = "true")]
    pub is_active: bool,
    
    pub created_at: ChronoDateTimeWithTimeZone,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumI<PERSON>, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::users::Entity",
        from = "Column::UserId",
        to = "super::users::Column::Id"
    )]
    User,
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Helper methods for ApiToken model
impl Model {
    /// Check if token is currently valid
    pub fn is_valid(&self) -> bool {
        if !self.is_active {
            return false;
        }
        
        if let Some(expires_at) = self.expires_at {
            chrono::Utc::now().timestamp() < expires_at.timestamp()
        } else {
            true
        }
    }
    
    /// Check if token is expired
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            chrono::Utc::now().timestamp() >= expires_at.timestamp()
        } else {
            false
        }
    }
    
    /// Get permissions list
    pub fn get_permissions(&self) -> Vec<String> {
        if let Some(permissions_json) = &self.permissions {
            if let Some(array) = permissions_json.as_array() {
                return array
                    .iter()
                    .filter_map(|v| v.as_str())
                    .map(|s| s.to_string())
                    .collect();
            }
        }
        Vec::new()
    }
    
    /// Check if token has specific permission
    pub fn has_permission(&self, permission: &str) -> bool {
        self.get_permissions().contains(&permission.to_string())
    }
    
    /// Get remaining time until expiration
    pub fn remaining_time(&self) -> Option<chrono::Duration> {
        if let Some(expires_at) = self.expires_at {
            let now = chrono::Utc::now().timestamp();
            let expires_timestamp = expires_at.timestamp();
            if expires_timestamp > now {
                Some(chrono::Duration::seconds(expires_timestamp - now))
            } else {
                Some(chrono::Duration::zero())
            }
        } else {
            None
        }
    }
    
    /// Get last used display
    pub fn last_used_display(&self) -> String {
        if let Some(last_used) = self.last_used_at {
            let now = chrono::Utc::now().timestamp();
            let duration_secs = now - last_used.timestamp();
            let duration = chrono::Duration::seconds(duration_secs);
            
            if duration.num_days() > 0 {
                format!("{}天前", duration.num_days())
            } else if duration.num_hours() > 0 {
                format!("{}小时前", duration.num_hours())
            } else if duration.num_minutes() > 0 {
                format!("{}分钟前", duration.num_minutes())
            } else {
                "刚刚".to_string()
            }
        } else {
            "从未使用".to_string()
        }
    }
    
    /// Get expiration display
    pub fn expiration_display(&self) -> String {
        if let Some(expires_at) = self.expires_at {
            if self.is_expired() {
                "已过期".to_string()
            } else {
                let now = chrono::Utc::now().timestamp();
                let duration_secs = expires_at.timestamp() - now;
                let duration = chrono::Duration::seconds(duration_secs);
                
                if duration.num_days() > 0 {
                    format!("{}天后过期", duration.num_days())
                } else if duration.num_hours() > 0 {
                    format!("{}小时后过期", duration.num_hours())
                } else if duration.num_minutes() > 0 {
                    format!("{}分钟后过期", duration.num_minutes())
                } else {
                    "即将过期".to_string()
                }
            }
        } else {
            "永不过期".to_string()
        }
    }
    
    /// Get token status display
    pub fn status_display(&self) -> String {
        if !self.is_active {
            "已禁用".to_string()
        } else if self.is_expired() {
            "已过期".to_string()
        } else {
            "正常".to_string()
        }
    }
    
    /// Create new token with permissions
    pub fn new_with_permissions(
        user_id: Uuid,
        name: String,
        permissions: Vec<String>,
        expires_at: Option<ChronoDateTimeWithTimeZone>,
    ) -> (ActiveModel, String) {
        let token = generate_token();
        let token_hash = hash_token(&token);
        
        let permissions_json = serde_json::to_value(permissions).unwrap_or_default();
        
        let active_model = ActiveModel {
            id: Set(Uuid::new_v4()),
            user_id: Set(user_id),
            token_hash: Set(token_hash),
            name: Set(name),
            permissions: Set(Some(permissions_json)),
            expires_at: Set(expires_at),
            last_used_at: Set(None),
            last_used_ip: Set(None),
            is_active: Set(true),
            created_at: Set(chrono::Utc::now().into()),
        };
        
        (active_model, token)
    }
    
    /// Update last used information
    pub fn update_last_used(&mut self, ip: Option<String>) {
        self.last_used_at = Some(chrono::Utc::now().into());
        self.last_used_ip = ip;
    }
}

// Helper functions
fn generate_token() -> String {
    use rand::Rng;
    const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    const TOKEN_LENGTH: usize = 64;
    
    let mut rng = rand::thread_rng();
    (0..TOKEN_LENGTH)
        .map(|_| {
            let idx = rng.gen_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

fn hash_token(token: &str) -> String {
    use sha2::{Sha256, Digest};
    let mut hasher = Sha256::new();
    hasher.update(token.as_bytes());
    format!("{:x}", hasher.finalize())
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenPermission {
    pub permission: String,
    pub description: String,
}

impl TokenPermission {
    pub fn get_available_permissions() -> Vec<TokenPermission> {
        vec![
            TokenPermission {
                permission: "read:profile".to_string(),
                description: "读取用户资料".to_string(),
            },
            TokenPermission {
                permission: "write:profile".to_string(),
                description: "修改用户资料".to_string(),
            },
            TokenPermission {
                permission: "read:config".to_string(),
                description: "读取配置".to_string(),
            },
            TokenPermission {
                permission: "read:orders".to_string(),
                description: "读取订单".to_string(),
            },
            TokenPermission {
                permission: "write:orders".to_string(),
                description: "创建订单".to_string(),
            },
            TokenPermission {
                permission: "read:announcements".to_string(),
                description: "读取公告".to_string(),
            },
            TokenPermission {
                permission: "admin:users".to_string(),
                description: "管理用户".to_string(),
            },
            TokenPermission {
                permission: "admin:plans".to_string(),
                description: "管理套餐".to_string(),
            },
            TokenPermission {
                permission: "admin:orders".to_string(),
                description: "管理订单".to_string(),
            },
            TokenPermission {
                permission: "admin:announcements".to_string(),
                description: "管理公告".to_string(),
            },
            TokenPermission {
                permission: "admin:settings".to_string(),
                description: "管理系统设置".to_string(),
            },
        ]
    }
}