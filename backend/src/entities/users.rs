use sea_orm::entity::prelude::*;
use sea_orm::prelude::StringLen;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "users")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    
    #[sea_orm(column_type = "String(StringLen::N(50))", unique)]
    pub username: String,
    
    #[sea_orm(column_type = "String(StringLen::N(255))", unique, nullable)]
    pub email: Option<String>,
    
    #[sea_orm(column_type = "String(StringLen::N(255))")]
    pub password_hash: String,
    
    #[sea_orm(default_value = "true")]
    pub is_active: bool,
    
    #[sea_orm(default_value = "false")]
    pub is_admin: bool,
    
    pub created_at: ChronoDateTimeWithTimeZone,
    pub updated_at: ChronoDateTimeWithTimeZone,
    pub last_login_at: Option<ChronoDateTimeWithTimeZone>,
    
    #[sea_orm(default_value = "3")]
    pub max_concurrent_devices: i32,
    
    #[sea_orm(default_value = "false")]
    pub is_banned: bool,
    
    pub ban_reason: Option<String>,
    pub banned_until: Option<ChronoDateTimeWithTimeZone>,
    
    #[sea_orm(default_value = "0")]
    pub total_traffic_used: i64,
    
    pub last_traffic_reset: ChronoDateTimeWithTimeZone,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::subscriptions::Entity")]
    Subscriptions,
    
    #[sea_orm(has_many = "super::orders::Entity")]
    Orders,
    
    #[sea_orm(has_many = "super::connection_logs::Entity")]
    ConnectionLogs,
    
    #[sea_orm(has_many = "super::api_tokens::Entity")]
    ApiTokens,
    
    #[sea_orm(has_many = "super::announcements::Entity")]
    CreatedAnnouncements,
}

impl Related<super::subscriptions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Subscriptions.def()
    }
}

impl Related<super::orders::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Orders.def()
    }
}

impl Related<super::connection_logs::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ConnectionLogs.def()
    }
}

impl Related<super::api_tokens::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ApiTokens.def()
    }
}

impl Related<super::announcements::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CreatedAnnouncements.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Helper methods for User model
impl Model {
    /// Check if user is currently banned
    pub fn is_currently_banned(&self) -> bool {
        if !self.is_banned {
            return false;
        }
        
        if let Some(banned_until) = self.banned_until {
            chrono::Utc::now().timestamp() < banned_until.timestamp()
        } else {
            true // Permanently banned
        }
    }
    
    /// Check if user can login (active and not banned)
    pub fn can_login(&self) -> bool {
        self.is_active && !self.is_currently_banned()
    }
    
    /// Get user's display name (username or email)
    pub fn display_name(&self) -> String {
        self.email.clone().unwrap_or_else(|| self.username.clone())
    }
}