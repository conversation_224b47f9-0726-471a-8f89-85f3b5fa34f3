use sea_orm::entity::prelude::*;
use sea_orm::prelude::StringLen;
use serde::{Deserialize, Serialize};
use rust_decimal::Decimal;
use rust_decimal::prelude::ToPrimitive;

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "subscriptions")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    
    pub user_id: Uuid,
    pub plan_id: Uuid,
    
    pub start_date: ChronoDateTimeWithTimeZone,
    pub end_date: ChronoDateTimeWithTimeZone,
    
    #[sea_orm(column_type = "Decimal(Some((10, 3)))")]
    pub total_traffic_gb: Decimal,
    
    #[sea_orm(column_type = "Decimal(Some((10, 3)))", default_value = "0")]
    pub used_traffic_gb: Decimal,
    
    pub max_concurrent_devices: i32,
    
    #[sea_orm(default_value = "0")]
    pub current_devices_count: i32,
    
    pub status: SubscriptionStatus,
    
    #[sea_orm(default_value = "false")]
    pub auto_renew: bool,
    
    pub auto_renew_plan_id: Option<Uuid>,
    
    #[sea_orm(column_type = "String(StringLen::N(100))", nullable)]
    pub proxy_username: Option<String>,
    
    #[sea_orm(column_type = "String(StringLen::N(100))", nullable)]
    pub proxy_password: Option<String>,
    
    pub proxy_port: Option<i32>,
    
    #[sea_orm(column_type = "String(StringLen::N(64))", nullable)]
    pub proxy_config_hash: Option<String>,
    
    pub created_at: ChronoDateTimeWithTimeZone,
    pub updated_at: ChronoDateTimeWithTimeZone,
    pub last_traffic_sync: ChronoDateTimeWithTimeZone,
    
    #[sea_orm(default_value = "0")]
    pub total_connections_count: i64,
    
    #[sea_orm(default_value = "0")]
    pub total_session_time: i64,
}

pub use crate::entities::enums::SubscriptionStatus;

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::users::Entity",
        from = "Column::UserId",
        to = "super::users::Column::Id"
    )]
    User,
    
    #[sea_orm(
        belongs_to = "super::plans::Entity",
        from = "Column::PlanId",
        to = "super::plans::Column::Id"
    )]
    Plan,
    
    #[sea_orm(
        belongs_to = "super::plans::Entity",
        from = "Column::AutoRenewPlanId",
        to = "super::plans::Column::Id"
    )]
    AutoRenewPlan,
    
    #[sea_orm(has_many = "super::connection_logs::Entity")]
    ConnectionLogs,
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}

impl Related<super::plans::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Plan.def()
    }
}

impl Related<super::connection_logs::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ConnectionLogs.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Helper methods for Subscription model
impl Model {
    /// Check if subscription is currently active
    pub fn is_active(&self) -> bool {
        self.status == SubscriptionStatus::Active &&
        chrono::Utc::now().timestamp() < self.end_date.timestamp()
    }
    
    /// Check if subscription is expired
    pub fn is_expired(&self) -> bool {
        self.status == SubscriptionStatus::Expired ||
        chrono::Utc::now().timestamp() >= self.end_date.timestamp()
    }
    
    /// Get remaining days until expiration
    pub fn remaining_days(&self) -> i64 {
        let now = chrono::Utc::now().timestamp();
        let end_timestamp = self.end_date.timestamp();
        let duration = end_timestamp - now;
        (duration / 86400).max(0)
    }
    
    /// Get remaining traffic in GB
    pub fn remaining_traffic_gb(&self) -> Decimal {
        (self.total_traffic_gb - self.used_traffic_gb).max(rust_decimal::Decimal::ZERO)
    }
    
    /// Get traffic usage percentage
    pub fn traffic_usage_percentage(&self) -> f64 {
        if self.total_traffic_gb == rust_decimal::Decimal::ZERO {
            0.0
        } else {
            let used = self.used_traffic_gb.to_f64().unwrap_or(0.0);
            let total = self.total_traffic_gb.to_f64().unwrap_or(1.0);
            (used / total * 100.0).min(100.0)
        }
    }
    
    /// Check if traffic limit is exceeded
    pub fn is_traffic_exceeded(&self) -> bool {
        self.used_traffic_gb >= self.total_traffic_gb
    }
    
    /// Check if device limit is exceeded
    pub fn is_device_limit_exceeded(&self) -> bool {
        self.current_devices_count >= self.max_concurrent_devices
    }
    
    /// Check if user can connect (active subscription, not expired, has traffic, device limit not exceeded)
    pub fn can_connect(&self) -> bool {
        self.is_active() && 
        !self.is_traffic_exceeded() && 
        !self.is_device_limit_exceeded()
    }
    
    /// Get subscription status display
    pub fn status_display(&self) -> String {
        match self.status {
            SubscriptionStatus::Active => {
                if self.is_expired() {
                    "已过期".to_string()
                } else if self.is_traffic_exceeded() {
                    "流量已用完".to_string()
                } else {
                    "正常".to_string()
                }
            }
            SubscriptionStatus::Expired => "已过期".to_string(),
            SubscriptionStatus::Suspended => "已暂停".to_string(),
            SubscriptionStatus::Cancelled => "已取消".to_string(),
        }
    }
    
    /// Get proxy configuration if available
    pub fn get_proxy_config(&self) -> Option<ProxyConfig> {
        if let (Some(username), Some(password), Some(port)) = (
            &self.proxy_username,
            &self.proxy_password,
            self.proxy_port
        ) {
            Some(ProxyConfig {
                username: username.clone(),
                password: password.clone(),
                port: port,
            })
        } else {
            None
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyConfig {
    pub username: String,
    pub password: String,
    pub port: i32,
}