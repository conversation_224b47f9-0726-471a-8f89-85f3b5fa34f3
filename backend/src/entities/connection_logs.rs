use sea_orm::entity::prelude::*;
use sea_orm::prelude::StringLen;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "connection_logs")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    
    pub user_id: Uuid,
    pub subscription_id: Uuid,
    
    pub client_ip: Option<String>,
    pub client_port: Option<i32>,
    pub user_agent: Option<String>,
    pub device_info: Option<Json>,
    
    #[sea_orm(column_type = "String(StringLen::N(64))", nullable)]
    pub session_id: Option<String>,
    
    pub connected_at: ChronoDateTimeWithTimeZone,
    pub disconnected_at: Option<ChronoDateTimeWithTimeZone>,
    pub duration_seconds: Option<i32>,
    
    #[sea_orm(default_value = "0")]
    pub bytes_uploaded: i64,
    
    #[sea_orm(default_value = "0")]
    pub bytes_downloaded: i64,
    
    #[sea_orm(column_type = "String(StringLen::N(50))", nullable)]
    pub disconnect_reason: Option<String>,
    
    #[sea_orm(column_type = "String(StringLen::N(2))", nullable)]
    pub country_code: Option<String>,
    
    #[sea_orm(column_type = "String(StringLen::N(50))", nullable)]
    pub city: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::users::Entity",
        from = "Column::UserId",
        to = "super::users::Column::Id"
    )]
    User,
    
    #[sea_orm(
        belongs_to = "super::subscriptions::Entity",
        from = "Column::SubscriptionId",
        to = "super::subscriptions::Column::Id"
    )]
    Subscription,
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}

impl Related<super::subscriptions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Subscription.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Helper methods for ConnectionLog model
impl Model {
    /// Check if connection is still active
    pub fn is_active(&self) -> bool {
        self.disconnected_at.is_none()
    }
    
    /// Get connection duration
    pub fn get_duration(&self) -> Option<chrono::Duration> {
        if let Some(disconnected_at) = self.disconnected_at {
            let duration_secs = disconnected_at.timestamp() - self.connected_at.timestamp();
            Some(chrono::Duration::seconds(duration_secs))
        } else {
            // If still connected, calculate duration from now
            let duration_secs = chrono::Utc::now().timestamp() - self.connected_at.timestamp();
            Some(chrono::Duration::seconds(duration_secs))
        }
    }
    
    /// Get total bytes transferred
    pub fn total_bytes(&self) -> i64 {
        self.bytes_uploaded + self.bytes_downloaded
    }
    
    /// Get formatted data usage
    pub fn formatted_data_usage(&self) -> String {
        let total = self.total_bytes();
        format_bytes(total)
    }
    
    /// Get formatted upload/download usage
    pub fn formatted_upload_download(&self) -> String {
        format!("↑{} ↓{}", format_bytes(self.bytes_uploaded), format_bytes(self.bytes_downloaded))
    }
    
    /// Get connection duration display
    pub fn duration_display(&self) -> String {
        if let Some(duration) = self.get_duration() {
            format_duration(duration)
        } else {
            "未知".to_string()
        }
    }
    
    /// Get disconnect reason display
    pub fn disconnect_reason_display(&self) -> String {
        match self.disconnect_reason.as_deref() {
            Some("normal") => "正常断开".to_string(),
            Some("timeout") => "超时断开".to_string(),
            Some("error") => "错误断开".to_string(),
            Some("banned") => "用户被封禁".to_string(),
            Some("limit_exceeded") => "流量超限".to_string(),
            Some(reason) => reason.to_string(),
            None => if self.is_active() { "连接中".to_string() } else { "未知".to_string() }
        }
    }
    
    /// Get location display
    pub fn location_display(&self) -> String {
        match (&self.country_code, &self.city) {
            (Some(country), Some(city)) => format!("{}, {}", city, country),
            (Some(country), None) => country.clone(),
            (None, Some(city)) => city.clone(),
            (None, None) => "未知".to_string(),
        }
    }
    
    /// Get device information
    pub fn get_device_info(&self) -> Option<DeviceInfo> {
        if let Some(device_json) = &self.device_info {
            serde_json::from_value(device_json.clone()).ok()
        } else {
            None
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub device_type: Option<String>,
    pub os: Option<String>,
    pub os_version: Option<String>,
    pub app_version: Option<String>,
    pub device_model: Option<String>,
}

// Helper functions
fn format_bytes(bytes: i64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    const THRESHOLD: f64 = 1024.0;
    
    let mut size = bytes as f64;
    let mut unit_index = 0;
    
    while size >= THRESHOLD && unit_index < UNITS.len() - 1 {
        size /= THRESHOLD;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{:.0}{}", size, UNITS[unit_index])
    } else {
        format!("{:.1}{}", size, UNITS[unit_index])
    }
}

fn format_duration(duration: chrono::Duration) -> String {
    let total_seconds = duration.num_seconds();
    let hours = total_seconds / 3600;
    let minutes = (total_seconds % 3600) / 60;
    let seconds = total_seconds % 60;
    
    if hours > 0 {
        format!("{}:{:02}:{:02}", hours, minutes, seconds)
    } else if minutes > 0 {
        format!("{}:{:02}", minutes, seconds)
    } else {
        format!("{}s", seconds)
    }
}