// Test JWT token utilities for subtask 3.2
use backend::auth::jwt::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JwtError};
use uuid::Uuid;

fn main() {
    println!("=== JWT Token Utilities Test (Subtask 3.2) ===");
    
    let jwt_manager = JwtManager::new("test_secret_key_for_development");
    let user_id = Uuid::new_v4();
    let username = "testuser";
    let role = "user";
    
    // Test access token generation and validation
    println!("\n1. Testing Access Token Generation & Validation");
    let access_token = jwt_manager.generate_access_token(user_id, username, role).unwrap();
    println!("✓ Access token generated: {}...{}", &access_token[..20], &access_token[access_token.len()-20..]);
    
    let claims = jwt_manager.validate_access_token(&access_token).unwrap();
    println!("✓ Access token validated successfully");
    println!("  User ID: {}", claims.user_id);
    println!("  Username: {}", claims.username);
    println!("  Role: {}", claims.role);
    println!("  Expires at: {}", claims.exp);
    
    // Test refresh token generation and validation
    println!("\n2. Testing Refresh Token Generation & Validation");
    let refresh_token = jwt_manager.generate_refresh_token(user_id).unwrap();
    println!("✓ Refresh token generated: {}...{}", &refresh_token[..20], &refresh_token[refresh_token.len()-20..]);
    
    let refresh_claims = jwt_manager.validate_refresh_token(&refresh_token).unwrap();
    println!("✓ Refresh token validated successfully");
    println!("  User ID: {}", refresh_claims.sub);
    println!("  Token type: {}", refresh_claims.token_type);
    println!("  Expires at: {}", refresh_claims.exp);
    
    // Test token pair generation
    println!("\n3. Testing Token Pair Generation");
    let (access_token2, refresh_token2) = jwt_manager.generate_token_pair(user_id, username, role).unwrap();
    println!("✓ Token pair generated successfully");
    println!("  Access token: {}...", &access_token2[..30]);
    println!("  Refresh token: {}...", &refresh_token2[..30]);
    
    // Test refresh access token functionality
    println!("\n4. Testing Access Token Refresh");
    let new_access_token = jwt_manager.refresh_access_token(&refresh_token2, username, role).unwrap();
    println!("✓ Access token refreshed successfully");
    println!("  New access token: {}...", &new_access_token[..30]);
    
    let new_claims = jwt_manager.validate_access_token(&new_access_token).unwrap();
    println!("✓ New access token is valid");
    println!("  User ID: {}", new_claims.user_id);
    
    // Test error handling
    println!("\n5. Testing Error Handling");
    
    // Invalid token
    match jwt_manager.validate_access_token("invalid_token") {
        Err(JwtError::DecodingError(_)) => println!("✓ Invalid token properly rejected"),
        other => println!("✗ Unexpected result for invalid token: {:?}", other),
    }
    
    // Wrong secret
    let jwt_manager2 = JwtManager::new("different_secret");
    match jwt_manager2.validate_access_token(&access_token) {
        Err(JwtError::DecodingError(_)) => println!("✓ Token with wrong secret properly rejected"),
        other => println!("✗ Unexpected result for wrong secret: {:?}", other),
    }
    
    // Using access token as refresh token
    match jwt_manager.validate_refresh_token(&access_token) {
        Err(_) => println!("✓ Access token properly rejected as refresh token"),
        Ok(_) => println!("✗ Access token incorrectly accepted as refresh token"),
    }
    
    // Test token expiration (using very short duration)
    println!("\n6. Testing Token Expiration");
    let short_jwt_manager = JwtManager::with_durations("test_secret", 1, 1);
    let short_token = short_jwt_manager.generate_access_token(user_id, username, role).unwrap();
    
    // Wait for token to expire
    std::thread::sleep(std::time::Duration::from_secs(2));
    
    match short_jwt_manager.validate_access_token(&short_token) {
        Err(JwtError::TokenExpired) => println!("✓ Expired token properly rejected"),
        other => println!("✗ Unexpected result for expired token: {:?}", other),
    }
    
    // Test custom durations
    println!("\n7. Testing Custom Token Durations");
    let custom_jwt_manager = JwtManager::with_durations("test_secret", 1800, 86400);
    println!("✓ Custom durations set:");
    println!("  Access token duration: {} seconds (30 minutes)", custom_jwt_manager.access_token_duration());
    println!("  Refresh token duration: {} seconds (24 hours)", custom_jwt_manager.refresh_token_duration());
    
    println!("\n=== Subtask 3.2 Complete ===");
    println!("✓ JWT token generation implemented");
    println!("✓ JWT token validation implemented");
    println!("✓ JWT token refresh mechanism implemented");
    println!("✓ Proper error handling for invalid/expired tokens");
    println!("✓ Configurable token durations");
    println!("✓ Access and refresh token separation");
    println!("✓ All tests passed!");
}