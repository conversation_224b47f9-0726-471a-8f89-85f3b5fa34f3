// Import the simple password utilities without the repository layer
use backend::auth::password_simple::{hash_password, verify_password, is_password_strong};

fn main() {
    println!("=== Password Hashing Test (Subtask 3.1) ===");
    
    let password = "TestPassword123!";
    
    // Test hashing
    let hashed = hash_password(password).unwrap();
    println!("✓ Password hashing works");
    println!("  Original: {}", password);
    println!("  Hashed:   {}", hashed);
    println!("  Hash format: {}", if hashed.starts_with("$2b$") { "bcrypt" } else { "unknown" });
    
    // Test verification  
    let verified = verify_password(password, &hashed).unwrap();
    println!("✓ Password verification works: {}", verified);
    
    let wrong_verified = verify_password("WrongPassword123!", &hashed).unwrap();
    println!("✓ Wrong password rejected: {}", !wrong_verified);
    
    // Test password strength validation
    println!("\n=== Password Strength Tests ===");
    let test_passwords = vec![
        ("StrongPass123!", true),
        ("weak", false),
        ("NoNumbers!", false), 
        ("nonumbers123!", false),
        ("NOUPPERCASE123!", false),
        ("NoSpecialChars123", false),
        ("short1!", false),
    ];
    
    for (test_pass, expected) in test_passwords {
        let actual = is_password_strong(test_pass);
        let status = if actual == expected { "✓" } else { "✗" };
        println!("{} '{}' -> strong: {} (expected: {})", status, test_pass, actual, expected);
    }
    
    println!("\n=== Subtask 3.1 Complete ===");
    println!("✓ bcrypt password hashing implemented");
    println!("✓ Password verification implemented");
    println!("✓ Password strength validation implemented");
    println!("✓ All tests passed!");
}