use sea_orm::{Database, DatabaseConnection, DbErr, ConnectionTrait};
use std::env;
use std::time::Duration;
use tracing::{info, warn};

pub mod migration;
// 移除冲突的迁移模块
// pub mod migration_simple;
// pub mod migration_basic;

/// Database configuration structure
#[derive(Clone)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connect_timeout: Duration,
    pub idle_timeout: Duration,
}

impl DatabaseConfig {
    /// Create database configuration from environment variables
    pub fn from_env() -> Result<Self, DbErr> {
        let url = env::var("DATABASE_URL")
            .map_err(|_| DbErr::Custom("DATABASE_URL environment variable not set".to_string()))?;
        
        let max_connections = env::var("DB_MAX_CONNECTIONS")
            .unwrap_or_else(|_| "20".to_string())
            .parse()
            .map_err(|_| DbErr::Custom("Invalid DB_MAX_CONNECTIONS value".to_string()))?;
        
        let min_connections = env::var("DB_MIN_CONNECTIONS")
            .unwrap_or_else(|_| "5".to_string())
            .parse()
            .map_err(|_| DbErr::Custom("Invalid DB_MIN_CONNECTIONS value".to_string()))?;
        
        let connect_timeout_secs = env::var("DB_CONNECT_TIMEOUT")
            .unwrap_or_else(|_| "10".to_string())
            .parse()
            .map_err(|_| DbErr::Custom("Invalid DB_CONNECT_TIMEOUT value".to_string()))?;
        
        let idle_timeout_secs = env::var("DB_IDLE_TIMEOUT")
            .unwrap_or_else(|_| "300".to_string())
            .parse()
            .map_err(|_| DbErr::Custom("Invalid DB_IDLE_TIMEOUT value".to_string()))?;
        
        Ok(Self {
            url,
            max_connections,
            min_connections,
            connect_timeout: Duration::from_secs(connect_timeout_secs),
            idle_timeout: Duration::from_secs(idle_timeout_secs),
        })
    }
}

/// Database connection manager
pub struct DatabaseManager {
    config: DatabaseConfig,
}

impl DatabaseManager {
    /// Create a new database manager
    pub fn new(config: DatabaseConfig) -> Self {
        Self { config }
    }
    
    /// Establish database connection with configured settings
    pub async fn connect(&self) -> Result<DatabaseConnection, DbErr> {
        info!("Connecting to database...");
        
        let mut opt = sea_orm::ConnectOptions::new(&self.config.url);
        
        // Configure connection pool
        opt.max_connections(self.config.max_connections)
            .min_connections(self.config.min_connections)
            .connect_timeout(self.config.connect_timeout)
            .idle_timeout(self.config.idle_timeout)
            .sqlx_logging(true);
        
        let db = Database::connect(opt).await?;
        
        info!("Database connection established successfully");
        Ok(db)
    }
    
    /// Test database connection
    pub async fn test_connection(&self) -> Result<(), DbErr> {
        info!("Testing database connection...");
        
        let db = self.connect().await?;
        
        // Execute a simple query to test connection
        let result = db.execute_unprepared("SELECT 1").await;
        match result {
            Ok(_) => {
                info!("Database connection test successful");
                Ok(())
            }
            Err(e) => {
                warn!("Database connection test failed: {}", e);
                Err(e)
            }
        }
    }
    
    /// Reset migration state and run fresh migrations
    pub async fn reset_and_run_migrations(&self) -> Result<(), DbErr> {
        info!("Resetting migration state and running fresh migrations...");
        
        let db = self.connect().await?;
        
        // Drop the migration table to reset migration state
        let _ = db.execute_unprepared("DROP TABLE IF EXISTS seaql_migrations").await;
        info!("Migration state reset");
        
        // Run the unified migration
        migration::run_migrations(&db).await?;
        
        info!("Fresh migrations completed successfully");
        Ok(())
    }
    
    /// Run pending migrations (unified approach)
    pub async fn run_migrations(&self) -> Result<(), DbErr> {
        info!("Running database migrations...");
        
        let db = self.connect().await?;
        
        // Only run the unified migration
        migration::run_migrations(&db).await?;
        
        info!("Database migrations completed successfully");
        Ok(())
    }
}

/// Initialize database with configuration and migrations
pub async fn initialize_database() -> Result<DatabaseConnection, DbErr> {
    let config = DatabaseConfig::from_env()?;
    let manager = DatabaseManager::new(config);
    
    // Test connection first
    manager.test_connection().await?;
    
    // Run migrations
    manager.run_migrations().await?;
    
    // Return connection for use
    manager.connect().await
}

/// Reset database migrations and run fresh (for troubleshooting)
pub async fn reset_database_migrations() -> Result<DatabaseConnection, DbErr> {
    let config = DatabaseConfig::from_env()?;
    let manager = DatabaseManager::new(config);
    
    // Test connection first
    manager.test_connection().await?;
    
    // Reset and run fresh migrations
    manager.reset_and_run_migrations().await?;
    
    // Return connection for use
    manager.connect().await
}