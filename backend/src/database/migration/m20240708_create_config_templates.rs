use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(ConfigTemplates::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(ConfigTemplates::Id)
                            .uuid()
                            .not_null()
                            .primary_key()
                            .default(Expr::cust("gen_random_uuid()"))
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::Name)
                            .string_len(100)
                            .not_null()
                            .unique_key()
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::Description)
                            .string_len(200)
                            .null()
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::TemplateContent)
                            .text()
                            .not_null()
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::TemplateType)
                            .string_len(50)
                            .not_null()
                            .default("leaf")
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::Version)
                            .string_len(20)
                            .not_null()
                            .default("1.0")
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::IsActive)
                            .boolean()
                            .not_null()
                            .default(true)
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::IsSystemDefault)
                            .boolean()
                            .not_null()
                            .default(false)
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::SortOrder)
                            .integer()
                            .not_null()
                            .default(0)
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::CreatedAt)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::cust("CURRENT_TIMESTAMP"))
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::UpdatedAt)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::cust("CURRENT_TIMESTAMP"))
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::CreatedBy)
                            .uuid()
                            .not_null()
                    )
                    .col(
                        ColumnDef::new(ConfigTemplates::UpdatedBy)
                            .uuid()
                            .not_null()
                    )
                    .to_owned(),
            )
            .await?;

        // Create index for template_type
        manager
            .create_index(
                Index::create()
                    .name("idx_config_templates_type")
                    .table(ConfigTemplates::Table)
                    .col(ConfigTemplates::TemplateType)
                    .to_owned(),
            )
            .await?;

        // Create index for active templates
        manager
            .create_index(
                Index::create()
                    .name("idx_config_templates_active")
                    .table(ConfigTemplates::Table)
                    .col(ConfigTemplates::IsActive)
                    .to_owned(),
            )
            .await?;

        // Create composite index for system default by type
        manager
            .create_index(
                Index::create()
                    .name("idx_config_templates_system_default")
                    .table(ConfigTemplates::Table)
                    .col(ConfigTemplates::IsSystemDefault)
                    .col(ConfigTemplates::TemplateType)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(ConfigTemplates::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum ConfigTemplates {
    Table,
    Id,
    Name,
    Description,
    TemplateContent,
    TemplateType,
    Version,
    IsActive,
    IsSystemDefault,
    SortOrder,
    CreatedAt,
    UpdatedAt,
    CreatedBy,
    UpdatedBy,
}