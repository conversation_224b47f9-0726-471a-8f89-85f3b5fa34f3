use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .alter_table(
                Table::alter()
                    .table(Plans::Table)
                    .add_column(
                        ColumnDef::new(Plans::ConfigTemplateId)
                            .uuid()
                            .null()
                    )
                    .to_owned(),
            )
            .await?;

        // Add foreign key constraint
        manager
            .create_foreign_key(
                ForeignKey::create()
                    .name("fk_plans_config_template")
                    .from(Plans::Table, Plans::ConfigTemplateId)
                    .to(ConfigTemplates::Table, ConfigTemplates::Id)
                    .on_delete(ForeignKeyAction::SetNull)
                    .on_update(ForeignKeyAction::Cascade)
                    .to_owned(),
            )
            .await?;

        // Create index for config_template_id
        manager
            .create_index(
                Index::create()
                    .name("idx_plans_config_template_id")
                    .table(Plans::Table)
                    .col(Plans::ConfigTemplateId)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_foreign_key(
                ForeignKey::drop()
                    .name("fk_plans_config_template")
                    .table(Plans::Table)
                    .to_owned(),
            )
            .await?;

        manager
            .alter_table(
                Table::alter()
                    .table(Plans::Table)
                    .drop_column(Plans::ConfigTemplateId)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }
}

#[derive(DeriveIden)]
enum Plans {
    Table,
    ConfigTemplateId,
}

#[derive(DeriveIden)]
enum ConfigTemplates {
    Table,
    Id,
}