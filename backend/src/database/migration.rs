use sea_orm::sea_query::extension::postgres::TypeCreateStatement;
use sea_orm::{DatabaseConnection, DbErr};
use sea_orm_migration::prelude::extension::postgres::Type;
use sea_orm_migration::prelude::*;
use tracing::info;

pub mod m20240708_add_config_template_to_plans;
pub mod m20240708_create_config_templates;

/// Migration runner module
pub struct Migrator;

#[async_trait::async_trait]
impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![
            Box::new(m20240101_000001_create_all_tables::Migration),
            Box::new(m20240708_create_config_templates::Migration),
            Box::new(m20240708_add_config_template_to_plans::Migration),
        ]
    }
}

/// Run all pending migrations
pub async fn run_migrations(db: &DatabaseConnection) -> Result<(), DbErr> {
    info!("Starting database migrations...");

    Migrator::up(db, None).await?;

    info!("All migrations completed successfully");
    Ok(())
}

async fn allow_dup_for_create_type(
    manager: &SchemaManager<'_>,
    type_statement: TypeCreateStatement,
) -> Result<(), DbErr> {
    match manager.create_type(type_statement).await {
        Ok(_) => Ok(()),
        Err(e) => {
            Err(e)
        }
    }
}

/// Create all tables in one migration
mod m20240101_000001_create_all_tables {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            // Create all enums first
            self.create_enums(manager).await?;

            // Create all tables
            self.create_users_table(manager).await?;
            self.create_plans_table(manager).await?;
            self.create_subscriptions_table(manager).await?;
            self.create_orders_table(manager).await?;
            self.create_announcements_table(manager).await?;
            self.create_connection_logs_table(manager).await?;
            self.create_system_settings_table(manager).await?;
            self.create_api_tokens_table(manager).await?;

            // Create indexes
            self.create_indexes(manager).await?;

            Ok(())
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            // Drop all tables
            manager
                .drop_table(Table::drop().table(ApiTokens::Table).to_owned())
                .await?;
            manager
                .drop_table(Table::drop().table(SystemSettings::Table).to_owned())
                .await?;
            manager
                .drop_table(Table::drop().table(ConnectionLogs::Table).to_owned())
                .await?;
            manager
                .drop_table(Table::drop().table(Announcements::Table).to_owned())
                .await?;
            manager
                .drop_table(Table::drop().table(Orders::Table).to_owned())
                .await?;
            manager
                .drop_table(Table::drop().table(Subscriptions::Table).to_owned())
                .await?;
            manager
                .drop_table(Table::drop().table(Plans::Table).to_owned())
                .await?;
            manager
                .drop_table(Table::drop().table(Users::Table).to_owned())
                .await?;

            // Drop enums
            manager
                .drop_type(Type::drop().name(SettingValueType::Type).to_owned())
                .await?;
            manager
                .drop_type(Type::drop().name(AnnouncementType::Type).to_owned())
                .await?;
            manager
                .drop_type(Type::drop().name(AnnouncementAudience::Type).to_owned())
                .await?;
            manager
                .drop_type(Type::drop().name(PaymentMethodType::Type).to_owned())
                .await?;
            manager
                .drop_type(Type::drop().name(OrderStatus::Type).to_owned())
                .await?;
            manager
                .drop_type(Type::drop().name(SubscriptionStatus::Type).to_owned())
                .await?;

            Ok(())
        }
    }

    impl Migration {
        async fn create_enums(&self, manager: &SchemaManager<'_>) -> Result<(), DbErr> {
            // Subscription status enum
            allow_dup_for_create_type(
                manager,
                Type::create()
                    .as_enum(SubscriptionStatus::Type)
                    .values([
                        SubscriptionStatus::Active,
                        SubscriptionStatus::Expired,
                        SubscriptionStatus::Suspended,
                        SubscriptionStatus::Cancelled,
                    ])
                    .to_owned(),
            )
            .await?;

            // Order status enum
            allow_dup_for_create_type(
                manager,
                Type::create()
                        .as_enum(OrderStatus::Type)
                        .values([
                            OrderStatus::PendingPayment,
                            OrderStatus::Paid,
                            OrderStatus::Processing,
                            OrderStatus::Completed,
                            OrderStatus::Cancelled,
                            OrderStatus::Refunded,
                        ])
                        .to_owned(),
                )
                .await?;

            // Payment method enum
            allow_dup_for_create_type(
                manager,
                Type::create()
                        .as_enum(PaymentMethodType::Type)
                        .values([
                            PaymentMethodType::Manual,
                            PaymentMethodType::Alipay,
                            PaymentMethodType::Wechat,
                            PaymentMethodType::Paypal,
                            PaymentMethodType::Crypto,
                        ])
                        .to_owned(),
                )
                .await?;

            // Announcement audience enum
            allow_dup_for_create_type(
                manager,
                Type::create()
                        .as_enum(AnnouncementAudience::Type)
                        .values([
                            AnnouncementAudience::All,
                            AnnouncementAudience::ActiveUsers,
                            AnnouncementAudience::ExpiredUsers,
                            AnnouncementAudience::SpecificPlans,
                        ])
                        .to_owned(),
                )
                .await?;

            // Announcement type enum
            allow_dup_for_create_type(
                manager,
                Type::create()
                        .as_enum(AnnouncementType::Type)
                        .values([
                            AnnouncementType::Info,
                            AnnouncementType::Warning,
                            AnnouncementType::Maintenance,
                            AnnouncementType::Promotion,
                            AnnouncementType::Urgent,
                        ])
                        .to_owned(),
                )
                .await?;

            // Setting value type enum
            allow_dup_for_create_type(
                manager,
                Type::create()
                        .as_enum(SettingValueType::Type)
                        .values([
                            SettingValueType::String,
                            SettingValueType::Integer,
                            SettingValueType::Decimal,
                            SettingValueType::Boolean,
                            SettingValueType::Json,
                        ])
                        .to_owned(),
                )
                .await?;

            Ok(())
        }

        async fn create_users_table(&self, manager: &SchemaManager<'_>) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(Users::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(Users::Id)
                                .uuid()
                                .not_null()
                                .primary_key()
                                .extra("DEFAULT gen_random_uuid()".to_string()),
                        )
                        .col(
                            ColumnDef::new(Users::Username)
                                .string_len(50)
                                .not_null()
                                .unique_key(),
                        )
                        .col(ColumnDef::new(Users::Email).string_len(255).unique_key())
                        .col(
                            ColumnDef::new(Users::PasswordHash)
                                .string_len(255)
                                .not_null(),
                        )
                        .col(ColumnDef::new(Users::IsActive).boolean().default(true))
                        .col(ColumnDef::new(Users::IsAdmin).boolean().default(false))
                        .col(
                            ColumnDef::new(Users::CreatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(Users::UpdatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(ColumnDef::new(Users::LastLoginAt).timestamp_with_time_zone())
                        .col(
                            ColumnDef::new(Users::MaxConcurrentDevices)
                                .integer()
                                .default(3),
                        )
                        .col(ColumnDef::new(Users::IsBanned).boolean().default(false))
                        .col(ColumnDef::new(Users::BanReason).text())
                        .col(ColumnDef::new(Users::BannedUntil).timestamp_with_time_zone())
                        .col(
                            ColumnDef::new(Users::TotalTrafficUsed)
                                .big_integer()
                                .default(0),
                        )
                        .col(
                            ColumnDef::new(Users::LastTrafficReset)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn create_plans_table(&self, manager: &SchemaManager<'_>) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(Plans::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(Plans::Id)
                                .uuid()
                                .not_null()
                                .primary_key()
                                .extra("DEFAULT gen_random_uuid()".to_string()),
                        )
                        .col(ColumnDef::new(Plans::Name).string_len(100).not_null())
                        .col(ColumnDef::new(Plans::Description).text())
                        .col(ColumnDef::new(Plans::Price).decimal_len(10, 2).not_null())
                        .col(ColumnDef::new(Plans::Currency).string_len(3).default("USD"))
                        .col(ColumnDef::new(Plans::DurationDays).integer().not_null())
                        .col(ColumnDef::new(Plans::TrafficLimitGb).decimal_len(10, 3))
                        .col(
                            ColumnDef::new(Plans::MaxConcurrentDevices)
                                .integer()
                                .default(1),
                        )
                        .col(
                            ColumnDef::new(Plans::MaxConcurrentConnections)
                                .integer()
                                .default(10),
                        )
                        .col(ColumnDef::new(Plans::SpeedLimitUpload).integer())
                        .col(ColumnDef::new(Plans::SpeedLimitDownload).integer())
                        .col(ColumnDef::new(Plans::PriorityLevel).integer().default(1))
                        .col(ColumnDef::new(Plans::ConfigTemplate).text().not_null())
                        .col(ColumnDef::new(Plans::IsActive).boolean().default(true))
                        .col(ColumnDef::new(Plans::IsFeatured).boolean().default(false))
                        .col(ColumnDef::new(Plans::SortOrder).integer().default(0))
                        .col(
                            ColumnDef::new(Plans::CreatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(Plans::UpdatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn create_subscriptions_table(
            &self,
            manager: &SchemaManager<'_>,
        ) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(Subscriptions::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(Subscriptions::Id)
                                .uuid()
                                .not_null()
                                .primary_key()
                                .extra("DEFAULT gen_random_uuid()".to_string()),
                        )
                        .col(ColumnDef::new(Subscriptions::UserId).uuid().not_null())
                        .col(ColumnDef::new(Subscriptions::PlanId).uuid().not_null())
                        .col(
                            ColumnDef::new(Subscriptions::StartDate)
                                .timestamp_with_time_zone()
                                .not_null(),
                        )
                        .col(
                            ColumnDef::new(Subscriptions::EndDate)
                                .timestamp_with_time_zone()
                                .not_null(),
                        )
                        .col(ColumnDef::new(Subscriptions::TotalTrafficGb).decimal_len(10, 3))
                        .col(
                            ColumnDef::new(Subscriptions::UsedTrafficGb)
                                .decimal_len(10, 3)
                                .default(0),
                        )
                        .col(
                            ColumnDef::new(Subscriptions::MaxConcurrentDevices)
                                .integer()
                                .default(1),
                        )
                        .col(
                            ColumnDef::new(Subscriptions::CurrentDevicesCount)
                                .integer()
                                .default(0),
                        )
                        .col(
                            ColumnDef::new(Subscriptions::Status)
                                .enumeration(
                                    SubscriptionStatus::Type,
                                    [
                                        SubscriptionStatus::Active,
                                        SubscriptionStatus::Expired,
                                        SubscriptionStatus::Suspended,
                                        SubscriptionStatus::Cancelled,
                                    ],
                                )
                                .default("active"),
                        )
                        .col(
                            ColumnDef::new(Subscriptions::AutoRenew)
                                .boolean()
                                .default(false),
                        )
                        .col(ColumnDef::new(Subscriptions::AutoRenewPlanId).uuid())
                        .col(ColumnDef::new(Subscriptions::ProxyUsername).string_len(100))
                        .col(ColumnDef::new(Subscriptions::ProxyPassword).string_len(255))
                        .col(ColumnDef::new(Subscriptions::ProxyPort).integer())
                        .col(ColumnDef::new(Subscriptions::ProxyConfigHash).string_len(64))
                        .col(
                            ColumnDef::new(Subscriptions::CreatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(Subscriptions::UpdatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(Subscriptions::LastTrafficSync)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(Subscriptions::TotalConnectionsCount)
                                .big_integer()
                                .default(0),
                        )
                        .col(
                            ColumnDef::new(Subscriptions::TotalSessionTime)
                                .big_integer()
                                .default(0),
                        )
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_subscriptions_user_id")
                                .from(Subscriptions::Table, Subscriptions::UserId)
                                .to(Users::Table, Users::Id)
                                .on_delete(ForeignKeyAction::Cascade)
                                .on_update(ForeignKeyAction::Cascade),
                        )
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_subscriptions_plan_id")
                                .from(Subscriptions::Table, Subscriptions::PlanId)
                                .to(Plans::Table, Plans::Id)
                                .on_delete(ForeignKeyAction::Cascade)
                                .on_update(ForeignKeyAction::Cascade),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn create_orders_table(&self, manager: &SchemaManager<'_>) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(Orders::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(Orders::Id)
                                .uuid()
                                .not_null()
                                .primary_key()
                                .extra("DEFAULT gen_random_uuid()".to_string()),
                        )
                        .col(
                            ColumnDef::new(Orders::OrderNumber)
                                .string_len(100)
                                .not_null()
                                .unique_key(),
                        )
                        .col(ColumnDef::new(Orders::UserId).uuid().not_null())
                        .col(ColumnDef::new(Orders::PlanId).uuid().not_null())
                        .col(ColumnDef::new(Orders::Amount).decimal_len(10, 2).not_null())
                        .col(
                            ColumnDef::new(Orders::Currency)
                                .string_len(3)
                                .default("USD"),
                        )
                        .col(ColumnDef::new(Orders::Quantity).integer().default(1))
                        .col(
                            ColumnDef::new(Orders::PaymentMethod)
                                .enumeration(
                                    PaymentMethodType::Type,
                                    [
                                        PaymentMethodType::Manual,
                                        PaymentMethodType::Alipay,
                                        PaymentMethodType::Wechat,
                                        PaymentMethodType::Paypal,
                                        PaymentMethodType::Crypto,
                                    ],
                                )
                                .not_null(),
                        )
                        .col(ColumnDef::new(Orders::PaymentReference).string_len(255))
                        .col(ColumnDef::new(Orders::PaymentProofUrl).string_len(500))
                        .col(
                            ColumnDef::new(Orders::Status)
                                .enumeration(
                                    OrderStatus::Type,
                                    [
                                        OrderStatus::PendingPayment,
                                        OrderStatus::Paid,
                                        OrderStatus::Processing,
                                        OrderStatus::Completed,
                                        OrderStatus::Cancelled,
                                        OrderStatus::Refunded,
                                    ],
                                )
                                .default("pending_payment"),
                        )
                        .col(ColumnDef::new(Orders::ProcessedBy).uuid())
                        .col(ColumnDef::new(Orders::ProcessedAt).timestamp_with_time_zone())
                        .col(ColumnDef::new(Orders::Notes).text())
                        .col(
                            ColumnDef::new(Orders::CreatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(Orders::UpdatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(ColumnDef::new(Orders::ExpiresAt).timestamp_with_time_zone())
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_orders_user_id")
                                .from(Orders::Table, Orders::UserId)
                                .to(Users::Table, Users::Id)
                                .on_delete(ForeignKeyAction::Cascade)
                                .on_update(ForeignKeyAction::Cascade),
                        )
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_orders_plan_id")
                                .from(Orders::Table, Orders::PlanId)
                                .to(Plans::Table, Plans::Id)
                                .on_delete(ForeignKeyAction::Cascade)
                                .on_update(ForeignKeyAction::Cascade),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn create_announcements_table(
            &self,
            manager: &SchemaManager<'_>,
        ) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(Announcements::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(Announcements::Id)
                                .uuid()
                                .not_null()
                                .primary_key()
                                .extra("DEFAULT gen_random_uuid()".to_string()),
                        )
                        .col(
                            ColumnDef::new(Announcements::Title)
                                .string_len(200)
                                .not_null(),
                        )
                        .col(ColumnDef::new(Announcements::Content).text().not_null())
                        .col(
                            ColumnDef::new(Announcements::TargetAudience)
                                .enumeration(
                                    AnnouncementAudience::Type,
                                    [
                                        AnnouncementAudience::All,
                                        AnnouncementAudience::ActiveUsers,
                                        AnnouncementAudience::ExpiredUsers,
                                        AnnouncementAudience::SpecificPlans,
                                    ],
                                )
                                .default("all"),
                        )
                        .col(ColumnDef::new(Announcements::TargetPlanIds).json())
                        .col(
                            ColumnDef::new(Announcements::AnnouncementType)
                                .enumeration(
                                    AnnouncementType::Type,
                                    [
                                        AnnouncementType::Info,
                                        AnnouncementType::Warning,
                                        AnnouncementType::Maintenance,
                                        AnnouncementType::Promotion,
                                        AnnouncementType::Urgent,
                                    ],
                                )
                                .default("info"),
                        )
                        .col(
                            ColumnDef::new(Announcements::IsPopup)
                                .boolean()
                                .default(false),
                        )
                        .col(
                            ColumnDef::new(Announcements::IsPinned)
                                .boolean()
                                .default(false),
                        )
                        .col(
                            ColumnDef::new(Announcements::IsActive)
                                .boolean()
                                .default(true),
                        )
                        .col(
                            ColumnDef::new(Announcements::PublishAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(ColumnDef::new(Announcements::ExpiresAt).timestamp_with_time_zone())
                        .col(
                            ColumnDef::new(Announcements::CreatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(Announcements::UpdatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(ColumnDef::new(Announcements::CreatedBy).uuid().not_null())
                        .to_owned(),
                )
                .await
        }

        async fn create_connection_logs_table(
            &self,
            manager: &SchemaManager<'_>,
        ) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(ConnectionLogs::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(ConnectionLogs::Id)
                                .uuid()
                                .not_null()
                                .primary_key()
                                .extra("DEFAULT gen_random_uuid()".to_string()),
                        )
                        .col(ColumnDef::new(ConnectionLogs::UserId).uuid().not_null())
                        .col(
                            ColumnDef::new(ConnectionLogs::SubscriptionId)
                                .uuid()
                                .not_null(),
                        )
                        .col(
                            ColumnDef::new(ConnectionLogs::ClientIp)
                                .string_len(45)
                                .not_null(),
                        )
                        .col(ColumnDef::new(ConnectionLogs::ClientPort).integer())
                        .col(ColumnDef::new(ConnectionLogs::UserAgent).string_len(500))
                        .col(ColumnDef::new(ConnectionLogs::DeviceInfo).json())
                        .col(ColumnDef::new(ConnectionLogs::SessionId).string_len(100))
                        .col(
                            ColumnDef::new(ConnectionLogs::ConnectedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(ConnectionLogs::DisconnectedAt)
                                .timestamp_with_time_zone(),
                        )
                        .col(
                            ColumnDef::new(ConnectionLogs::DurationSeconds)
                                .integer()
                                .default(0),
                        )
                        .col(
                            ColumnDef::new(ConnectionLogs::BytesUploaded)
                                .big_integer()
                                .default(0),
                        )
                        .col(
                            ColumnDef::new(ConnectionLogs::BytesDownloaded)
                                .big_integer()
                                .default(0),
                        )
                        .col(ColumnDef::new(ConnectionLogs::DisconnectReason).string_len(100))
                        .col(ColumnDef::new(ConnectionLogs::CountryCode).string_len(2))
                        .col(ColumnDef::new(ConnectionLogs::City).string_len(100))
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_connection_logs_user_id")
                                .from(ConnectionLogs::Table, ConnectionLogs::UserId)
                                .to(Users::Table, Users::Id)
                                .on_delete(ForeignKeyAction::Cascade)
                                .on_update(ForeignKeyAction::Cascade),
                        )
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_connection_logs_subscription_id")
                                .from(ConnectionLogs::Table, ConnectionLogs::SubscriptionId)
                                .to(Subscriptions::Table, Subscriptions::Id)
                                .on_delete(ForeignKeyAction::Cascade)
                                .on_update(ForeignKeyAction::Cascade),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn create_system_settings_table(
            &self,
            manager: &SchemaManager<'_>,
        ) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(SystemSettings::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(SystemSettings::Id)
                                .uuid()
                                .not_null()
                                .primary_key()
                                .extra("DEFAULT gen_random_uuid()".to_string()),
                        )
                        .col(
                            ColumnDef::new(SystemSettings::Key)
                                .string_len(100)
                                .not_null()
                                .unique_key(),
                        )
                        .col(ColumnDef::new(SystemSettings::Value).text().not_null())
                        .col(
                            ColumnDef::new(SystemSettings::ValueType)
                                .enumeration(
                                    SettingValueType::Type,
                                    [
                                        SettingValueType::String,
                                        SettingValueType::Integer,
                                        SettingValueType::Decimal,
                                        SettingValueType::Boolean,
                                        SettingValueType::Json,
                                    ],
                                )
                                .default("string"),
                        )
                        .col(ColumnDef::new(SystemSettings::Description).text())
                        .col(
                            ColumnDef::new(SystemSettings::IsPublic)
                                .boolean()
                                .default(false),
                        )
                        .col(
                            ColumnDef::new(SystemSettings::CreatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(SystemSettings::UpdatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn create_api_tokens_table(&self, manager: &SchemaManager<'_>) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(ApiTokens::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(ApiTokens::Id)
                                .uuid()
                                .not_null()
                                .primary_key()
                                .extra("DEFAULT gen_random_uuid()".to_string()),
                        )
                        .col(ColumnDef::new(ApiTokens::UserId).uuid().not_null())
                        .col(
                            ColumnDef::new(ApiTokens::TokenHash)
                                .string_len(255)
                                .not_null()
                                .unique_key(),
                        )
                        .col(ColumnDef::new(ApiTokens::Name).string_len(100).not_null())
                        .col(ColumnDef::new(ApiTokens::Permissions).json())
                        .col(ColumnDef::new(ApiTokens::ExpiresAt).timestamp_with_time_zone())
                        .col(ColumnDef::new(ApiTokens::LastUsedAt).timestamp_with_time_zone())
                        .col(ColumnDef::new(ApiTokens::LastUsedIp).string_len(45))
                        .col(ColumnDef::new(ApiTokens::IsActive).boolean().default(true))
                        .col(
                            ColumnDef::new(ApiTokens::CreatedAt)
                                .timestamp_with_time_zone()
                                .default(Expr::current_timestamp()),
                        )
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_api_tokens_user_id")
                                .from(ApiTokens::Table, ApiTokens::UserId)
                                .to(Users::Table, Users::Id)
                                .on_delete(ForeignKeyAction::Cascade)
                                .on_update(ForeignKeyAction::Cascade),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn create_indexes(&self, manager: &SchemaManager<'_>) -> Result<(), DbErr> {
            // Users table indexes
            manager
                .create_index(
                    Index::create()
                        .name("idx_users_username")
                        .table(Users::Table)
                        .col(Users::Username)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_users_email")
                        .table(Users::Table)
                        .col(Users::Email)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_users_is_active")
                        .table(Users::Table)
                        .col(Users::IsActive)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_users_created_at")
                        .table(Users::Table)
                        .col(Users::CreatedAt)
                        .to_owned(),
                )
                .await?;

            // Plans table indexes
            manager
                .create_index(
                    Index::create()
                        .name("idx_plans_is_active")
                        .table(Plans::Table)
                        .col(Plans::IsActive)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_plans_sort_order")
                        .table(Plans::Table)
                        .col(Plans::SortOrder)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_plans_price")
                        .table(Plans::Table)
                        .col(Plans::Price)
                        .to_owned(),
                )
                .await?;

            // Subscriptions table indexes
            manager
                .create_index(
                    Index::create()
                        .name("idx_subscriptions_user_id")
                        .table(Subscriptions::Table)
                        .col(Subscriptions::UserId)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_subscriptions_plan_id")
                        .table(Subscriptions::Table)
                        .col(Subscriptions::PlanId)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_subscriptions_status")
                        .table(Subscriptions::Table)
                        .col(Subscriptions::Status)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_subscriptions_end_date")
                        .table(Subscriptions::Table)
                        .col(Subscriptions::EndDate)
                        .to_owned(),
                )
                .await?;

            // Orders table indexes
            manager
                .create_index(
                    Index::create()
                        .name("idx_orders_user_id")
                        .table(Orders::Table)
                        .col(Orders::UserId)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_orders_status")
                        .table(Orders::Table)
                        .col(Orders::Status)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_orders_created_at")
                        .table(Orders::Table)
                        .col(Orders::CreatedAt)
                        .to_owned(),
                )
                .await?;

            // Connection logs table indexes
            manager
                .create_index(
                    Index::create()
                        .name("idx_connection_logs_user_id")
                        .table(ConnectionLogs::Table)
                        .col(ConnectionLogs::UserId)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_connection_logs_subscription_id")
                        .table(ConnectionLogs::Table)
                        .col(ConnectionLogs::SubscriptionId)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_connection_logs_connected_at")
                        .table(ConnectionLogs::Table)
                        .col(ConnectionLogs::ConnectedAt)
                        .to_owned(),
                )
                .await?;

            // System settings table indexes
            manager
                .create_index(
                    Index::create()
                        .name("idx_system_settings_key")
                        .table(SystemSettings::Table)
                        .col(SystemSettings::Key)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_system_settings_is_public")
                        .table(SystemSettings::Table)
                        .col(SystemSettings::IsPublic)
                        .to_owned(),
                )
                .await?;

            // API tokens table indexes
            manager
                .create_index(
                    Index::create()
                        .name("idx_api_tokens_user_id")
                        .table(ApiTokens::Table)
                        .col(ApiTokens::UserId)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_api_tokens_is_active")
                        .table(ApiTokens::Table)
                        .col(ApiTokens::IsActive)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_api_tokens_expires_at")
                        .table(ApiTokens::Table)
                        .col(ApiTokens::ExpiresAt)
                        .to_owned(),
                )
                .await?;

            Ok(())
        }
    }

    // Table enums
    #[derive(Iden)]
    enum Users {
        Table,
        Id,
        Username,
        Email,
        PasswordHash,
        IsActive,
        IsAdmin,
        CreatedAt,
        UpdatedAt,
        LastLoginAt,
        MaxConcurrentDevices,
        IsBanned,
        BanReason,
        BannedUntil,
        TotalTrafficUsed,
        LastTrafficReset,
    }

    #[derive(Iden)]
    enum Plans {
        Table,
        Id,
        Name,
        Description,
        Price,
        Currency,
        DurationDays,
        TrafficLimitGb,
        MaxConcurrentDevices,
        MaxConcurrentConnections,
        SpeedLimitUpload,
        SpeedLimitDownload,
        PriorityLevel,
        ConfigTemplate,
        IsActive,
        IsFeatured,
        SortOrder,
        CreatedAt,
        UpdatedAt,
    }

    #[derive(Iden)]
    enum Subscriptions {
        Table,
        Id,
        UserId,
        PlanId,
        StartDate,
        EndDate,
        TotalTrafficGb,
        UsedTrafficGb,
        MaxConcurrentDevices,
        CurrentDevicesCount,
        Status,
        AutoRenew,
        AutoRenewPlanId,
        ProxyUsername,
        ProxyPassword,
        ProxyPort,
        ProxyConfigHash,
        CreatedAt,
        UpdatedAt,
        LastTrafficSync,
        TotalConnectionsCount,
        TotalSessionTime,
    }

    #[derive(Iden)]
    enum Orders {
        Table,
        Id,
        OrderNumber,
        UserId,
        PlanId,
        Amount,
        Currency,
        Quantity,
        PaymentMethod,
        PaymentReference,
        PaymentProofUrl,
        Status,
        ProcessedBy,
        ProcessedAt,
        Notes,
        CreatedAt,
        UpdatedAt,
        ExpiresAt,
    }

    #[derive(Iden)]
    enum Announcements {
        Table,
        Id,
        Title,
        Content,
        TargetAudience,
        TargetPlanIds,
        AnnouncementType,
        IsPopup,
        IsPinned,
        IsActive,
        PublishAt,
        ExpiresAt,
        CreatedAt,
        UpdatedAt,
        CreatedBy,
    }

    #[derive(Iden)]
    enum ConnectionLogs {
        Table,
        Id,
        UserId,
        SubscriptionId,
        ClientIp,
        ClientPort,
        UserAgent,
        DeviceInfo,
        SessionId,
        ConnectedAt,
        DisconnectedAt,
        DurationSeconds,
        BytesUploaded,
        BytesDownloaded,
        DisconnectReason,
        CountryCode,
        City,
    }

    #[derive(Iden)]
    enum SystemSettings {
        Table,
        Id,
        Key,
        Value,
        ValueType,
        Description,
        IsPublic,
        CreatedAt,
        UpdatedAt,
    }

    #[derive(Iden)]
    enum ApiTokens {
        Table,
        Id,
        UserId,
        TokenHash,
        Name,
        Permissions,
        ExpiresAt,
        LastUsedAt,
        LastUsedIp,
        IsActive,
        CreatedAt,
    }

    #[derive(Iden)]
    enum SubscriptionStatus {
        #[iden = "subscription_status"]
        Type,
        #[iden = "active"]
        Active,
        #[iden = "expired"]
        Expired,
        #[iden = "suspended"]
        Suspended,
        #[iden = "cancelled"]
        Cancelled,
    }

    #[derive(Iden)]
    enum OrderStatus {
        #[iden = "order_status"]
        Type,
        #[iden = "pending_payment"]
        PendingPayment,
        #[iden = "paid"]
        Paid,
        #[iden = "processing"]
        Processing,
        #[iden = "completed"]
        Completed,
        #[iden = "cancelled"]
        Cancelled,
        #[iden = "refunded"]
        Refunded,
    }

    #[derive(Iden)]
    enum PaymentMethodType {
        #[iden = "payment_method"]
        Type,
        #[iden = "manual"]
        Manual,
        #[iden = "alipay"]
        Alipay,
        #[iden = "wechat"]
        Wechat,
        #[iden = "paypal"]
        Paypal,
        #[iden = "crypto"]
        Crypto,
    }

    #[derive(Iden)]
    enum AnnouncementAudience {
        #[iden = "announcement_audience"]
        Type,
        #[iden = "all"]
        All,
        #[iden = "active_users"]
        ActiveUsers,
        #[iden = "expired_users"]
        ExpiredUsers,
        #[iden = "specific_plans"]
        SpecificPlans,
    }

    #[derive(Iden)]
    enum AnnouncementType {
        #[iden = "announcement_type"]
        Type,
        #[iden = "info"]
        Info,
        #[iden = "warning"]
        Warning,
        #[iden = "maintenance"]
        Maintenance,
        #[iden = "promotion"]
        Promotion,
        #[iden = "urgent"]
        Urgent,
    }

    #[derive(Iden)]
    enum SettingValueType {
        #[iden = "setting_value_type"]
        Type,
        #[iden = "string"]
        String,
        #[iden = "integer"]
        Integer,
        #[iden = "decimal"]
        Decimal,
        #[iden = "boolean"]
        Boolean,
        #[iden = "json"]
        Json,
    }
}
