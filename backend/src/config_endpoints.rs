// Configuration template management API endpoints

use axum::{
    extract::State,
    response::Json,
    routing::{get, post, put},
    Router,
};
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

use crate::auth::UserContext;
use crate::errors::{AppError, AppResult, ApiResponse, success};
use crate::config_generator::ConfigTemplateManager;
use crate::repositories::{SystemSettingRepository, Repository};

/// Request structure for validating configuration template
#[derive(Debug, Deserialize)]
pub struct ValidateTemplateRequest {
    pub template: String,
}

/// Response structure for template validation
#[derive(Debug, Serialize)]
pub struct ValidateTemplateResponse {
    pub is_valid: bool,
    pub placeholders: Vec<String>,
    pub errors: Vec<String>,
}

/// Response structure for available placeholders
#[derive(Debug, Serialize)]
pub struct AvailablePlaceholdersResponse {
    pub placeholders: HashMap<String, String>,
}

/// Request structure for updating system template
#[derive(Debug, Deserialize)]
pub struct UpdateSystemTemplateRequest {
    pub template: String,
    pub description: Option<String>,
}

/// Response structure for system template
#[derive(Debug, Serialize)]
pub struct SystemTemplateResponse {
    pub template: String,
    pub description: Option<String>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub updated_by: Uuid,
}

/// GET /api/admin/config/placeholders - Get available placeholders
pub async fn get_available_placeholders(
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<AvailablePlaceholdersResponse>>> {
    let config_manager = ConfigTemplateManager::new();
    let placeholders = config_manager.get_available_placeholders().clone();
    
    let response = AvailablePlaceholdersResponse {
        placeholders,
    };
    
    Ok(Json(success(response)))
}

/// POST /api/admin/config/validate - Validate configuration template
pub async fn validate_template(
    _user_context: UserContext,
    Json(request): Json<ValidateTemplateRequest>,
) -> AppResult<Json<ApiResponse<ValidateTemplateResponse>>> {
    let config_manager = ConfigTemplateManager::new();
    
    let (is_valid, placeholders, errors) = match config_manager.validate_template(&request.template) {
        Ok(placeholders) => (true, placeholders, Vec::new()),
        Err(error) => (false, Vec::new(), vec![error.to_string()]),
    };
    
    let response = ValidateTemplateResponse {
        is_valid,
        placeholders,
        errors,
    };
    
    Ok(Json(success(response)))
}

/// GET /api/admin/config/system-template - Get system default template
pub async fn get_system_template(
    State(db): State<DatabaseConnection>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<SystemTemplateResponse>>> {
    let system_settings_repo = SystemSettingRepository::new(std::sync::Arc::new(db));
    
    let template_setting = system_settings_repo.find_by_key("default_config_template").await?;
    let description_setting = system_settings_repo.find_by_key("default_config_template_description").await?;
    
    let template = template_setting
        .map(|s| s.value.unwrap_or_default())
        .unwrap_or_else(|| DEFAULT_CONFIG_TEMPLATE.to_string());
    
    let description = description_setting
        .and_then(|s| s.value);
    
    let response = SystemTemplateResponse {
        template,
        description,
        updated_at: chrono::Utc::now(),
        updated_by: Uuid::new_v4(), // TODO: Get from user context
    };
    
    Ok(Json(success(response)))
}

/// PUT /api/admin/config/system-template - Update system default template
pub async fn update_system_template(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Json(request): Json<UpdateSystemTemplateRequest>,
) -> AppResult<Json<ApiResponse<SystemTemplateResponse>>> {
    let system_settings_repo = SystemSettingRepository::new(std::sync::Arc::new(db));
    
    // Validate the template first
    let config_manager = ConfigTemplateManager::new();
    config_manager.validate_template(&request.template)?;
    
    // Update template in system settings
    system_settings_repo.set_string_value(
        "default_config_template",
        &request.template,
        Some("Default VPN configuration template"),
        false,
    ).await?;
    
    if let Some(description) = &request.description {
        system_settings_repo.set_string_value(
            "default_config_template_description",
            description,
            Some("Description for default VPN configuration template"),
            false,
        ).await?;
    }
    
    let response = SystemTemplateResponse {
        template: request.template,
        description: request.description,
        updated_at: chrono::Utc::now(),
        updated_by: user_context.user_id,
    };
    
    Ok(Json(success(response)))
}

/// Create configuration management routes (admin only)
pub fn config_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/placeholders", get(get_available_placeholders))
        .route("/validate", post(validate_template))
        .route("/system-template", get(get_system_template).put(update_system_template))
}

/// Default configuration template
const DEFAULT_CONFIG_TEMPLATE: &str = r#"
[General]
loglevel = info
dns-server = *******, *******
tun-fd = 
always-real-ip = 
interface = 
rt-mark = 

[Proxy]
proxy = socks, {{proxy_username}}:{{proxy_password}}@{{server_endpoint}}, udp-relay=true

[Rule]
FINAL, proxy

# Generated at: {{generated_at}}
# User: {{username}} ({{user_id}})
# Plan: {{plan_name}}
# Subscription expires: {{subscription_expires}}
# Traffic: {{traffic_used}}/{{traffic_limit}} GB
# Config hash: {{config_hash}}
"#;