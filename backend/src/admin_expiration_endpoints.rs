// Admin endpoints for subscription expiration and user cleanup management
use axum::{
    extract::State,
    response::Json,
    routing::{get, post},
    Router,
};
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

use crate::auth::UserContext;
use crate::errors::{AppError, AppResult, ApiResponse, success};
use crate::services::subscription_expiration::{SubscriptionExpirationService, ExpirationConfig};

#[derive(Debug, Deserialize)]
pub struct UpdateExpirationConfigRequest {
    pub grace_period_days: Option<i32>,
    pub cleanup_warning_days: Option<i32>,
    pub final_cleanup_days: Option<i32>,
    pub notification_enabled: Option<bool>,
    pub data_retention_days: Option<i32>,
}

#[derive(Debug, Serialize)]
pub struct ExpirationStatsResponse {
    pub total_subscriptions: u32,
    pub total_users: u32,
    pub expired_subscriptions: u32,
    pub expiring_soon: u32,
    pub inactive_users: u32,
    pub users_in_grace_period: u32,
    pub users_due_for_cleanup: u32,
    pub config: ExpirationConfig,
}

/// GET /api/admin/expiration/stats - Get expiration statistics
pub async fn get_expiration_stats(
    State(db): State<DatabaseConnection>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let expiration_service = SubscriptionExpirationService::new(Arc::new(db));
    
    match expiration_service.get_expiration_stats().await {
        Ok(stats) => Ok(Json(success(stats))),
        Err(e) => {
            tracing::error!("Failed to get expiration stats: {}", e);
            Err(AppError::internal(&format!("Failed to get expiration stats: {}", e)))
        }
    }
}

/// POST /api/admin/expiration/process - Manually trigger expiration processing
pub async fn trigger_expiration_processing(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let expiration_service = SubscriptionExpirationService::new(Arc::new(db));
    
    match expiration_service.trigger_expiration_processing().await {
        Ok(result) => {
            tracing::info!("Expiration processing triggered by admin {}", user_context.username);
            Ok(Json(success(serde_json::json!({
                "message": "Expiration processing completed successfully",
                "expired_subscriptions": result.expired_subscriptions,
                "deactivated_users": result.deactivated_users,
                "cleaned_up_users": result.cleaned_up_users,
                "notifications_sent": result.notifications_sent,
                "triggered_by": user_context.username,
                "triggered_at": chrono::Utc::now()
            }))))
        }
        Err(e) => {
            tracing::error!("Failed to trigger expiration processing: {}", e);
            Err(AppError::internal(&format!("Failed to trigger expiration processing: {}", e)))
        }
    }
}

/// POST /api/admin/expiration/cleanup - Manually trigger user cleanup
pub async fn trigger_user_cleanup(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let expiration_service = SubscriptionExpirationService::new(Arc::new(db));
    
    match expiration_service.trigger_user_cleanup().await {
        Ok(cleaned_up_count) => {
            tracing::info!("User cleanup triggered by admin {}", user_context.username);
            Ok(Json(success(serde_json::json!({
                "message": "User cleanup completed successfully",
                "cleaned_up_users": cleaned_up_count,
                "triggered_by": user_context.username,
                "triggered_at": chrono::Utc::now()
            }))))
        }
        Err(e) => {
            tracing::error!("Failed to trigger user cleanup: {}", e);
            Err(AppError::internal(&format!("Failed to trigger user cleanup: {}", e)))
        }
    }
}

/// GET /api/admin/expiration/config - Get current expiration configuration
pub async fn get_expiration_config(
    State(db): State<DatabaseConnection>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<ExpirationConfig>>> {
    let expiration_service = SubscriptionExpirationService::new(Arc::new(db));
    let config = ExpirationConfig::default(); // Get from service if needed
    
    Ok(Json(success(config)))
}

/// POST /api/admin/expiration/config - Update expiration configuration
pub async fn update_expiration_config(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Json(request): Json<UpdateExpirationConfigRequest>,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let mut config = ExpirationConfig::default();
    
    // Update configuration fields if provided
    if let Some(grace_period) = request.grace_period_days {
        config.grace_period_days = grace_period;
    }
    if let Some(cleanup_warning) = request.cleanup_warning_days {
        config.cleanup_warning_days = cleanup_warning;
    }
    if let Some(final_cleanup) = request.final_cleanup_days {
        config.final_cleanup_days = final_cleanup;
    }
    if let Some(notification_enabled) = request.notification_enabled {
        config.notification_enabled = notification_enabled;
    }
    if let Some(data_retention) = request.data_retention_days {
        config.data_retention_days = data_retention;
    }
    
    // In a real implementation, you would persist this configuration
    // For now, we just return the updated config
    tracing::info!("Expiration configuration updated by admin {}", user_context.username);
    
    Ok(Json(success(serde_json::json!({
        "message": "Expiration configuration updated successfully",
        "config": config,
        "updated_by": user_context.username,
        "updated_at": chrono::Utc::now()
    }))))
}

/// GET /api/admin/expiration/preview - Preview what would be processed
pub async fn preview_expiration_actions(
    State(db): State<DatabaseConnection>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let expiration_service = SubscriptionExpirationService::new(Arc::new(db));
    
    // This would normally be a separate preview method
    // For now, we'll return the stats as a preview
    match expiration_service.get_expiration_stats().await {
        Ok(stats) => Ok(Json(success(serde_json::json!({
            "message": "Preview of expiration actions",
            "stats": stats,
            "preview_note": "This shows current statistics. In a real implementation, this would show exactly what would be processed."
        })))),
        Err(e) => {
            tracing::error!("Failed to get expiration preview: {}", e);
            Err(AppError::internal(&format!("Failed to get expiration preview: {}", e)))
        }
    }
}

/// GET /api/admin/expiration/health - Check expiration service health
pub async fn get_expiration_health(
    State(db): State<DatabaseConnection>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let expiration_service = SubscriptionExpirationService::new(Arc::new(db));
    
    // Basic health check
    let health_status = match expiration_service.get_expiration_stats().await {
        Ok(_) => "healthy",
        Err(_) => "unhealthy",
    };
    
    Ok(Json(success(serde_json::json!({
        "status": health_status,
        "service": "subscription_expiration",
        "checked_at": chrono::Utc::now(),
        "config": ExpirationConfig::default()
    }))))
}

/// Create admin expiration management routes
pub fn admin_expiration_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/stats", get(get_expiration_stats))
        .route("/process", post(trigger_expiration_processing))
        .route("/cleanup", post(trigger_user_cleanup))
        .route("/config", get(get_expiration_config))
        .route("/config", post(update_expiration_config))
        .route("/preview", get(preview_expiration_actions))
        .route("/health", get(get_expiration_health))
}