use sea_orm::{DatabaseConnection, DbErr, EntityTrait, QueryFilter, QueryOrder, ActiveModelTrait, Set, JoinType, RelationTrait};
use sea_orm::prelude::*;
use std::sync::Arc;

use crate::entities::{announcements, users, prelude::*, enums::{AnnouncementAudience, AnnouncementType}};
use super::{Repository, PaginatedRepository, PaginationParams, PaginatedResult, PaginationHelper, RepositoryError, RepositoryResult};

#[derive(Clone)]
pub struct AnnouncementRepository {
    db: Arc<DatabaseConnection>,
}

impl AnnouncementRepository {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self { db }
    }

    pub async fn find_active_announcements(&self) -> RepositoryResult<Vec<announcements::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let announcements = Announcements::find()
            .filter(announcements::Column::IsActive.eq(true))
            .filter(announcements::Column::PublishAt.lte(now))
            .filter(
                announcements::Column::ExpiresAt.is_null()
                    .or(announcements::Column::ExpiresAt.gt(now))
            )
            .order_by_desc(announcements::Column::IsPinned)
            .order_by_desc(announcements::Column::PublishAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(announcements)
    }

    pub async fn find_by_audience(&self, audience: AnnouncementAudience) -> RepositoryResult<Vec<announcements::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let announcements = Announcements::find()
            .filter(announcements::Column::IsActive.eq(true))
            .filter(announcements::Column::PublishAt.lte(now))
            .filter(
                announcements::Column::ExpiresAt.is_null()
                    .or(announcements::Column::ExpiresAt.gt(now))
            )
            .filter(announcements::Column::TargetAudience.eq(audience))
            .order_by_desc(announcements::Column::IsPinned)
            .order_by_desc(announcements::Column::PublishAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(announcements)
    }

    pub async fn find_by_type(&self, announcement_type: AnnouncementType) -> RepositoryResult<Vec<announcements::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let announcements = Announcements::find()
            .filter(announcements::Column::IsActive.eq(true))
            .filter(announcements::Column::PublishAt.lte(now))
            .filter(
                announcements::Column::ExpiresAt.is_null()
                    .or(announcements::Column::ExpiresAt.gt(now))
            )
            .filter(announcements::Column::AnnouncementType.eq(announcement_type))
            .order_by_desc(announcements::Column::IsPinned)
            .order_by_desc(announcements::Column::PublishAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(announcements)
    }

    pub async fn find_pinned_announcements(&self) -> RepositoryResult<Vec<announcements::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let announcements = Announcements::find()
            .filter(announcements::Column::IsActive.eq(true))
            .filter(announcements::Column::IsPinned.eq(true))
            .filter(announcements::Column::PublishAt.lte(now))
            .filter(
                announcements::Column::ExpiresAt.is_null()
                    .or(announcements::Column::ExpiresAt.gt(now))
            )
            .order_by_desc(announcements::Column::PublishAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(announcements)
    }

    pub async fn find_popup_announcements(&self) -> RepositoryResult<Vec<announcements::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let announcements = Announcements::find()
            .filter(announcements::Column::IsActive.eq(true))
            .filter(announcements::Column::IsPopup.eq(true))
            .filter(announcements::Column::PublishAt.lte(now))
            .filter(
                announcements::Column::ExpiresAt.is_null()
                    .or(announcements::Column::ExpiresAt.gt(now))
            )
            .order_by_desc(announcements::Column::PublishAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(announcements)
    }

    pub async fn find_by_created_by(&self, created_by: Uuid) -> RepositoryResult<Vec<announcements::Model>> {
        let announcements = Announcements::find()
            .filter(announcements::Column::CreatedBy.eq(created_by))
            .order_by_desc(announcements::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(announcements)
    }

    pub async fn find_for_plan(&self, plan_id: Uuid) -> RepositoryResult<Vec<announcements::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let announcements = Announcements::find()
            .filter(announcements::Column::IsActive.eq(true))
            .filter(announcements::Column::PublishAt.lte(now))
            .filter(
                announcements::Column::ExpiresAt.is_null()
                    .or(announcements::Column::ExpiresAt.gt(now))
            )
            .filter(
                announcements::Column::TargetAudience.eq(AnnouncementAudience::All)
                    .or(announcements::Column::TargetAudience.eq(AnnouncementAudience::SpecificPlans))
            )
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        // Filter by plan ID in target_plan_ids JSON field
        let filtered_announcements: Vec<announcements::Model> = announcements.into_iter()
            .filter(|announcement| {
                if announcement.target_audience == AnnouncementAudience::All {
                    return true;
                }
                
                if let Some(target_plan_ids) = &announcement.target_plan_ids {
                    if let Ok(plan_ids) = serde_json::from_value::<Vec<Uuid>>(target_plan_ids.clone()) {
                        return plan_ids.contains(&plan_id);
                    }
                }
                
                false
            })
            .collect();
        
        Ok(filtered_announcements)
    }

    pub async fn find_expired_announcements(&self) -> RepositoryResult<Vec<announcements::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let announcements = Announcements::find()
            .filter(announcements::Column::ExpiresAt.is_not_null())
            .filter(announcements::Column::ExpiresAt.lt(now))
            .filter(announcements::Column::IsActive.eq(true))
            .order_by_asc(announcements::Column::ExpiresAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(announcements)
    }

    pub async fn find_with_creator(&self, announcement_id: Uuid) -> RepositoryResult<Option<(announcements::Model, Option<users::Model>)>> {
        let result = Announcements::find_by_id(announcement_id)
            .find_also_related(Users)
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(result)
    }

    pub async fn activate_announcement(&self, announcement_id: Uuid) -> RepositoryResult<announcements::Model> {
        let mut announcement: announcements::ActiveModel = announcements::ActiveModel {
            id: Set(announcement_id),
            is_active: Set(true),
            ..Default::default()
        };
        
        let announcement = announcement.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(announcement)
    }

    pub async fn deactivate_announcement(&self, announcement_id: Uuid) -> RepositoryResult<announcements::Model> {
        let mut announcement: announcements::ActiveModel = announcements::ActiveModel {
            id: Set(announcement_id),
            is_active: Set(false),
            ..Default::default()
        };
        
        let announcement = announcement.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(announcement)
    }

    pub async fn pin_announcement(&self, announcement_id: Uuid) -> RepositoryResult<announcements::Model> {
        let mut announcement: announcements::ActiveModel = announcements::ActiveModel {
            id: Set(announcement_id),
            is_pinned: Set(true),
            ..Default::default()
        };
        
        let announcement = announcement.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(announcement)
    }

    pub async fn unpin_announcement(&self, announcement_id: Uuid) -> RepositoryResult<announcements::Model> {
        let mut announcement: announcements::ActiveModel = announcements::ActiveModel {
            id: Set(announcement_id),
            is_pinned: Set(false),
            ..Default::default()
        };
        
        let announcement = announcement.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(announcement)
    }

    pub async fn set_popup(&self, announcement_id: Uuid, is_popup: bool) -> RepositoryResult<announcements::Model> {
        let mut announcement: announcements::ActiveModel = announcements::ActiveModel {
            id: Set(announcement_id),
            is_popup: Set(is_popup),
            ..Default::default()
        };
        
        let announcement = announcement.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(announcement)
    }

    pub async fn update_expiry(&self, announcement_id: Uuid, expires_at: Option<chrono::DateTime<chrono::FixedOffset>>) -> RepositoryResult<announcements::Model> {
        let mut announcement: announcements::ActiveModel = announcements::ActiveModel {
            id: Set(announcement_id),
            expires_at: Set(expires_at),
            ..Default::default()
        };
        
        let announcement = announcement.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(announcement)
    }

    pub async fn count_active_announcements(&self) -> RepositoryResult<u64> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let count = Announcements::find()
            .filter(announcements::Column::IsActive.eq(true))
            .filter(announcements::Column::PublishAt.lte(now))
            .filter(
                announcements::Column::ExpiresAt.is_null()
                    .or(announcements::Column::ExpiresAt.gt(now))
            )
            .count(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(count)
    }
}

#[async_trait::async_trait]
impl Repository<Announcements, announcements::Model, announcements::ActiveModel> for AnnouncementRepository {
    async fn create(&self, model: announcements::ActiveModel) -> Result<announcements::Model, DbErr> {
        let announcement = model.insert(self.db.as_ref()).await?;
        Ok(announcement)
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<announcements::Model>, DbErr> {
        let announcement = Announcements::find_by_id(id).one(self.db.as_ref()).await?;
        Ok(announcement)
    }

    async fn find_all(&self) -> Result<Vec<announcements::Model>, DbErr> {
        let announcements = Announcements::find()
            .order_by_desc(announcements::Column::CreatedAt)
            .all(self.db.as_ref())
            .await?;
        Ok(announcements)
    }

    async fn update(&self, id: Uuid, model: announcements::ActiveModel) -> Result<announcements::Model, DbErr> {
        let mut model = model;
        model.id = Set(id);
        let announcement = model.update(self.db.as_ref()).await?;
        Ok(announcement)
    }

    async fn delete(&self, id: Uuid) -> Result<(), DbErr> {
        Announcements::delete_by_id(id).exec(self.db.as_ref()).await?;
        Ok(())
    }

    async fn count(&self) -> Result<u64, DbErr> {
        let count = Announcements::find().count(self.db.as_ref()).await?;
        Ok(count)
    }
}

#[async_trait::async_trait]
impl PaginatedRepository<Announcements, announcements::Model> for AnnouncementRepository {
    async fn find_with_pagination(&self, params: PaginationParams) -> Result<PaginatedResult<announcements::Model>, DbErr> {
        let query = Announcements::find()
            .order_by_desc(announcements::Column::IsPinned)
            .order_by_desc(announcements::Column::PublishAt);
        self.paginate_query(query, params).await
    }

    async fn find_with_pagination_and_filter(&self, params: PaginationParams, filter: sea_orm::Condition) -> Result<PaginatedResult<announcements::Model>, DbErr> {
        let query = Announcements::find()
            .filter(filter)
            .order_by_desc(announcements::Column::IsPinned)
            .order_by_desc(announcements::Column::PublishAt);
        self.paginate_query(query, params).await
    }
}

impl PaginationHelper<Announcements, announcements::Model> for AnnouncementRepository {
    fn db(&self) -> &DatabaseConnection {
        self.db.as_ref()
    }
}