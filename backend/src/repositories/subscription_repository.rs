use sea_orm::prelude::*;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, DbErr, EntityTrait, <PERSON><PERSON><PERSON><PERSON>, QueryFilter, QueryOrder,
    RelationTrait, Set,
};
use std::sync::Arc;

use super::{
    PaginatedRepository, PaginatedResult, PaginationHelper, PaginationParams, Repository,
    RepositoryError, RepositoryResult,
};
use crate::entities::{enums::SubscriptionStatus, plans, prelude::*, subscriptions, users};

#[derive(Clone)]
pub struct SubscriptionRepository {
    db: Arc<DatabaseConnection>,
}

impl SubscriptionRepository {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self { db }
    }

    pub async fn find_by_user_id(
        &self,
        user_id: Uuid,
    ) -> RepositoryResult<Vec<subscriptions::Model>> {
        let subscriptions = Subscriptions::find()
            .filter(subscriptions::Column::UserId.eq(user_id))
            .order_by_desc(subscriptions::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(subscriptions)
    }

    pub async fn find_active_by_user_id(
        &self,
        user_id: Uuid,
    ) -> RepositoryResult<Vec<subscriptions::Model>> {
        let subscriptions = Subscriptions::find()
            .filter(subscriptions::Column::UserId.eq(user_id))
            .filter(subscriptions::Column::Status.eq(SubscriptionStatus::Active))
            .order_by_desc(subscriptions::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(subscriptions)
    }

    pub async fn find_by_plan_id(
        &self,
        plan_id: Uuid,
    ) -> RepositoryResult<Vec<subscriptions::Model>> {
        let subscriptions = Subscriptions::find()
            .filter(subscriptions::Column::PlanId.eq(plan_id))
            .order_by_desc(subscriptions::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(subscriptions)
    }

    pub async fn find_by_status(
        &self,
        status: SubscriptionStatus,
    ) -> RepositoryResult<Vec<subscriptions::Model>> {
        let subscriptions = Subscriptions::find()
            .filter(subscriptions::Column::Status.eq(status))
            .order_by_desc(subscriptions::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(subscriptions)
    }

    pub async fn find_expiring_soon(
        &self,
        days: i32,
    ) -> RepositoryResult<Vec<subscriptions::Model>> {
        let expiry_date = chrono::Utc::now()
            .with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())
            + chrono::Duration::days(days as i64);

        let subscriptions = Subscriptions::find()
            .filter(subscriptions::Column::Status.eq(SubscriptionStatus::Active))
            .filter(subscriptions::Column::EndDate.lte(expiry_date))
            .order_by_asc(subscriptions::Column::EndDate)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(subscriptions)
    }

    pub async fn find_expired(&self) -> RepositoryResult<Vec<subscriptions::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());

        let subscriptions = Subscriptions::find()
            .filter(subscriptions::Column::Status.eq(SubscriptionStatus::Active))
            .filter(subscriptions::Column::EndDate.lt(now))
            .order_by_asc(subscriptions::Column::EndDate)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(subscriptions)
    }

    pub async fn find_active_subscriptions(&self) -> RepositoryResult<Vec<subscriptions::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());

        let subscriptions = Subscriptions::find()
            .filter(subscriptions::Column::Status.eq(SubscriptionStatus::Active))
            .filter(subscriptions::Column::EndDate.gt(now))
            .order_by_asc(subscriptions::Column::EndDate)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(subscriptions)
    }

    pub async fn find_with_user_and_plan(
        &self,
        subscription_id: Uuid,
    ) -> RepositoryResult<
        Option<(
            subscriptions::Model,
            Option<users::Model>,
            Option<plans::Model>,
        )>,
    > {
        let result = Subscriptions::find_by_id(subscription_id)
            .find_also_related(Users)
            .find_also_related(Plans)
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(result)
    }

    pub async fn update_traffic_usage(
        &self,
        subscription_id: Uuid,
        used_traffic_gb: rust_decimal::Decimal,
    ) -> RepositoryResult<subscriptions::Model> {
        let mut subscription: subscriptions::ActiveModel = subscriptions::ActiveModel {
            id: Set(subscription_id),
            used_traffic_gb: Set(used_traffic_gb),
            last_traffic_sync: Set(
                chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())
            ),
            ..Default::default()
        };

        let subscription = subscription
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(subscription)
    }

    pub async fn update_device_count(
        &self,
        subscription_id: Uuid,
        device_count: i32,
    ) -> RepositoryResult<subscriptions::Model> {
        let mut subscription: subscriptions::ActiveModel = subscriptions::ActiveModel {
            id: Set(subscription_id),
            current_devices_count: Set(device_count),
            ..Default::default()
        };

        let subscription = subscription
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(subscription)
    }

    pub async fn update_status(
        &self,
        subscription_id: Uuid,
        status: SubscriptionStatus,
    ) -> RepositoryResult<subscriptions::Model> {
        let mut subscription: subscriptions::ActiveModel = subscriptions::ActiveModel {
            id: Set(subscription_id),
            status: Set(status),
            ..Default::default()
        };

        let subscription = subscription
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(subscription)
    }

    pub async fn update_connection_stats(
        &self,
        subscription_id: Uuid,
        total_connections: i64,
        total_session_time: i64,
    ) -> RepositoryResult<subscriptions::Model> {
        let mut subscription: subscriptions::ActiveModel = subscriptions::ActiveModel {
            id: Set(subscription_id),
            total_connections_count: Set(total_connections),
            total_session_time: Set(total_session_time),
            ..Default::default()
        };

        let subscription = subscription
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(subscription)
    }

    pub async fn set_auto_renew(
        &self,
        subscription_id: Uuid,
        auto_renew: bool,
        auto_renew_plan_id: Option<Uuid>,
    ) -> RepositoryResult<subscriptions::Model> {
        let mut subscription: subscriptions::ActiveModel = subscriptions::ActiveModel {
            id: Set(subscription_id),
            auto_renew: Set(auto_renew),
            auto_renew_plan_id: Set(auto_renew_plan_id),
            ..Default::default()
        };

        let subscription = subscription
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(subscription)
    }

    pub async fn extend_subscription(
        &self,
        subscription_id: Uuid,
        additional_days: i32,
    ) -> RepositoryResult<subscriptions::Model> {
        let subscription = self
            .find_by_id(subscription_id)
            .await?
            .ok_or(RepositoryError::NotFound)?;

        let new_end_date = subscription.end_date + chrono::Duration::days(additional_days as i64);

        let mut subscription_active: subscriptions::ActiveModel = subscriptions::ActiveModel {
            id: Set(subscription_id),
            end_date: Set(new_end_date),
            status: Set(SubscriptionStatus::Active),
            ..Default::default()
        };

        let subscription = subscription_active
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(subscription)
    }

    pub async fn find_by_proxy_credentials(
        &self,
        username: &str,
        password: &str,
    ) -> RepositoryResult<Option<subscriptions::Model>> {
        let subscription = Subscriptions::find()
            .filter(subscriptions::Column::ProxyUsername.eq(username))
            .filter(subscriptions::Column::ProxyPassword.eq(password))
            .filter(subscriptions::Column::Status.eq(SubscriptionStatus::Active))
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(subscription)
    }

    pub async fn update_proxy_config(
        &self,
        subscription_id: Uuid,
        config_hash: &str,
    ) -> RepositoryResult<subscriptions::Model> {
        let mut subscription: subscriptions::ActiveModel = subscriptions::ActiveModel {
            id: Set(subscription_id),
            proxy_config_hash: Set(Some(config_hash.to_string())),
            ..Default::default()
        };

        let subscription = subscription
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(subscription)
    }

    pub async fn count_active_subscriptions(&self) -> RepositoryResult<u64> {
        let count = Subscriptions::find()
            .filter(subscriptions::Column::Status.eq(SubscriptionStatus::Active))
            .count(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(count)
    }

    pub async fn count_by_plan(&self, plan_id: Uuid) -> RepositoryResult<u64> {
        let count = Subscriptions::find()
            .filter(subscriptions::Column::PlanId.eq(plan_id))
            .filter(subscriptions::Column::Status.eq(SubscriptionStatus::Active))
            .count(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(count)
    }
}

#[async_trait::async_trait]
impl Repository<Subscriptions, subscriptions::Model, subscriptions::ActiveModel>
    for SubscriptionRepository
{
    async fn create(
        &self,
        model: subscriptions::ActiveModel,
    ) -> Result<subscriptions::Model, DbErr> {
        let subscription = model.insert(self.db.as_ref()).await?;
        Ok(subscription)
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<subscriptions::Model>, DbErr> {
        let subscription = Subscriptions::find_by_id(id).one(self.db.as_ref()).await?;
        Ok(subscription)
    }

    async fn find_all(&self) -> Result<Vec<subscriptions::Model>, DbErr> {
        let subscriptions = Subscriptions::find()
            .order_by_desc(subscriptions::Column::CreatedAt)
            .all(self.db.as_ref())
            .await?;
        Ok(subscriptions)
    }

    async fn update(
        &self,
        id: Uuid,
        model: subscriptions::ActiveModel,
    ) -> Result<subscriptions::Model, DbErr> {
        let mut model = model;
        model.id = Set(id);
        let subscription = model.update(self.db.as_ref()).await?;
        Ok(subscription)
    }

    async fn delete(&self, id: Uuid) -> Result<(), DbErr> {
        Subscriptions::delete_by_id(id)
            .exec(self.db.as_ref())
            .await?;
        Ok(())
    }

    async fn count(&self) -> Result<u64, DbErr> {
        let count = Subscriptions::find().count(self.db.as_ref()).await?;
        Ok(count)
    }
}

#[async_trait::async_trait]
impl PaginatedRepository<Subscriptions, subscriptions::Model> for SubscriptionRepository {
    async fn find_with_pagination(
        &self,
        params: PaginationParams,
    ) -> Result<PaginatedResult<subscriptions::Model>, DbErr> {
        let query = Subscriptions::find().order_by_desc(subscriptions::Column::CreatedAt);
        self.paginate_query(query, params).await
    }

    async fn find_with_pagination_and_filter(
        &self,
        params: PaginationParams,
        filter: sea_orm::Condition,
    ) -> Result<PaginatedResult<subscriptions::Model>, DbErr> {
        let query = Subscriptions::find()
            .filter(filter)
            .order_by_desc(subscriptions::Column::CreatedAt);
        self.paginate_query(query, params).await
    }
}

impl PaginationHelper<Subscriptions, subscriptions::Model> for SubscriptionRepository {
    fn db(&self) -> &DatabaseConnection {
        self.db.as_ref()
    }
}
