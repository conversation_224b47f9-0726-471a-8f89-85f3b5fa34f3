use sea_orm::{DatabaseConnection, DbErr, EntityTrait, QueryFilter, QueryOrder, ActiveModelTrait, Set, JoinType, RelationTrait};
use sea_orm::prelude::*;
use std::sync::Arc;
use sha2::{Sha256, Digest};

use crate::entities::{api_tokens, users, prelude::*};
use super::{Repository, PaginatedRepository, PaginationParams, PaginatedResult, PaginationHelper, RepositoryError, RepositoryResult};

#[derive(Clone)]
pub struct ApiTokenRepository {
    db: Arc<DatabaseConnection>,
}

impl ApiTokenRepository {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self { db }
    }

    pub async fn find_by_user_id(&self, user_id: Uuid) -> RepositoryResult<Vec<api_tokens::Model>> {
        let tokens = ApiTokens::find()
            .filter(api_tokens::Column::UserId.eq(user_id))
            .order_by_desc(api_tokens::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(tokens)
    }

    pub async fn find_by_token_hash(&self, token_hash: &str) -> RepositoryResult<Option<api_tokens::Model>> {
        let token = ApiTokens::find()
            .filter(api_tokens::Column::TokenHash.eq(token_hash))
            .filter(api_tokens::Column::IsActive.eq(true))
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(token)
    }

    pub async fn find_active_tokens(&self) -> RepositoryResult<Vec<api_tokens::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let tokens = ApiTokens::find()
            .filter(api_tokens::Column::IsActive.eq(true))
            .filter(
                api_tokens::Column::ExpiresAt.is_null()
                    .or(api_tokens::Column::ExpiresAt.gt(now))
            )
            .order_by_desc(api_tokens::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(tokens)
    }

    pub async fn find_expired_tokens(&self) -> RepositoryResult<Vec<api_tokens::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let tokens = ApiTokens::find()
            .filter(api_tokens::Column::ExpiresAt.is_not_null())
            .filter(api_tokens::Column::ExpiresAt.lt(now))
            .filter(api_tokens::Column::IsActive.eq(true))
            .order_by_asc(api_tokens::Column::ExpiresAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(tokens)
    }

    pub async fn find_with_user(&self, token_id: Uuid) -> RepositoryResult<Option<(api_tokens::Model, Option<users::Model>)>> {
        let result = ApiTokens::find_by_id(token_id)
            .find_also_related(Users)
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(result)
    }

    pub async fn create_token(&self, user_id: Uuid, name: &str, permissions: serde_json::Value, expires_at: Option<chrono::DateTime<chrono::FixedOffset>>) -> RepositoryResult<(api_tokens::Model, String)> {
        // Generate a random token
        let token = self.generate_token();
        let token_hash = self.hash_token(&token);
        
        let token_model = api_tokens::ActiveModel {
            user_id: Set(user_id),
            token_hash: Set(token_hash),
            name: Set(name.to_string()),
            permissions: Set(Some(permissions)),
            expires_at: Set(expires_at),
            is_active: Set(true),
            ..Default::default()
        };
        
        let saved_token = token_model.insert(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        
        Ok((saved_token, token))
    }

    pub async fn validate_token(&self, token: &str) -> RepositoryResult<Option<api_tokens::Model>> {
        let token_hash = self.hash_token(token);
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let token_model = ApiTokens::find()
            .filter(api_tokens::Column::TokenHash.eq(&token_hash))
            .filter(api_tokens::Column::IsActive.eq(true))
            .filter(
                api_tokens::Column::ExpiresAt.is_null()
                    .or(api_tokens::Column::ExpiresAt.gt(now))
            )
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(token_model)
    }

    pub async fn update_last_used(&self, token_id: Uuid, ip_address: &str) -> RepositoryResult<api_tokens::Model> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let mut token: api_tokens::ActiveModel = api_tokens::ActiveModel {
            id: Set(token_id),
            last_used_at: Set(Some(now)),
            last_used_ip: Set(Some(ip_address.to_string())),
            ..Default::default()
        };
        
        let token = token.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(token)
    }

    pub async fn deactivate_token(&self, token_id: Uuid) -> RepositoryResult<api_tokens::Model> {
        let mut token: api_tokens::ActiveModel = api_tokens::ActiveModel {
            id: Set(token_id),
            is_active: Set(false),
            ..Default::default()
        };
        
        let token = token.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(token)
    }

    pub async fn reactivate_token(&self, token_id: Uuid) -> RepositoryResult<api_tokens::Model> {
        let mut token: api_tokens::ActiveModel = api_tokens::ActiveModel {
            id: Set(token_id),
            is_active: Set(true),
            ..Default::default()
        };
        
        let token = token.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(token)
    }

    pub async fn update_permissions(&self, token_id: Uuid, permissions: serde_json::Value) -> RepositoryResult<api_tokens::Model> {
        let mut token: api_tokens::ActiveModel = api_tokens::ActiveModel {
            id: Set(token_id),
            permissions: Set(Some(permissions)),
            ..Default::default()
        };
        
        let token = token.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(token)
    }

    pub async fn extend_expiry(&self, token_id: Uuid, new_expiry: chrono::DateTime<chrono::FixedOffset>) -> RepositoryResult<api_tokens::Model> {
        let mut token: api_tokens::ActiveModel = api_tokens::ActiveModel {
            id: Set(token_id),
            expires_at: Set(Some(new_expiry)),
            ..Default::default()
        };
        
        let token = token.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(token)
    }

    pub async fn revoke_all_user_tokens(&self, user_id: Uuid) -> RepositoryResult<u64> {
        let result = ApiTokens::update_many()
            .filter(api_tokens::Column::UserId.eq(user_id))
            .col_expr(api_tokens::Column::IsActive, sea_orm::prelude::Expr::value(false))
            .exec(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(result.rows_affected)
    }

    pub async fn cleanup_expired_tokens(&self) -> RepositoryResult<u64> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let result = ApiTokens::update_many()
            .filter(api_tokens::Column::ExpiresAt.is_not_null())
            .filter(api_tokens::Column::ExpiresAt.lt(now))
            .col_expr(api_tokens::Column::IsActive, sea_orm::prelude::Expr::value(false))
            .exec(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(result.rows_affected)
    }

    pub async fn count_active_tokens_by_user(&self, user_id: Uuid) -> RepositoryResult<u64> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let count = ApiTokens::find()
            .filter(api_tokens::Column::UserId.eq(user_id))
            .filter(api_tokens::Column::IsActive.eq(true))
            .filter(
                api_tokens::Column::ExpiresAt.is_null()
                    .or(api_tokens::Column::ExpiresAt.gt(now))
            )
            .count(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(count)
    }

    pub async fn get_token_usage_stats(&self, token_id: Uuid) -> RepositoryResult<Option<(chrono::DateTime<chrono::FixedOffset>, Option<chrono::DateTime<chrono::FixedOffset>>, Option<String>)>> {
        let token = ApiTokens::find_by_id(token_id)
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        if let Some(token) = token {
            Ok(Some((token.created_at, token.last_used_at, token.last_used_ip)))
        } else {
            Ok(None)
        }
    }

    pub async fn check_permission(&self, token_id: Uuid, permission: &str) -> RepositoryResult<bool> {
        let token = ApiTokens::find_by_id(token_id)
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        if let Some(token) = token {
            if let Some(permissions) = token.permissions {
                if let Ok(perms) = serde_json::from_value::<Vec<String>>(permissions) {
                    return Ok(perms.contains(&permission.to_string()) || perms.contains(&"*".to_string()));
                }
            }
        }
        
        Ok(false)
    }

    fn generate_token(&self) -> String {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        let token_bytes: [u8; 32] = rng.gen();
        hex::encode(token_bytes)
    }

    fn hash_token(&self, token: &str) -> String {
        let mut hasher = Sha256::new();
        hasher.update(token.as_bytes());
        hex::encode(hasher.finalize())
    }
}

#[async_trait::async_trait]
impl Repository<ApiTokens, api_tokens::Model, api_tokens::ActiveModel> for ApiTokenRepository {
    async fn create(&self, model: api_tokens::ActiveModel) -> Result<api_tokens::Model, DbErr> {
        let token = model.insert(self.db.as_ref()).await?;
        Ok(token)
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<api_tokens::Model>, DbErr> {
        let token = ApiTokens::find_by_id(id).one(self.db.as_ref()).await?;
        Ok(token)
    }

    async fn find_all(&self) -> Result<Vec<api_tokens::Model>, DbErr> {
        let tokens = ApiTokens::find()
            .order_by_desc(api_tokens::Column::CreatedAt)
            .all(self.db.as_ref())
            .await?;
        Ok(tokens)
    }

    async fn update(&self, id: Uuid, model: api_tokens::ActiveModel) -> Result<api_tokens::Model, DbErr> {
        let mut model = model;
        model.id = Set(id);
        let token = model.update(self.db.as_ref()).await?;
        Ok(token)
    }

    async fn delete(&self, id: Uuid) -> Result<(), DbErr> {
        ApiTokens::delete_by_id(id).exec(self.db.as_ref()).await?;
        Ok(())
    }

    async fn count(&self) -> Result<u64, DbErr> {
        let count = ApiTokens::find().count(self.db.as_ref()).await?;
        Ok(count)
    }
}

#[async_trait::async_trait]
impl PaginatedRepository<ApiTokens, api_tokens::Model> for ApiTokenRepository {
    async fn find_with_pagination(&self, params: PaginationParams) -> Result<PaginatedResult<api_tokens::Model>, DbErr> {
        let query = ApiTokens::find().order_by_desc(api_tokens::Column::CreatedAt);
        self.paginate_query(query, params).await
    }

    async fn find_with_pagination_and_filter(&self, params: PaginationParams, filter: sea_orm::Condition) -> Result<PaginatedResult<api_tokens::Model>, DbErr> {
        let query = ApiTokens::find()
            .filter(filter)
            .order_by_desc(api_tokens::Column::CreatedAt);
        self.paginate_query(query, params).await
    }
}

impl PaginationHelper<ApiTokens, api_tokens::Model> for ApiTokenRepository {
    fn db(&self) -> &DatabaseConnection {
        self.db.as_ref()
    }
}