use sea_orm::prelude::*;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, DbErr, EntityTrait, JoinType, QueryFilter, QueryOrder,
    QuerySelect, RelationTrait, Set,
};
use std::sync::Arc;

use super::{
    PaginatedRepository, PaginatedResult, PaginationHelper, PaginationParams, Repository,
    RepositoryError, RepositoryResult,
};
use crate::entities::{connection_logs, prelude::*, subscriptions, users};

#[derive(Clone)]
pub struct ConnectionLogRepository {
    db: Arc<DatabaseConnection>,
}

impl ConnectionLogRepository {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self { db }
    }

    pub async fn find_by_user_id(
        &self,
        user_id: Uuid,
    ) -> RepositoryResult<Vec<connection_logs::Model>> {
        let logs = ConnectionLogs::find()
            .filter(connection_logs::Column::UserId.eq(user_id))
            .order_by_desc(connection_logs::Column::ConnectedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(logs)
    }

    pub async fn find_by_subscription_id(
        &self,
        subscription_id: Uuid,
    ) -> RepositoryResult<Vec<connection_logs::Model>> {
        let logs = ConnectionLogs::find()
            .filter(connection_logs::Column::SubscriptionId.eq(subscription_id))
            .order_by_desc(connection_logs::Column::ConnectedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(logs)
    }

    pub async fn find_by_session_id(
        &self,
        session_id: &str,
    ) -> RepositoryResult<Option<connection_logs::Model>> {
        let log = ConnectionLogs::find()
            .filter(connection_logs::Column::SessionId.eq(session_id))
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(log)
    }

    pub async fn find_active_connections(&self) -> RepositoryResult<Vec<connection_logs::Model>> {
        let logs = ConnectionLogs::find()
            .filter(connection_logs::Column::DisconnectedAt.is_null())
            .order_by_desc(connection_logs::Column::ConnectedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(logs)
    }

    pub async fn find_active_connections_by_user(
        &self,
        user_id: Uuid,
    ) -> RepositoryResult<Vec<connection_logs::Model>> {
        let logs = ConnectionLogs::find()
            .filter(connection_logs::Column::UserId.eq(user_id))
            .filter(connection_logs::Column::DisconnectedAt.is_null())
            .order_by_desc(connection_logs::Column::ConnectedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(logs)
    }

    pub async fn find_by_client_ip(
        &self,
        client_ip: &str,
    ) -> RepositoryResult<Vec<connection_logs::Model>> {
        let logs = ConnectionLogs::find()
            .filter(connection_logs::Column::ClientIp.eq(client_ip))
            .order_by_desc(connection_logs::Column::ConnectedAt)
            .limit(50) // Limit to prevent huge result sets
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(logs)
    }

    pub async fn find_by_country(
        &self,
        country_code: &str,
    ) -> RepositoryResult<Vec<connection_logs::Model>> {
        let logs = ConnectionLogs::find()
            .filter(connection_logs::Column::CountryCode.eq(country_code))
            .order_by_desc(connection_logs::Column::ConnectedAt)
            .limit(100) // Limit to prevent huge result sets
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(logs)
    }

    pub async fn find_by_date_range(
        &self,
        start_date: chrono::DateTime<chrono::FixedOffset>,
        end_date: chrono::DateTime<chrono::FixedOffset>,
    ) -> RepositoryResult<Vec<connection_logs::Model>> {
        let logs = ConnectionLogs::find()
            .filter(connection_logs::Column::ConnectedAt.gte(start_date))
            .filter(connection_logs::Column::ConnectedAt.lte(end_date))
            .order_by_desc(connection_logs::Column::ConnectedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(logs)
    }

    pub async fn find_with_user_and_subscription(
        &self,
        log_id: Uuid,
    ) -> RepositoryResult<
        Option<(
            connection_logs::Model,
            Option<users::Model>,
            Option<subscriptions::Model>,
        )>,
    > {
        let result = ConnectionLogs::find_by_id(log_id)
            .find_also_related(Users)
            .find_also_related(Subscriptions)
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(result)
    }

    pub async fn disconnect_session(
        &self,
        session_id: &str,
        disconnect_reason: Option<String>,
    ) -> RepositoryResult<connection_logs::Model> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());

        // Find the connection log by session_id
        let connection_log = ConnectionLogs::find()
            .filter(connection_logs::Column::SessionId.eq(session_id))
            .filter(connection_logs::Column::DisconnectedAt.is_null())
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?
            .ok_or(RepositoryError::NotFound)?;

        // Calculate duration
        let duration_seconds = (now
            .signed_duration_since(connection_log.connected_at)
            .num_seconds()) as i32;

        let mut log: connection_logs::ActiveModel = connection_logs::ActiveModel {
            id: Set(connection_log.id),
            disconnected_at: Set(Some(now)),
            duration_seconds: Set(Some(duration_seconds)),
            disconnect_reason: Set(disconnect_reason),
            ..Default::default()
        };

        let log = log
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(log)
    }

    pub async fn update_traffic_stats(
        &self,
        session_id: &str,
        bytes_uploaded: i64,
        bytes_downloaded: i64,
    ) -> RepositoryResult<connection_logs::Model> {
        let connection_log = ConnectionLogs::find()
            .filter(connection_logs::Column::SessionId.eq(session_id))
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?
            .ok_or(RepositoryError::NotFound)?;

        let mut log: connection_logs::ActiveModel = connection_logs::ActiveModel {
            id: Set(connection_log.id),
            bytes_uploaded: Set(bytes_uploaded),
            bytes_downloaded: Set(bytes_downloaded),
            ..Default::default()
        };

        let log = log
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(log)
    }

    pub async fn calculate_user_traffic(
        &self,
        user_id: Uuid,
        start_date: chrono::DateTime<chrono::FixedOffset>,
        end_date: chrono::DateTime<chrono::FixedOffset>,
    ) -> RepositoryResult<(i64, i64)> {
        let logs = ConnectionLogs::find()
            .filter(connection_logs::Column::UserId.eq(user_id))
            .filter(connection_logs::Column::ConnectedAt.gte(start_date))
            .filter(connection_logs::Column::ConnectedAt.lte(end_date))
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        let total_uploaded = logs.iter().map(|log| log.bytes_uploaded).sum();
        let total_downloaded = logs.iter().map(|log| log.bytes_downloaded).sum();

        Ok((total_uploaded, total_downloaded))
    }

    pub async fn calculate_subscription_traffic(
        &self,
        subscription_id: Uuid,
    ) -> RepositoryResult<(i64, i64)> {
        let logs = ConnectionLogs::find()
            .filter(connection_logs::Column::SubscriptionId.eq(subscription_id))
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        let total_uploaded = logs.iter().map(|log| log.bytes_uploaded).sum();
        let total_downloaded = logs.iter().map(|log| log.bytes_downloaded).sum();

        Ok((total_uploaded, total_downloaded))
    }

    pub async fn count_active_connections_by_user(&self, user_id: Uuid) -> RepositoryResult<u64> {
        let count = ConnectionLogs::find()
            .filter(connection_logs::Column::UserId.eq(user_id))
            .filter(connection_logs::Column::DisconnectedAt.is_null())
            .count(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(count)
    }

    pub async fn count_total_active_connections(&self) -> RepositoryResult<u64> {
        let count = ConnectionLogs::find()
            .filter(connection_logs::Column::DisconnectedAt.is_null())
            .count(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(count)
    }

    pub async fn get_top_countries(&self, limit: u64) -> RepositoryResult<Vec<(String, u64)>> {
        let logs = ConnectionLogs::find()
            .filter(connection_logs::Column::CountryCode.is_not_null())
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        let mut country_counts = std::collections::HashMap::new();
        for log in logs {
            if let Some(country) = log.country_code {
                *country_counts.entry(country).or_insert(0) += 1;
            }
        }

        let mut sorted_countries: Vec<(String, u64)> = country_counts.into_iter().collect();
        sorted_countries.sort_by(|a, b| b.1.cmp(&a.1));
        sorted_countries.truncate(limit as usize);

        Ok(sorted_countries)
    }

    pub async fn cleanup_old_logs(&self, days_to_keep: i32) -> RepositoryResult<u64> {
        let cutoff_date = chrono::Utc::now()
            .with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())
            - chrono::Duration::days(days_to_keep as i64);

        let result = ConnectionLogs::delete_many()
            .filter(connection_logs::Column::ConnectedAt.lt(cutoff_date))
            .exec(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(result.rows_affected)
    }
}

#[async_trait::async_trait]
impl Repository<ConnectionLogs, connection_logs::Model, connection_logs::ActiveModel>
    for ConnectionLogRepository
{
    async fn create(
        &self,
        model: connection_logs::ActiveModel,
    ) -> Result<connection_logs::Model, DbErr> {
        let log = model.insert(self.db.as_ref()).await?;
        Ok(log)
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<connection_logs::Model>, DbErr> {
        let log = ConnectionLogs::find_by_id(id).one(self.db.as_ref()).await?;
        Ok(log)
    }

    async fn find_all(&self) -> Result<Vec<connection_logs::Model>, DbErr> {
        let logs = ConnectionLogs::find()
            .order_by_desc(connection_logs::Column::ConnectedAt)
            .all(self.db.as_ref())
            .await?;
        Ok(logs)
    }

    async fn update(
        &self,
        id: Uuid,
        model: connection_logs::ActiveModel,
    ) -> Result<connection_logs::Model, DbErr> {
        let mut model = model;
        model.id = Set(id);
        let log = model.update(self.db.as_ref()).await?;
        Ok(log)
    }

    async fn delete(&self, id: Uuid) -> Result<(), DbErr> {
        ConnectionLogs::delete_by_id(id)
            .exec(self.db.as_ref())
            .await?;
        Ok(())
    }

    async fn count(&self) -> Result<u64, DbErr> {
        let count = ConnectionLogs::find().count(self.db.as_ref()).await?;
        Ok(count)
    }
}

#[async_trait::async_trait]
impl PaginatedRepository<ConnectionLogs, connection_logs::Model> for ConnectionLogRepository {
    async fn find_with_pagination(
        &self,
        params: PaginationParams,
    ) -> Result<PaginatedResult<connection_logs::Model>, DbErr> {
        let query = ConnectionLogs::find().order_by_desc(connection_logs::Column::ConnectedAt);
        self.paginate_query(query, params).await
    }

    async fn find_with_pagination_and_filter(
        &self,
        params: PaginationParams,
        filter: sea_orm::Condition,
    ) -> Result<PaginatedResult<connection_logs::Model>, DbErr> {
        let query = ConnectionLogs::find()
            .filter(filter)
            .order_by_desc(connection_logs::Column::ConnectedAt);
        self.paginate_query(query, params).await
    }
}

impl PaginationHelper<ConnectionLogs, connection_logs::Model> for ConnectionLogRepository {
    fn db(&self) -> &DatabaseConnection {
        self.db.as_ref()
    }
}
