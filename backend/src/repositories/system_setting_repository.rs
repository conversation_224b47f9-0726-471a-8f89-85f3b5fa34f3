use sea_orm::{DatabaseConnection, DbErr, <PERSON><PERSON><PERSON>T<PERSON>t, QueryFilter, QueryOrder, ActiveModelTrait, Set};
use sea_orm::prelude::*;
use std::sync::Arc;

use crate::entities::{system_settings, prelude::*};
use super::{Repository, PaginatedRepository, PaginationParams, PaginatedResult, PaginationHelper, RepositoryError, RepositoryResult};

#[derive(Clone)]
pub struct SystemSettingRepository {
    db: Arc<DatabaseConnection>,
}

impl SystemSettingRepository {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self { db }
    }

    pub async fn find_by_key(&self, key: &str) -> RepositoryResult<Option<system_settings::Model>> {
        let setting = SystemSettings::find()
            .filter(system_settings::Column::Key.eq(key))
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(setting)
    }

    pub async fn find_public_settings(&self) -> RepositoryResult<Vec<system_settings::Model>> {
        let settings = SystemSettings::find()
            .filter(system_settings::Column::IsPublic.eq(true))
            .order_by_asc(system_settings::Column::Key)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(settings)
    }

    pub async fn find_private_settings(&self) -> RepositoryResult<Vec<system_settings::Model>> {
        let settings = SystemSettings::find()
            .filter(system_settings::Column::IsPublic.eq(false))
            .order_by_asc(system_settings::Column::Key)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(settings)
    }

    pub async fn find_by_value_type(&self, value_type: system_settings::SettingValueType) -> RepositoryResult<Vec<system_settings::Model>> {
        let settings = SystemSettings::find()
            .filter(system_settings::Column::ValueType.eq(value_type))
            .order_by_asc(system_settings::Column::Key)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(settings)
    }

    pub async fn get_string_value(&self, key: &str) -> RepositoryResult<Option<String>> {
        let setting = self.find_by_key(key).await?;
        Ok(setting.and_then(|s| s.value))
    }

    pub async fn get_integer_value(&self, key: &str) -> RepositoryResult<Option<i64>> {
        let setting = self.find_by_key(key).await?;
        match setting {
            Some(s) => {
                if let Some(value) = s.value {
                    match value.parse::<i64>() {
                        Ok(int_value) => Ok(Some(int_value)),
                        Err(_) => Err(RepositoryError::Validation(format!("Invalid integer value for key: {}", key))),
                    }
                } else {
                    Ok(None)
                }
            }
            None => Ok(None),
        }
    }

    pub async fn get_decimal_value(&self, key: &str) -> RepositoryResult<Option<rust_decimal::Decimal>> {
        let setting = self.find_by_key(key).await?;
        match setting {
            Some(s) => {
                if let Some(value) = s.value {
                    match value.parse::<rust_decimal::Decimal>() {
                        Ok(decimal_value) => Ok(Some(decimal_value)),
                        Err(_) => Err(RepositoryError::Validation(format!("Invalid decimal value for key: {}", key))),
                    }
                } else {
                    Ok(None)
                }
            }
            None => Ok(None),
        }
    }

    pub async fn get_boolean_value(&self, key: &str) -> RepositoryResult<Option<bool>> {
        let setting = self.find_by_key(key).await?;
        match setting {
            Some(s) => {
                if let Some(value) = s.value {
                    match value.to_lowercase().as_str() {
                        "true" | "1" | "yes" | "on" => Ok(Some(true)),
                        "false" | "0" | "no" | "off" => Ok(Some(false)),
                        _ => Err(RepositoryError::Validation(format!("Invalid boolean value for key: {}", key))),
                    }
                } else {
                    Ok(None)
                }
            }
            None => Ok(None),
        }
    }

    pub async fn get_json_value(&self, key: &str) -> RepositoryResult<Option<serde_json::Value>> {
        let setting = self.find_by_key(key).await?;
        match setting {
            Some(s) => {
                if let Some(value) = s.value {
                    match serde_json::from_str(&value) {
                        Ok(json_value) => Ok(Some(json_value)),
                        Err(_) => Err(RepositoryError::Validation(format!("Invalid JSON value for key: {}", key))),
                    }
                } else {
                    Ok(None)
                }
            }
            None => Ok(None),
        }
    }

    pub async fn set_string_value(&self, key: &str, value: &str, description: Option<&str>, is_public: bool) -> RepositoryResult<system_settings::Model> {
        self.upsert_setting(key, Some(value.to_string()), system_settings::SettingValueType::String, description, is_public).await
    }

    pub async fn set_integer_value(&self, key: &str, value: i64, description: Option<&str>, is_public: bool) -> RepositoryResult<system_settings::Model> {
        self.upsert_setting(key, Some(value.to_string()), system_settings::SettingValueType::Integer, description, is_public).await
    }

    pub async fn set_decimal_value(&self, key: &str, value: rust_decimal::Decimal, description: Option<&str>, is_public: bool) -> RepositoryResult<system_settings::Model> {
        self.upsert_setting(key, Some(value.to_string()), system_settings::SettingValueType::Decimal, description, is_public).await
    }

    pub async fn set_boolean_value(&self, key: &str, value: bool, description: Option<&str>, is_public: bool) -> RepositoryResult<system_settings::Model> {
        self.upsert_setting(key, Some(value.to_string()), system_settings::SettingValueType::Boolean, description, is_public).await
    }

    pub async fn set_json_value(&self, key: &str, value: &serde_json::Value, description: Option<&str>, is_public: bool) -> RepositoryResult<system_settings::Model> {
        let json_string = serde_json::to_string(value)
            .map_err(|_| RepositoryError::Validation("Invalid JSON value".to_string()))?;
        self.upsert_setting(key, Some(json_string), system_settings::SettingValueType::Json, description, is_public).await
    }

    async fn upsert_setting(&self, key: &str, value: Option<String>, value_type: system_settings::SettingValueType, description: Option<&str>, is_public: bool) -> RepositoryResult<system_settings::Model> {
        // Check if setting already exists
        if let Some(existing) = self.find_by_key(key).await? {
            // Update existing setting
            let mut setting: system_settings::ActiveModel = system_settings::ActiveModel {
                id: Set(existing.id),
                value: Set(value),
                value_type: Set(value_type),
                description: Set(description.map(|d| d.to_string())),
                is_public: Set(is_public),
                ..Default::default()
            };
            
            let setting = setting.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
            Ok(setting)
        } else {
            // Create new setting
            let setting = system_settings::ActiveModel {
                key: Set(key.to_string()),
                value: Set(value),
                value_type: Set(value_type),
                description: Set(description.map(|d| d.to_string())),
                is_public: Set(is_public),
                ..Default::default()
            };
            
            let setting = setting.insert(self.db.as_ref()).await.map_err(RepositoryError::from)?;
            Ok(setting)
        }
    }

    pub async fn delete_by_key(&self, key: &str) -> RepositoryResult<()> {
        let setting = self.find_by_key(key).await?
            .ok_or(RepositoryError::NotFound)?;
        
        SystemSettings::delete_by_id(setting.id).exec(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(())
    }

    pub async fn update_description(&self, key: &str, description: &str) -> RepositoryResult<system_settings::Model> {
        let setting = self.find_by_key(key).await?
            .ok_or(RepositoryError::NotFound)?;
        
        let mut setting_active: system_settings::ActiveModel = system_settings::ActiveModel {
            id: Set(setting.id),
            description: Set(Some(description.to_string())),
            ..Default::default()
        };
        
        let setting = setting_active.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(setting)
    }

    pub async fn set_public_visibility(&self, key: &str, is_public: bool) -> RepositoryResult<system_settings::Model> {
        let setting = self.find_by_key(key).await?
            .ok_or(RepositoryError::NotFound)?;
        
        let mut setting_active: system_settings::ActiveModel = system_settings::ActiveModel {
            id: Set(setting.id),
            is_public: Set(is_public),
            ..Default::default()
        };
        
        let setting = setting_active.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(setting)
    }

    pub async fn get_maintenance_mode(&self) -> RepositoryResult<bool> {
        self.get_boolean_value("maintenance_mode").await.map(|v| v.unwrap_or(false))
    }

    pub async fn set_maintenance_mode(&self, enabled: bool, message: Option<&str>) -> RepositoryResult<()> {
        self.set_boolean_value("maintenance_mode", enabled, Some("维护模式"), true).await?;
        
        if let Some(msg) = message {
            self.set_string_value("maintenance_message", msg, Some("维护提示信息"), true).await?;
        }
        
        Ok(())
    }

    pub async fn get_site_name(&self) -> RepositoryResult<String> {
        self.get_string_value("site_name").await.map(|v| v.unwrap_or_else(|| "VPN Service".to_string()))
    }

    pub async fn get_max_login_attempts(&self) -> RepositoryResult<i64> {
        self.get_integer_value("max_login_attempts").await.map(|v| v.unwrap_or(5))
    }

    pub async fn get_login_lockout_minutes(&self) -> RepositoryResult<i64> {
        self.get_integer_value("login_lockout_minutes").await.map(|v| v.unwrap_or(30))
    }

    pub async fn get_renewal_reminder_days(&self) -> RepositoryResult<i64> {
        self.get_integer_value("renewal_reminder_days").await.map(|v| v.unwrap_or(7))
    }

    pub async fn get_cleanup_inactive_users_days(&self) -> RepositoryResult<i64> {
        self.get_integer_value("cleanup_inactive_users_days").await.map(|v| v.unwrap_or(30))
    }

    pub async fn get_support_contact(&self) -> RepositoryResult<String> {
        self.get_string_value("support_contact").await.map(|v| v.unwrap_or_else(|| "<EMAIL>".to_string()))
    }

    pub async fn backup_settings(&self) -> RepositoryResult<Vec<system_settings::Model>> {
        let settings = SystemSettings::find()
            .order_by_asc(system_settings::Column::Key)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(settings)
    }

    pub async fn restore_settings(&self, settings: Vec<system_settings::Model>) -> RepositoryResult<()> {
        for setting in settings {
            self.upsert_setting(
                &setting.key,
                setting.value,
                setting.value_type,
                setting.description.as_deref(),
                setting.is_public,
            ).await?;
        }
        
        Ok(())
    }
}

#[async_trait::async_trait]
impl Repository<SystemSettings, system_settings::Model, system_settings::ActiveModel> for SystemSettingRepository {
    async fn create(&self, model: system_settings::ActiveModel) -> Result<system_settings::Model, DbErr> {
        let setting = model.insert(self.db.as_ref()).await?;
        Ok(setting)
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<system_settings::Model>, DbErr> {
        let setting = SystemSettings::find_by_id(id).one(self.db.as_ref()).await?;
        Ok(setting)
    }

    async fn find_all(&self) -> Result<Vec<system_settings::Model>, DbErr> {
        let settings = SystemSettings::find()
            .order_by_asc(system_settings::Column::Key)
            .all(self.db.as_ref())
            .await?;
        Ok(settings)
    }

    async fn update(&self, id: Uuid, model: system_settings::ActiveModel) -> Result<system_settings::Model, DbErr> {
        let mut model = model;
        model.id = Set(id);
        let setting = model.update(self.db.as_ref()).await?;
        Ok(setting)
    }

    async fn delete(&self, id: Uuid) -> Result<(), DbErr> {
        SystemSettings::delete_by_id(id).exec(self.db.as_ref()).await?;
        Ok(())
    }

    async fn count(&self) -> Result<u64, DbErr> {
        let count = SystemSettings::find().count(self.db.as_ref()).await?;
        Ok(count)
    }
}

#[async_trait::async_trait]
impl PaginatedRepository<SystemSettings, system_settings::Model> for SystemSettingRepository {
    async fn find_with_pagination(&self, params: PaginationParams) -> Result<PaginatedResult<system_settings::Model>, DbErr> {
        let query = SystemSettings::find().order_by_asc(system_settings::Column::Key);
        self.paginate_query(query, params).await
    }

    async fn find_with_pagination_and_filter(&self, params: PaginationParams, filter: sea_orm::Condition) -> Result<PaginatedResult<system_settings::Model>, DbErr> {
        let query = SystemSettings::find()
            .filter(filter)
            .order_by_asc(system_settings::Column::Key);
        self.paginate_query(query, params).await
    }
}

impl PaginationHelper<SystemSettings, system_settings::Model> for SystemSettingRepository {
    fn db(&self) -> &DatabaseConnection {
        self.db.as_ref()
    }
}