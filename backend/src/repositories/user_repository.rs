use sea_orm::{DatabaseConnection, DbErr, EntityTrait, QueryFilter, QueryOrder, ActiveModelTrait, Set};
use sea_orm::prelude::*;
use std::sync::Arc;

use crate::entities::{users, prelude::*};
use super::{Repository, PaginatedRepository, PaginationParams, PaginatedResult, PaginationHelper, RepositoryError, RepositoryResult};

#[derive(Clone)]
pub struct UserRepository {
    db: Arc<DatabaseConnection>,
}

impl UserRepository {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self { db }
    }

    pub async fn find_by_username(&self, username: &str) -> RepositoryResult<Option<users::Model>> {
        let user = Users::find()
            .filter(users::Column::Username.eq(username))
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(user)
    }

    pub async fn find_by_email(&self, email: &str) -> RepositoryResult<Option<users::Model>> {
        let user = Users::find()
            .filter(users::Column::Email.eq(email))
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(user)
    }

    pub async fn find_active_users(&self) -> RepositoryResult<Vec<users::Model>> {
        let users = Users::find()
            .filter(users::Column::IsActive.eq(true))
            .filter(users::Column::IsBanned.eq(false))
            .order_by_asc(users::Column::Username)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(users)
    }

    pub async fn find_banned_users(&self) -> RepositoryResult<Vec<users::Model>> {
        let users = Users::find()
            .filter(users::Column::IsBanned.eq(true))
            .order_by_desc(users::Column::UpdatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(users)
    }

    pub async fn find_inactive_users(&self) -> RepositoryResult<Vec<users::Model>> {
        let users = Users::find()
            .filter(users::Column::IsActive.eq(false))
            .order_by_desc(users::Column::UpdatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(users)
    }

    pub async fn find_admin_users(&self) -> RepositoryResult<Vec<users::Model>> {
        let users = Users::find()
            .filter(users::Column::IsAdmin.eq(true))
            .order_by_asc(users::Column::Username)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(users)
    }

    pub async fn update_last_login(&self, user_id: Uuid) -> RepositoryResult<()> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let mut user: users::ActiveModel = users::ActiveModel {
            id: Set(user_id),
            last_login_at: Set(Some(now)),
            ..Default::default()
        };
        user.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        
        Ok(())
    }

    pub async fn ban_user(&self, user_id: Uuid, reason: String, banned_until: Option<chrono::DateTime<chrono::FixedOffset>>) -> RepositoryResult<users::Model> {
        let mut user: users::ActiveModel = users::ActiveModel {
            id: Set(user_id),
            is_banned: Set(true),
            ban_reason: Set(Some(reason)),
            banned_until: Set(banned_until),
            ..Default::default()
        };
        
        let user = user.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(user)
    }

    pub async fn unban_user(&self, user_id: Uuid) -> RepositoryResult<users::Model> {
        let mut user: users::ActiveModel = users::ActiveModel {
            id: Set(user_id),
            is_banned: Set(false),
            ban_reason: Set(None),
            banned_until: Set(None),
            ..Default::default()
        };
        
        let user = user.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(user)
    }

    pub async fn update_traffic_usage(&self, user_id: Uuid, bytes_used: i64) -> RepositoryResult<()> {
        let mut user: users::ActiveModel = users::ActiveModel {
            id: Set(user_id),
            total_traffic_used: Set(bytes_used),
            ..Default::default()
        };
        user.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        
        Ok(())
    }

    pub async fn reset_traffic_usage(&self, user_id: Uuid) -> RepositoryResult<()> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        
        let mut user: users::ActiveModel = users::ActiveModel {
            id: Set(user_id),
            total_traffic_used: Set(0),
            last_traffic_reset: Set(now),
            ..Default::default()
        };
        user.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        
        Ok(())
    }

    /// Search users by username or email
    pub async fn search_users(&self, search_term: &str) -> RepositoryResult<Vec<users::Model>> {
        let search_pattern = format!("%{}%", search_term);
        
        let users = Users::find()
            .filter(
                users::Column::Username.like(&search_pattern)
                    .or(users::Column::Email.like(&search_pattern))
            )
            .order_by_asc(users::Column::Username)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(users)
    }
}

#[async_trait::async_trait]
impl Repository<Users, users::Model, users::ActiveModel> for UserRepository {
    async fn create(&self, model: users::ActiveModel) -> Result<users::Model, DbErr> {
        let user = model.insert(self.db.as_ref()).await?;
        Ok(user)
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<users::Model>, DbErr> {
        let user = Users::find_by_id(id).one(self.db.as_ref()).await?;
        Ok(user)
    }

    async fn find_all(&self) -> Result<Vec<users::Model>, DbErr> {
        let users = Users::find().all(self.db.as_ref()).await?;
        Ok(users)
    }

    async fn update(&self, id: Uuid, model: users::ActiveModel) -> Result<users::Model, DbErr> {
        let mut model = model;
        model.id = Set(id);
        let user = model.update(self.db.as_ref()).await?;
        Ok(user)
    }

    async fn delete(&self, id: Uuid) -> Result<(), DbErr> {
        Users::delete_by_id(id).exec(self.db.as_ref()).await?;
        Ok(())
    }

    async fn count(&self) -> Result<u64, DbErr> {
        let count = Users::find().count(self.db.as_ref()).await?;
        Ok(count)
    }
}

#[async_trait::async_trait]
impl PaginatedRepository<Users, users::Model> for UserRepository {
    async fn find_with_pagination(&self, params: PaginationParams) -> Result<PaginatedResult<users::Model>, DbErr> {
        let query = Users::find().order_by_asc(users::Column::Username);
        self.paginate_query(query, params).await
    }

    async fn find_with_pagination_and_filter(&self, params: PaginationParams, filter: sea_orm::Condition) -> Result<PaginatedResult<users::Model>, DbErr> {
        let query = Users::find()
            .filter(filter)
            .order_by_asc(users::Column::Username);
        self.paginate_query(query, params).await
    }
}

impl PaginationHelper<Users, users::Model> for UserRepository {
    fn db(&self) -> &DatabaseConnection {
        self.db.as_ref()
    }
}