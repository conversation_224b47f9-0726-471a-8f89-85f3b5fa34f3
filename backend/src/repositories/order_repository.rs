use sea_orm::prelude::*;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, DbErr, EntityTrait, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON>ilter, QueryOrder,
    RelationTrait, Set,
};
use std::sync::Arc;

use super::{
    PaginatedRepository, PaginatedResult, PaginationHelper, PaginationParams, Repository,
    RepositoryError, RepositoryResult,
};
use crate::entities::{
    enums::{OrderStatus, PaymentMethodType},
    orders, plans,
    prelude::*,
    users,
};

#[derive(Clone)]
pub struct OrderRepository {
    db: Arc<DatabaseConnection>,
}

impl OrderRepository {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self { db }
    }

    pub async fn find_by_user_id(&self, user_id: Uuid) -> RepositoryResult<Vec<orders::Model>> {
        let orders = Orders::find()
            .filter(orders::Column::UserId.eq(user_id))
            .order_by_desc(orders::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(orders)
    }

    pub async fn find_by_order_number(
        &self,
        order_number: &str,
    ) -> RepositoryResult<Option<orders::Model>> {
        let order = Orders::find()
            .filter(orders::Column::OrderNumber.eq(order_number))
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(order)
    }

    pub async fn find_by_status(
        &self,
        status: OrderStatus,
    ) -> RepositoryResult<Vec<orders::Model>> {
        let orders = Orders::find()
            .filter(orders::Column::Status.eq(status))
            .order_by_desc(orders::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(orders)
    }

    pub async fn find_by_payment_method(
        &self,
        payment_method: PaymentMethodType,
    ) -> RepositoryResult<Vec<orders::Model>> {
        let orders = Orders::find()
            .filter(orders::Column::PaymentMethod.eq(payment_method))
            .order_by_desc(orders::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(orders)
    }

    pub async fn find_pending_orders(&self) -> RepositoryResult<Vec<orders::Model>> {
        let orders = Orders::find()
            .filter(orders::Column::Status.eq(OrderStatus::PendingPayment))
            .order_by_asc(orders::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(orders)
    }

    pub async fn find_expired_orders(&self) -> RepositoryResult<Vec<orders::Model>> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());

        let orders = Orders::find()
            .filter(orders::Column::Status.eq(OrderStatus::PendingPayment))
            .filter(orders::Column::ExpiresAt.lt(now))
            .order_by_asc(orders::Column::ExpiresAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(orders)
    }

    // Alias for consistency with service naming
    pub async fn find_expired(&self) -> RepositoryResult<Vec<orders::Model>> {
        self.find_expired_orders().await
    }

    pub async fn find_with_user_and_plan(
        &self,
        order_id: Uuid,
    ) -> RepositoryResult<Option<(orders::Model, Option<users::Model>, Option<plans::Model>)>> {
        let result = Orders::find_by_id(order_id)
            .find_also_related(Users)
            .find_also_related(Plans)
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(result)
    }

    pub async fn update_status(
        &self,
        order_id: Uuid,
        status: OrderStatus,
    ) -> RepositoryResult<orders::Model> {
        let mut order: orders::ActiveModel = orders::ActiveModel {
            id: Set(order_id),
            status: Set(status),
            ..Default::default()
        };

        let order = order
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(order)
    }

    pub async fn update_payment_info(
        &self,
        order_id: Uuid,
        payment_method: PaymentMethodType,
        payment_reference: Option<String>,
        payment_proof_url: Option<String>,
    ) -> RepositoryResult<orders::Model> {
        let mut order: orders::ActiveModel = orders::ActiveModel {
            id: Set(order_id),
            payment_method: Set(Some(payment_method)),
            payment_reference: Set(payment_reference),
            payment_proof_url: Set(payment_proof_url),
            ..Default::default()
        };

        let order = order
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(order)
    }

    pub async fn process_order(
        &self,
        order_id: Uuid,
        processed_by: Uuid,
        notes: Option<String>,
    ) -> RepositoryResult<orders::Model> {
        let now = chrono::Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());

        let mut order: orders::ActiveModel = orders::ActiveModel {
            id: Set(order_id),
            status: Set(OrderStatus::Processing),
            processed_by: Set(Some(processed_by)),
            processed_at: Set(Some(now)),
            notes: Set(notes),
            ..Default::default()
        };

        let order = order
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(order)
    }

    pub async fn complete_order(&self, order_id: Uuid) -> RepositoryResult<orders::Model> {
        let mut order: orders::ActiveModel = orders::ActiveModel {
            id: Set(order_id),
            status: Set(OrderStatus::Completed),
            ..Default::default()
        };

        let order = order
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(order)
    }

    pub async fn cancel_order(
        &self,
        order_id: Uuid,
        notes: Option<String>,
    ) -> RepositoryResult<orders::Model> {
        let mut order: orders::ActiveModel = orders::ActiveModel {
            id: Set(order_id),
            status: Set(OrderStatus::Cancelled),
            notes: Set(notes),
            ..Default::default()
        };

        let order = order
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(order)
    }

    pub async fn refund_order(
        &self,
        order_id: Uuid,
        notes: Option<String>,
    ) -> RepositoryResult<orders::Model> {
        let mut order: orders::ActiveModel = orders::ActiveModel {
            id: Set(order_id),
            status: Set(OrderStatus::Refunded),
            notes: Set(notes),
            ..Default::default()
        };

        let order = order
            .update(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        Ok(order)
    }

    pub async fn find_by_plan_id(&self, plan_id: Uuid) -> RepositoryResult<Vec<orders::Model>> {
        let orders = Orders::find()
            .filter(orders::Column::PlanId.eq(plan_id))
            .order_by_desc(orders::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(orders)
    }

    pub async fn find_by_date_range(
        &self,
        start_date: chrono::DateTime<chrono::FixedOffset>,
        end_date: chrono::DateTime<chrono::FixedOffset>,
    ) -> RepositoryResult<Vec<orders::Model>> {
        let orders = Orders::find()
            .filter(orders::Column::CreatedAt.gte(start_date))
            .filter(orders::Column::CreatedAt.lte(end_date))
            .order_by_desc(orders::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(orders)
    }

    pub async fn calculate_revenue_by_date_range(
        &self,
        start_date: chrono::DateTime<chrono::FixedOffset>,
        end_date: chrono::DateTime<chrono::FixedOffset>,
    ) -> RepositoryResult<rust_decimal::Decimal> {
        let orders = Orders::find()
            .filter(orders::Column::CreatedAt.gte(start_date))
            .filter(orders::Column::CreatedAt.lte(end_date))
            .filter(orders::Column::Status.eq(OrderStatus::Completed))
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        let total_revenue = orders
            .iter()
            .fold(rust_decimal::Decimal::ZERO, |acc, order| acc + order.amount);

        Ok(total_revenue)
    }

    pub async fn count_by_status(&self, status: OrderStatus) -> RepositoryResult<u64> {
        let count = Orders::find()
            .filter(orders::Column::Status.eq(status))
            .count(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(count)
    }

    pub async fn count_by_user(&self, user_id: Uuid) -> RepositoryResult<u64> {
        let count = Orders::find()
            .filter(orders::Column::UserId.eq(user_id))
            .count(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(count)
    }
}

#[async_trait::async_trait]
impl Repository<Orders, orders::Model, orders::ActiveModel> for OrderRepository {
    async fn create(&self, model: orders::ActiveModel) -> Result<orders::Model, DbErr> {
        let order = model.insert(self.db.as_ref()).await?;
        Ok(order)
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<orders::Model>, DbErr> {
        let order = Orders::find_by_id(id).one(self.db.as_ref()).await?;
        Ok(order)
    }

    async fn find_all(&self) -> Result<Vec<orders::Model>, DbErr> {
        let orders = Orders::find()
            .order_by_desc(orders::Column::CreatedAt)
            .all(self.db.as_ref())
            .await?;
        Ok(orders)
    }

    async fn update(&self, id: Uuid, model: orders::ActiveModel) -> Result<orders::Model, DbErr> {
        let mut model = model;
        model.id = Set(id);
        let order = model.update(self.db.as_ref()).await?;
        Ok(order)
    }

    async fn delete(&self, id: Uuid) -> Result<(), DbErr> {
        Orders::delete_by_id(id).exec(self.db.as_ref()).await?;
        Ok(())
    }

    async fn count(&self) -> Result<u64, DbErr> {
        let count = Orders::find().count(self.db.as_ref()).await?;
        Ok(count)
    }
}

#[async_trait::async_trait]
impl PaginatedRepository<Orders, orders::Model> for OrderRepository {
    async fn find_with_pagination(
        &self,
        params: PaginationParams,
    ) -> Result<PaginatedResult<orders::Model>, DbErr> {
        let query = Orders::find().order_by_desc(orders::Column::CreatedAt);
        self.paginate_query(query, params).await
    }

    async fn find_with_pagination_and_filter(
        &self,
        params: PaginationParams,
        filter: sea_orm::Condition,
    ) -> Result<PaginatedResult<orders::Model>, DbErr> {
        let query = Orders::find()
            .filter(filter)
            .order_by_desc(orders::Column::CreatedAt);
        self.paginate_query(query, params).await
    }
}

impl PaginationHelper<Orders, orders::Model> for OrderRepository {
    fn db(&self) -> &DatabaseConnection {
        self.db.as_ref()
    }
}
