use sea_orm::{prelude::*, FromQueryResult};
use sea_orm::{
    Condition, DatabaseConnection, DbErr, EntityTrait, Paginator, PaginatorTrait, QueryFilter,
    QueryOrder, QuerySelect, Select,
};
use std::sync::Arc;

pub mod announcement_repository;
pub mod api_token_repository;
pub mod config_template_repository;
pub mod connection_log_repository;
pub mod order_repository;
pub mod plan_repository;
pub mod subscription_repository;
pub mod system_setting_repository;
pub mod user_repository;

pub use announcement_repository::AnnouncementRepository;
pub use api_token_repository::ApiTokenRepository;
pub use config_template_repository::ConfigTemplateRepository;
pub use connection_log_repository::ConnectionLogRepository;
pub use order_repository::OrderRepository;
pub use plan_repository::PlanRepository;
pub use subscription_repository::SubscriptionRepository;
pub use system_setting_repository::SystemSettingRepository;
pub use user_repository::UserRepository;

/// Common repository trait for basic CRUD operations
#[async_trait::async_trait]
pub trait Repository<Entity, Model, ActiveModel>
where
    Entity: EntityTrait<Model = Model>,
    Model: Clone + Send + Sync,
    ActiveModel: Send + Sync,
{
    /// Create a new entity
    async fn create(&self, model: ActiveModel) -> Result<Model, DbErr>;

    /// Find entity by ID
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Model>, DbErr>;

    /// Find all entities
    async fn find_all(&self) -> Result<Vec<Model>, DbErr>;

    /// Update an entity
    async fn update(&self, id: Uuid, model: ActiveModel) -> Result<Model, DbErr>;

    /// Delete an entity
    async fn delete(&self, id: Uuid) -> Result<(), DbErr>;

    /// Count total entities
    async fn count(&self) -> Result<u64, DbErr>;
}

/// Pagination parameters
#[derive(Debug, Clone)]
pub struct PaginationParams {
    pub page: u64,
    pub per_page: u64,
}

impl Default for PaginationParams {
    fn default() -> Self {
        Self {
            page: 1,
            per_page: 20,
        }
    }
}

/// Paginated result
#[derive(Debug, Clone)]
pub struct PaginatedResult<T> {
    pub items: Vec<T>,
    pub page: u64,
    pub per_page: u64,
    pub total: u64,
    pub total_pages: u64,
}

impl<T> PaginatedResult<T> {
    pub fn new(items: Vec<T>, page: u64, per_page: u64, total: u64) -> Self {
        let total_pages = (total + per_page - 1) / per_page;
        Self {
            items,
            page,
            per_page,
            total,
            total_pages,
        }
    }
}

/// Common pagination trait
#[async_trait::async_trait]
pub trait PaginatedRepository<Entity, Model>
where
    Entity: EntityTrait<Model = Model>,
    Model: Clone + Send + Sync,
{
    /// Find entities with pagination
    async fn find_with_pagination(
        &self,
        params: PaginationParams,
    ) -> Result<PaginatedResult<Model>, DbErr>;

    /// Find entities with pagination and filtering
    async fn find_with_pagination_and_filter(
        &self,
        params: PaginationParams,
        filter: Condition,
    ) -> Result<PaginatedResult<Model>, DbErr>;
}

/// Repository manager that holds all repositories
pub struct RepositoryManager {
    pub user: UserRepository,
    pub plan: PlanRepository,
    pub subscription: SubscriptionRepository,
    pub order: OrderRepository,
    pub announcement: AnnouncementRepository,
    pub connection_log: ConnectionLogRepository,
    pub system_setting: SystemSettingRepository,
    pub api_token: ApiTokenRepository,
    pub config_template: ConfigTemplateRepository,
}

impl RepositoryManager {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self {
            user: UserRepository::new(db.clone()),
            plan: PlanRepository::new(db.clone()),
            subscription: SubscriptionRepository::new(db.clone()),
            order: OrderRepository::new(db.clone()),
            announcement: AnnouncementRepository::new(db.clone()),
            connection_log: ConnectionLogRepository::new(db.clone()),
            system_setting: SystemSettingRepository::new(db.clone()),
            api_token: ApiTokenRepository::new(db.clone()),
            config_template: ConfigTemplateRepository::new(db.clone()),
        }
    }
}

/// Helper trait for implementing pagination
pub trait PaginationHelper<Entity, Model>
where
    Entity: EntityTrait<Model = Model>,
    Model: FromQueryResult + Clone + Send + Sync,
{
    async fn paginate_query(
        &self,
        query: Select<Entity>,
        params: PaginationParams,
    ) -> Result<PaginatedResult<Model>, DbErr> {
        // Get all items first to count them (simple approach)
        let paged_selector = query.paginate(self.db(), params.per_page);
        let total = paged_selector.num_items().await?;
        let items = paged_selector.fetch_page(params.page - 1).await?;

        Ok(PaginatedResult::new(
            items,
            params.page,
            params.per_page,
            total,
        ))
    }

    fn db(&self) -> &DatabaseConnection;
}

/// Common query filters
pub struct QueryFilters;

impl QueryFilters {
    pub fn active_only() -> Condition {
        Condition::all().add(crate::entities::users::Column::IsActive.eq(true))
    }

    pub fn not_banned() -> Condition {
        Condition::all().add(crate::entities::users::Column::IsBanned.eq(false))
    }

    pub fn by_status<T>(status: T) -> Condition
    where
        T: Into<sea_orm::Value>,
    {
        Condition::all().add(crate::entities::subscriptions::Column::Status.eq(status))
    }

    pub fn by_date_range(
        column: impl sea_orm::ColumnTrait,
        start_date: Option<chrono::DateTime<chrono::FixedOffset>>,
        end_date: Option<chrono::DateTime<chrono::FixedOffset>>,
    ) -> Condition {
        let mut condition = Condition::all();

        if let Some(start) = start_date {
            condition = condition.add(column.gte(start));
        }

        if let Some(end) = end_date {
            condition = condition.add(column.lte(end));
        }

        condition
    }
}

/// Repository error types
#[derive(Debug, thiserror::Error)]
pub enum RepositoryError {
    #[error("Database error: {0}")]
    Database(#[from] DbErr),

    #[error("Entity not found")]
    NotFound,

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Constraint violation: {0}")]
    Constraint(String),
}

pub type RepositoryResult<T> = Result<T, RepositoryError>;
