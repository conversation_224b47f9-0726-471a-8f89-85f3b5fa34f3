use sea_orm::{DatabaseConnection, DbErr, En<PERSON>tyTrait, QueryFilter, QueryOrder, ActiveModelTrait, Set};
use sea_orm::prelude::*;
use std::sync::Arc;

use crate::entities::{config_templates, prelude::*};
use super::{Repository, PaginatedRepository, PaginationParams, PaginatedResult, PaginationHelper, RepositoryError, RepositoryResult};

#[derive(Clone)]
pub struct ConfigTemplateRepository {
    db: Arc<DatabaseConnection>,
}

impl ConfigTemplateRepository {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self { db }
    }

    pub async fn find_by_name(&self, name: &str) -> RepositoryResult<Option<config_templates::Model>> {
        let template = ConfigTemplates::find()
            .filter(config_templates::Column::Name.eq(name))
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(template)
    }

    pub async fn find_by_type(&self, template_type: &str) -> RepositoryResult<Vec<config_templates::Model>> {
        let templates = ConfigTemplates::find()
            .filter(config_templates::Column::TemplateType.eq(template_type))
            .filter(config_templates::Column::IsActive.eq(true))
            .order_by_asc(config_templates::Column::SortOrder)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(templates)
    }

    pub async fn find_active_templates(&self) -> RepositoryResult<Vec<config_templates::Model>> {
        let templates = ConfigTemplates::find()
            .filter(config_templates::Column::IsActive.eq(true))
            .order_by_asc(config_templates::Column::SortOrder)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(templates)
    }

    pub async fn find_system_default_by_type(&self, template_type: &str) -> RepositoryResult<Option<config_templates::Model>> {
        let template = ConfigTemplates::find()
            .filter(config_templates::Column::TemplateType.eq(template_type))
            .filter(config_templates::Column::IsSystemDefault.eq(true))
            .filter(config_templates::Column::IsActive.eq(true))
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(template)
    }

    pub async fn find_all_ordered(&self) -> RepositoryResult<Vec<config_templates::Model>> {
        let templates = ConfigTemplates::find()
            .order_by_asc(config_templates::Column::SortOrder)
            .order_by_asc(config_templates::Column::Name)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(templates)
    }

    pub async fn create_template(
        &self,
        name: &str,
        description: Option<&str>,
        template_content: &str,
        template_type: &str,
        version: Option<&str>,
        is_system_default: bool,
        sort_order: i32,
        created_by: Uuid,
    ) -> RepositoryResult<config_templates::Model> {
        let template = config_templates::ActiveModel {
            name: Set(name.to_string()),
            description: Set(description.map(|d| d.to_string())),
            template_content: Set(template_content.to_string()),
            template_type: Set(template_type.to_string()),
            version: Set(version.unwrap_or("1.0").to_string()),
            is_active: Set(true),
            is_system_default: Set(is_system_default),
            sort_order: Set(sort_order),
            created_by: Set(created_by),
            updated_by: Set(created_by),
            ..Default::default()
        };

        let template = template.insert(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(template)
    }

    pub async fn update_template(
        &self,
        id: Uuid,
        name: Option<&str>,
        description: Option<&str>,
        template_content: Option<&str>,
        template_type: Option<&str>,
        version: Option<&str>,
        is_active: Option<bool>,
        is_system_default: Option<bool>,
        sort_order: Option<i32>,
        updated_by: Uuid,
    ) -> RepositoryResult<config_templates::Model> {
        let mut template = config_templates::ActiveModel {
            id: Set(id),
            updated_by: Set(updated_by),
            ..Default::default()
        };

        if let Some(name) = name {
            template.name = Set(name.to_string());
        }
        if let Some(description) = description {
            template.description = Set(Some(description.to_string()));
        }
        if let Some(template_content) = template_content {
            template.template_content = Set(template_content.to_string());
        }
        if let Some(template_type) = template_type {
            template.template_type = Set(template_type.to_string());
        }
        if let Some(version) = version {
            template.version = Set(version.to_string());
        }
        if let Some(is_active) = is_active {
            template.is_active = Set(is_active);
        }
        if let Some(is_system_default) = is_system_default {
            template.is_system_default = Set(is_system_default);
        }
        if let Some(sort_order) = sort_order {
            template.sort_order = Set(sort_order);
        }

        let template = template.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(template)
    }

    pub async fn set_system_default(&self, id: Uuid, template_type: &str, updated_by: Uuid) -> RepositoryResult<config_templates::Model> {
        // First, clear any existing system defaults for this type
        let existing_defaults = ConfigTemplates::find()
            .filter(config_templates::Column::TemplateType.eq(template_type))
            .filter(config_templates::Column::IsSystemDefault.eq(true))
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        for existing in existing_defaults {
            let mut model = config_templates::ActiveModel {
                id: Set(existing.id),
                is_system_default: Set(false),
                updated_by: Set(updated_by),
                ..Default::default()
            };
            model.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        }

        // Then set the new system default
        let mut template = config_templates::ActiveModel {
            id: Set(id),
            is_system_default: Set(true),
            updated_by: Set(updated_by),
            ..Default::default()
        };

        let template = template.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(template)
    }

    pub async fn toggle_active(&self, id: Uuid, updated_by: Uuid) -> RepositoryResult<config_templates::Model> {
        let existing = self.find_by_id(id).await?
            .ok_or(RepositoryError::NotFound)?;

        let mut template = config_templates::ActiveModel {
            id: Set(id),
            is_active: Set(!existing.is_active),
            updated_by: Set(updated_by),
            ..Default::default()
        };

        let template = template.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(template)
    }

    pub async fn update_sort_order(&self, id: Uuid, sort_order: i32, updated_by: Uuid) -> RepositoryResult<config_templates::Model> {
        let mut template = config_templates::ActiveModel {
            id: Set(id),
            sort_order: Set(sort_order),
            updated_by: Set(updated_by),
            ..Default::default()
        };

        let template = template.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(template)
    }

    pub async fn duplicate_template(&self, id: Uuid, new_name: &str, created_by: Uuid) -> RepositoryResult<config_templates::Model> {
        let existing = self.find_by_id(id).await?
            .ok_or(RepositoryError::NotFound)?;

        let template = config_templates::ActiveModel {
            name: Set(new_name.to_string()),
            description: Set(existing.description.map(|d| format!("Copy of {}", d))),
            template_content: Set(existing.template_content),
            template_type: Set(existing.template_type),
            version: Set(existing.version),
            is_active: Set(false), // New templates start inactive
            is_system_default: Set(false), // Duplicates are never system defaults
            sort_order: Set(existing.sort_order + 1),
            created_by: Set(created_by),
            updated_by: Set(created_by),
            ..Default::default()
        };

        let template = template.insert(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(template)
    }

    pub async fn get_template_stats(&self) -> RepositoryResult<(u64, u64, u64)> {
        let total_count = ConfigTemplates::find()
            .count(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        let active_count = ConfigTemplates::find()
            .filter(config_templates::Column::IsActive.eq(true))
            .count(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        let system_default_count = ConfigTemplates::find()
            .filter(config_templates::Column::IsSystemDefault.eq(true))
            .count(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok((total_count, active_count, system_default_count))
    }

    pub async fn validate_template_name(&self, name: &str, exclude_id: Option<Uuid>) -> RepositoryResult<bool> {
        let mut query = ConfigTemplates::find()
            .filter(config_templates::Column::Name.eq(name));

        if let Some(id) = exclude_id {
            query = query.filter(config_templates::Column::Id.ne(id));
        }

        let exists = query.one(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(exists.is_none())
    }

    pub async fn find_templates_by_created_by(&self, created_by: Uuid) -> RepositoryResult<Vec<config_templates::Model>> {
        let templates = ConfigTemplates::find()
            .filter(config_templates::Column::CreatedBy.eq(created_by))
            .order_by_desc(config_templates::Column::CreatedAt)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        Ok(templates)
    }

    pub async fn get_template_types(&self) -> RepositoryResult<Vec<String>> {
        let templates = ConfigTemplates::find()
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;

        let mut types: Vec<String> = templates
            .into_iter()
            .map(|t| t.template_type)
            .collect::<std::collections::HashSet<_>>()
            .into_iter()
            .collect();

        types.sort();
        Ok(types)
    }
}

#[async_trait::async_trait]
impl Repository<ConfigTemplates, config_templates::Model, config_templates::ActiveModel> for ConfigTemplateRepository {
    async fn create(&self, model: config_templates::ActiveModel) -> Result<config_templates::Model, DbErr> {
        let template = model.insert(self.db.as_ref()).await?;
        Ok(template)
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<config_templates::Model>, DbErr> {
        let template = ConfigTemplates::find_by_id(id).one(self.db.as_ref()).await?;
        Ok(template)
    }

    async fn find_all(&self) -> Result<Vec<config_templates::Model>, DbErr> {
        let templates = ConfigTemplates::find()
            .order_by_asc(config_templates::Column::SortOrder)
            .order_by_asc(config_templates::Column::Name)
            .all(self.db.as_ref())
            .await?;
        Ok(templates)
    }

    async fn update(&self, id: Uuid, model: config_templates::ActiveModel) -> Result<config_templates::Model, DbErr> {
        let mut model = model;
        model.id = Set(id);
        let template = model.update(self.db.as_ref()).await?;
        Ok(template)
    }

    async fn delete(&self, id: Uuid) -> Result<(), DbErr> {
        ConfigTemplates::delete_by_id(id).exec(self.db.as_ref()).await?;
        Ok(())
    }

    async fn count(&self) -> Result<u64, DbErr> {
        let count = ConfigTemplates::find().count(self.db.as_ref()).await?;
        Ok(count)
    }
}

#[async_trait::async_trait]
impl PaginatedRepository<ConfigTemplates, config_templates::Model> for ConfigTemplateRepository {
    async fn find_with_pagination(&self, params: PaginationParams) -> Result<PaginatedResult<config_templates::Model>, DbErr> {
        let query = ConfigTemplates::find()
            .order_by_asc(config_templates::Column::SortOrder)
            .order_by_asc(config_templates::Column::Name);
        self.paginate_query(query, params).await
    }

    async fn find_with_pagination_and_filter(&self, params: PaginationParams, filter: sea_orm::Condition) -> Result<PaginatedResult<config_templates::Model>, DbErr> {
        let query = ConfigTemplates::find()
            .filter(filter)
            .order_by_asc(config_templates::Column::SortOrder)
            .order_by_asc(config_templates::Column::Name);
        self.paginate_query(query, params).await
    }
}

impl PaginationHelper<ConfigTemplates, config_templates::Model> for ConfigTemplateRepository {
    fn db(&self) -> &DatabaseConnection {
        self.db.as_ref()
    }
}