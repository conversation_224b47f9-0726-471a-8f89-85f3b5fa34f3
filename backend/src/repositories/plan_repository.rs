use sea_orm::{DatabaseConnection, DbErr, En<PERSON>tyTrait, QueryFilter, QueryOrder, ActiveModelTrait, Set};
use sea_orm::prelude::*;
use std::sync::Arc;

use crate::entities::{plans, prelude::*};
use super::{Repository, PaginatedRepository, PaginationParams, PaginatedResult, PaginationHelper, RepositoryError, RepositoryResult};

#[derive(Clone)]
pub struct PlanRepository {
    db: Arc<DatabaseConnection>,
}

impl PlanRepository {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self { db }
    }

    pub async fn find_active_plans(&self) -> RepositoryResult<Vec<plans::Model>> {
        let plans = Plans::find()
            .filter(plans::Column::IsActive.eq(true))
            .order_by_asc(plans::Column::SortOrder)
            .order_by_asc(plans::Column::Price)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(plans)
    }

    pub async fn find_featured_plans(&self) -> RepositoryResult<Vec<plans::Model>> {
        let plans = Plans::find()
            .filter(plans::Column::IsActive.eq(true))
            .filter(plans::Column::IsFeatured.eq(true))
            .order_by_asc(plans::Column::SortOrder)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(plans)
    }

    pub async fn find_by_price_range(&self, min_price: rust_decimal::Decimal, max_price: rust_decimal::Decimal) -> RepositoryResult<Vec<plans::Model>> {
        let plans = Plans::find()
            .filter(plans::Column::IsActive.eq(true))
            .filter(plans::Column::Price.gte(min_price))
            .filter(plans::Column::Price.lte(max_price))
            .order_by_asc(plans::Column::Price)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(plans)
    }

    pub async fn find_by_duration(&self, duration_days: i32) -> RepositoryResult<Vec<plans::Model>> {
        let plans = Plans::find()
            .filter(plans::Column::IsActive.eq(true))
            .filter(plans::Column::DurationDays.eq(duration_days))
            .order_by_asc(plans::Column::Price)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(plans)
    }

    pub async fn find_by_traffic_limit(&self, min_traffic_gb: rust_decimal::Decimal) -> RepositoryResult<Vec<plans::Model>> {
        let plans = Plans::find()
            .filter(plans::Column::IsActive.eq(true))
            .filter(plans::Column::TrafficLimitGb.gte(min_traffic_gb))
            .order_by_asc(plans::Column::Price)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(plans)
    }

    pub async fn find_unlimited_plans(&self) -> RepositoryResult<Vec<plans::Model>> {
        let plans = Plans::find()
            .filter(plans::Column::IsActive.eq(true))
            .filter(plans::Column::TrafficLimitGb.is_null())
            .order_by_asc(plans::Column::Price)
            .all(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(plans)
    }

    pub async fn activate_plan(&self, plan_id: Uuid) -> RepositoryResult<plans::Model> {
        let mut plan: plans::ActiveModel = plans::ActiveModel {
            id: Set(plan_id),
            is_active: Set(true),
            ..Default::default()
        };
        
        let plan = plan.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(plan)
    }

    pub async fn deactivate_plan(&self, plan_id: Uuid) -> RepositoryResult<plans::Model> {
        let mut plan: plans::ActiveModel = plans::ActiveModel {
            id: Set(plan_id),
            is_active: Set(false),
            ..Default::default()
        };
        
        let plan = plan.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(plan)
    }

    pub async fn set_featured(&self, plan_id: Uuid, featured: bool) -> RepositoryResult<plans::Model> {
        let mut plan: plans::ActiveModel = plans::ActiveModel {
            id: Set(plan_id),
            is_featured: Set(featured),
            ..Default::default()
        };
        
        let plan = plan.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(plan)
    }

    pub async fn update_sort_order(&self, plan_id: Uuid, sort_order: i32) -> RepositoryResult<plans::Model> {
        let mut plan: plans::ActiveModel = plans::ActiveModel {
            id: Set(plan_id),
            sort_order: Set(sort_order),
            ..Default::default()
        };
        
        let plan = plan.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(plan)
    }

    pub async fn update_price(&self, plan_id: Uuid, price: rust_decimal::Decimal) -> RepositoryResult<plans::Model> {
        let mut plan: plans::ActiveModel = plans::ActiveModel {
            id: Set(plan_id),
            price: Set(price),
            ..Default::default()
        };
        
        let plan = plan.update(self.db.as_ref()).await.map_err(RepositoryError::from)?;
        Ok(plan)
    }

    pub async fn find_by_name(&self, name: &str) -> RepositoryResult<Option<plans::Model>> {
        let plan = Plans::find()
            .filter(plans::Column::Name.eq(name))
            .one(self.db.as_ref())
            .await
            .map_err(RepositoryError::from)?;
        
        Ok(plan)
    }
}

#[async_trait::async_trait]
impl Repository<Plans, plans::Model, plans::ActiveModel> for PlanRepository {
    async fn create(&self, model: plans::ActiveModel) -> Result<plans::Model, DbErr> {
        let plan = model.insert(self.db.as_ref()).await?;
        Ok(plan)
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<plans::Model>, DbErr> {
        let plan = Plans::find_by_id(id).one(self.db.as_ref()).await?;
        Ok(plan)
    }

    async fn find_all(&self) -> Result<Vec<plans::Model>, DbErr> {
        let plans = Plans::find()
            .order_by_asc(plans::Column::SortOrder)
            .all(self.db.as_ref())
            .await?;
        Ok(plans)
    }

    async fn update(&self, id: Uuid, model: plans::ActiveModel) -> Result<plans::Model, DbErr> {
        let mut model = model;
        model.id = Set(id);
        let plan = model.update(self.db.as_ref()).await?;
        Ok(plan)
    }

    async fn delete(&self, id: Uuid) -> Result<(), DbErr> {
        Plans::delete_by_id(id).exec(self.db.as_ref()).await?;
        Ok(())
    }

    async fn count(&self) -> Result<u64, DbErr> {
        let count = Plans::find().count(self.db.as_ref()).await?;
        Ok(count)
    }
}

#[async_trait::async_trait]
impl PaginatedRepository<Plans, plans::Model> for PlanRepository {
    async fn find_with_pagination(&self, params: PaginationParams) -> Result<PaginatedResult<plans::Model>, DbErr> {
        let query = Plans::find()
            .order_by_asc(plans::Column::SortOrder)
            .order_by_asc(plans::Column::Price);
        self.paginate_query(query, params).await
    }

    async fn find_with_pagination_and_filter(&self, params: PaginationParams, filter: sea_orm::Condition) -> Result<PaginatedResult<plans::Model>, DbErr> {
        let query = Plans::find()
            .filter(filter)
            .order_by_asc(plans::Column::SortOrder)
            .order_by_asc(plans::Column::Price);
        self.paginate_query(query, params).await
    }
}

impl PaginationHelper<Plans, plans::Model> for PlanRepository {
    fn db(&self) -> &DatabaseConnection {
        self.db.as_ref()
    }
}