// Order management API endpoints

use axum::{
    extract::{State, Path},
    response::Json,
    routing::{get, post},
    Router,
};
use sea_orm::{DatabaseConnection, ActiveModelTrait, Set};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use rust_decimal::Decimal;
use std::sync::Arc;
use crate::errors::{AppError, AppResult, ApiResponse, success};
use crate::repositories::{OrderRepository, PlanRepository, UserRepository, Repository};
use crate::entities::{orders, plans, enums::{OrderStatus, PaymentMethodType}};
use crate::auth::UserContext;

/// Create order request structure
#[derive(Debug, Deserialize)]
pub struct CreateOrderRequest {
    pub plan_id: Uuid,
    pub quantity: Option<i32>,
    pub payment_method: Option<String>,
}

/// Order response structure for API
#[derive(Debug, Serialize)]
pub struct OrderResponse {
    pub id: Uuid,
    pub order_number: String,
    pub plan: OrderPlanInfo,
    pub user: OrderUserInfo,
    pub amount: Decimal,
    pub currency: String,
    pub quantity: i32,
    pub status: String,
    pub payment_method: Option<String>,
    pub payment_reference: Option<String>,
    pub notes: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub expires_at: chrono::DateTime<chrono::Utc>,
}

/// Plan information in order response
#[derive(Debug, Serialize)]
pub struct OrderPlanInfo {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub price: Decimal,
    pub duration_days: i32,
}

/// User information in order response
#[derive(Debug, Serialize)]
pub struct OrderUserInfo {
    pub id: Uuid,
    pub username: String,
    pub email: Option<String>,
}

/// Payment instructions for manual payment
#[derive(Debug, Serialize)]
pub struct PaymentInstructions {
    pub method: String,
    pub details: String,
    pub contact: String,
}

/// Order list response structure
#[derive(Debug, Serialize)]
pub struct OrderListResponse {
    pub orders: Vec<OrderResponse>,
    pub total_count: usize,
}

/// POST /api/orders/create - Create a new order
pub async fn create_order(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Json(request): Json<CreateOrderRequest>,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let order_repo = OrderRepository::new(Arc::new(db.clone()));
    let plan_repo = PlanRepository::new(Arc::new(db.clone()));
    let user_repo = UserRepository::new(Arc::new(db));
    
    // Validate plan exists and is active
    let plan = plan_repo.find_by_id(request.plan_id).await?
        .ok_or_else(|| AppError::not_found("Plan not found"))?;
    
    if !plan.is_available() {
        return Err(AppError::bad_request("Plan is not available"));
    }
    
    // Get user information
    let user = user_repo.find_by_id(user_context.user_id).await?
        .ok_or_else(|| AppError::not_found("User not found"))?;
    
    // Validate user status
    if user.is_banned {
        return Err(AppError::authorization("User account is banned"));
    }
    
    if !user.is_active {
        return Err(AppError::authorization("User account is inactive"));
    }
    
    // Calculate order amount
    let quantity = request.quantity.unwrap_or(1);
    if quantity <= 0 {
        return Err(AppError::bad_request("Quantity must be positive"));
    }
    
    let amount = plan.price * Decimal::from(quantity);
    
    // Determine payment method
    let payment_method = request.payment_method.unwrap_or_else(|| "manual".to_string());
    
    // Parse payment method
    let payment_method_enum = match payment_method.as_str() {
        "manual" => PaymentMethodType::Manual,
        "alipay" => PaymentMethodType::Alipay,
        "wechat" => PaymentMethodType::Wechat,
        "paypal" => PaymentMethodType::PayPal,
        "crypto" => PaymentMethodType::Crypto,
        _ => return Err(AppError::bad_request("Invalid payment method")),
    };
    
    // Create new order
    let order_id = Uuid::new_v4();
    let new_order = orders::ActiveModel {
        id: Set(order_id),
        order_number: Set(format!("ORD-{}-{}", chrono::Utc::now().format("%Y%m%d"), uuid::Uuid::new_v4().to_string()[..8].to_uppercase())), // Generate order number
        user_id: Set(user_context.user_id),
        plan_id: Set(request.plan_id),
        amount: Set(amount),
        currency: Set(plan.currency.clone()),
        quantity: Set(quantity),
        payment_method: Set(Some(payment_method_enum)),
        payment_reference: Set(None),
        payment_proof_url: Set(None),
        status: Set(OrderStatus::PendingPayment),
        processed_by: Set(None),
        processed_at: Set(None),
        notes: Set(None),
        created_at: Set(chrono::Utc::now().into()),
        updated_at: Set(chrono::Utc::now().into()),
        expires_at: Set((chrono::Utc::now() + chrono::Duration::hours(24)).into()),
    };
    
    let order = order_repo.create(new_order).await
        .map_err(|e| AppError::internal(format!("Failed to create order: {}", e)))?;
    
    // Prepare response
    let order_response = OrderResponse {
        id: order.id,
        order_number: order.order_number,
        plan: OrderPlanInfo {
            id: plan.id,
            name: plan.name,
            description: plan.description,
            price: plan.price,
            duration_days: plan.duration_days,
        },
        user: OrderUserInfo {
            id: user.id,
            username: user.username,
            email: user.email,
        },
        amount: order.amount,
        currency: order.currency,
        quantity: order.quantity,
        status: order.status.to_string(),
        payment_method: order.payment_method.map(|pm| pm.to_string()),
        payment_reference: order.payment_reference,
        notes: order.notes,
        created_at: order.created_at.into(),
        updated_at: order.updated_at.into(),
        expires_at: order.expires_at.into(),
    };
    
    // Payment instructions for manual payment
    let payment_instructions = match payment_method.as_str() {
        "manual" => PaymentInstructions {
            method: "manual".to_string(),
            details: "Please contact customer service with your order number to complete payment".to_string(),
            contact: "<EMAIL>".to_string(),
        },
        "alipay" => PaymentInstructions {
            method: "alipay".to_string(),
            details: "Please use Alipay to complete payment".to_string(),
            contact: "<EMAIL>".to_string(),
        },
        "wechat" => PaymentInstructions {
            method: "wechat".to_string(),
            details: "Please use WeChat Pay to complete payment".to_string(),
            contact: "<EMAIL>".to_string(),
        },
        "paypal" => PaymentInstructions {
            method: "paypal".to_string(),
            details: "Please use PayPal to complete payment".to_string(),
            contact: "<EMAIL>".to_string(),
        },
        "crypto" => PaymentInstructions {
            method: "crypto".to_string(),
            details: "Please use cryptocurrency to complete payment".to_string(),
            contact: "<EMAIL>".to_string(),
        },
        _ => PaymentInstructions {
            method: "manual".to_string(),
            details: "Please contact customer service to complete payment".to_string(),
            contact: "<EMAIL>".to_string(),
        },
    };
    
    let response_data = serde_json::json!({
        "order": order_response,
        "payment_instructions": payment_instructions
    });
    
    Ok(Json(success(response_data)))
}

/// GET /api/orders/:order_number - Get order details by order number
pub async fn get_order_by_number(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Path(order_number): Path<String>,
) -> AppResult<Json<ApiResponse<OrderResponse>>> {
    let order_repo = OrderRepository::new(Arc::new(db.clone()));
    let plan_repo = PlanRepository::new(Arc::new(db.clone()));
    let user_repo = UserRepository::new(Arc::new(db));
    
    // Find order by order number
    let order = order_repo.find_by_order_number(&order_number).await?
        .ok_or_else(|| AppError::not_found("Order not found"))?;
    
    // Check if user owns this order (or is admin)
    if order.user_id != user_context.user_id && user_context.role != "admin" {
        return Err(AppError::authorization("Access denied"));
    }
    
    // Get plan information
    let plan = plan_repo.find_by_id(order.plan_id).await?
        .ok_or_else(|| AppError::not_found("Plan not found"))?;
    
    // Get user information
    let user = user_repo.find_by_id(order.user_id).await?
        .ok_or_else(|| AppError::not_found("User not found"))?;
    
    let response = OrderResponse {
        id: order.id,
        order_number: order.order_number,
        plan: OrderPlanInfo {
            id: plan.id,
            name: plan.name,
            description: plan.description,
            price: plan.price,
            duration_days: plan.duration_days,
        },
        user: OrderUserInfo {
            id: user.id,
            username: user.username,
            email: user.email,
        },
        amount: order.amount,
        currency: order.currency,
        quantity: order.quantity,
        status: order.status.to_string(),
        payment_method: order.payment_method.map(|pm| pm.to_string()),
        payment_reference: order.payment_reference,
        notes: order.notes,
        created_at: order.created_at.into(),
        updated_at: order.updated_at.into(),
        expires_at: order.expires_at.into(),
    };
    
    Ok(Json(success(response)))
}

/// GET /api/orders/ - Get user's order list
pub async fn get_user_orders(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<OrderListResponse>>> {
    let order_repo = OrderRepository::new(Arc::new(db.clone()));
    let plan_repo = PlanRepository::new(Arc::new(db.clone()));
    let user_repo = UserRepository::new(Arc::new(db));
    
    // Get user's orders
    let orders = order_repo.find_by_user_id(user_context.user_id).await?;
    
    let mut order_responses = Vec::new();
    
    for order in orders {
        // Get plan information
        let plan = plan_repo.find_by_id(order.plan_id).await?
            .ok_or_else(|| AppError::not_found("Plan not found"))?;
        
        // Get user information
        let user = user_repo.find_by_id(order.user_id).await?
            .ok_or_else(|| AppError::not_found("User not found"))?;
        
        order_responses.push(OrderResponse {
            id: order.id,
            order_number: order.order_number,
            plan: OrderPlanInfo {
                id: plan.id,
                name: plan.name,
                description: plan.description,
                price: plan.price,
                duration_days: plan.duration_days,
            },
            user: OrderUserInfo {
                id: user.id,
                username: user.username,
                email: user.email,
            },
            amount: order.amount,
            currency: order.currency,
            quantity: order.quantity,
            status: order.status.to_string(),
            payment_method: order.payment_method.map(|pm| pm.to_string()),
            payment_reference: order.payment_reference,
            notes: order.notes,
            created_at: order.created_at.into(),
            updated_at: order.updated_at.into(),
            expires_at: order.expires_at.into(),
        });
    }
    
    let response = OrderListResponse {
        total_count: order_responses.len(),
        orders: order_responses,
    };
    
    Ok(Json(success(response)))
}

/// Create order routes
pub fn order_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/", post(create_order).get(get_user_orders))
        .route("/:order_number", get(get_order_by_number))
}