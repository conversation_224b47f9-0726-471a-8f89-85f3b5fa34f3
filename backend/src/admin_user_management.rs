// Admin user management API endpoints for managing users

use axum::{
    extract::{Path, Query, State},
    response::Json,
    routing::{get, post, put, delete},
    Router,
};
use sea_orm::{DatabaseConnection, ActiveModelTrait, Set};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc, Datelike};
use std::sync::Arc;

use crate::auth::UserContext;
use crate::errors::{AppError, AppResult, ApiResponse, success};
use crate::repositories::{UserRepository, SubscriptionRepository, Repository};
use crate::entities::users;

/// Request structure for creating user
#[derive(Debug, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: Option<String>,
    pub password: String,
    pub is_admin: Option<bool>,
    pub is_active: Option<bool>,
    pub max_concurrent_devices: Option<i32>,
}

/// Request structure for updating user
#[derive(Debug, Deserialize)]
pub struct UpdateUserRequest {
    pub username: Option<String>,
    pub email: Option<String>,
    pub password: Option<String>,
    pub is_admin: Option<bool>,
    pub is_active: Option<bool>,
    pub max_concurrent_devices: Option<i32>,
}

/// Request structure for banning user
#[derive(Debug, Deserialize)]
pub struct BanUserRequest {
    pub reason: String,
    pub banned_until: Option<DateTime<Utc>>,
}

/// Response structure for user list
#[derive(Debug, Serialize)]
pub struct UserListResponse {
    pub users: Vec<UserResponse>,
    pub total_count: usize,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

/// Response structure for user information
#[derive(Debug, Serialize)]
pub struct UserResponse {
    pub id: Uuid,
    pub username: String,
    pub email: Option<String>,
    pub is_active: bool,
    pub is_admin: bool,
    pub is_banned: bool,
    pub ban_reason: Option<String>,
    pub banned_until: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login_at: Option<DateTime<Utc>>,
    pub max_concurrent_devices: i32,
    pub total_traffic_used: i64,
    pub last_traffic_reset: DateTime<Utc>,
    pub subscription_count: u32,
    pub active_subscription_count: u32,
}

impl From<users::Model> for UserResponse {
    fn from(user: users::Model) -> Self {
        Self {
            id: user.id,
            username: user.username,
            email: user.email,
            is_active: user.is_active,
            is_admin: user.is_admin,
            is_banned: user.is_banned,
            ban_reason: user.ban_reason,
            banned_until: user.banned_until.map(|dt| dt.into()),
            created_at: user.created_at.into(),
            updated_at: user.updated_at.into(),
            last_login_at: user.last_login_at.map(|dt| dt.into()),
            max_concurrent_devices: user.max_concurrent_devices,
            total_traffic_used: user.total_traffic_used,
            last_traffic_reset: user.last_traffic_reset.into(),
            subscription_count: 0, // Will be populated separately
            active_subscription_count: 0, // Will be populated separately
        }
    }
}

/// Query parameters for listing users
#[derive(Debug, Deserialize)]
pub struct ListUsersQuery {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub is_active: Option<bool>,
    pub is_admin: Option<bool>,
    pub is_banned: Option<bool>,
    pub search: Option<String>,
}

/// Response structure for user statistics
#[derive(Debug, Serialize)]
pub struct UserStatsResponse {
    pub total_users: u64,
    pub active_users: u64,
    pub banned_users: u64,
    pub admin_users: u64,
    pub users_with_active_subscriptions: u64,
    pub new_users_this_month: u64,
}

/// GET /api/admin/users - List all users with pagination and filtering
pub async fn list_users(
    State(db): State<DatabaseConnection>,
    Query(query): Query<ListUsersQuery>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<UserListResponse>>> {
    let user_repo = UserRepository::new(Arc::new(db.clone()));
    let subscription_repo = SubscriptionRepository::new(Arc::new(db));
    
    let page = query.page.unwrap_or(1);
    let page_size = query.page_size.unwrap_or(20).min(100); // Max 100 per page
    
    // Get users based on filters
    let users = if let Some(search) = &query.search {
        user_repo.search_users(search).await?
    } else if query.is_active == Some(true) {
        user_repo.find_active_users().await?
    } else if query.is_banned == Some(true) {
        user_repo.find_banned_users().await?
    } else if query.is_admin == Some(true) {
        user_repo.find_admin_users().await?
    } else {
        user_repo.find_all().await?
    };
    
    // Apply pagination
    let total_count = users.len();
    let total_pages = ((total_count as f64) / (page_size as f64)).ceil() as u32;
    let start_index = ((page - 1) * page_size) as usize;
    let end_index = (start_index + page_size as usize).min(total_count);
    
    let paginated_users = if start_index < total_count {
        &users[start_index..end_index]
    } else {
        &[]
    };
    
    // Convert to response format and add subscription counts
    let mut user_responses = Vec::new();
    for user in paginated_users {
        let mut user_response = UserResponse::from(user.clone());
        
        // Get subscription counts
        let subscriptions = subscription_repo.find_by_user_id(user.id).await?;
        user_response.subscription_count = subscriptions.len() as u32;
        user_response.active_subscription_count = subscriptions
            .iter()
            .filter(|s| s.is_active())
            .count() as u32;
        
        user_responses.push(user_response);
    }
    
    let response = UserListResponse {
        users: user_responses,
        total_count,
        page,
        page_size,
        total_pages,
    };
    
    Ok(Json(success(response)))
}

/// GET /api/admin/users/:id - Get specific user details
pub async fn get_user(
    State(db): State<DatabaseConnection>,
    Path(user_id): Path<Uuid>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<UserResponse>>> {
    let user_repo = UserRepository::new(Arc::new(db.clone()));
    let subscription_repo = SubscriptionRepository::new(Arc::new(db));
    
    let user = user_repo.find_by_id(user_id).await?
        .ok_or(AppError::not_found("User not found"))?;
    
    let mut user_response = UserResponse::from(user);
    
    // Get subscription counts
    let subscriptions = subscription_repo.find_by_user_id(user_id).await?;
    user_response.subscription_count = subscriptions.len() as u32;
    user_response.active_subscription_count = subscriptions
        .iter()
        .filter(|s| s.is_active())
        .count() as u32;
    
    Ok(Json(success(user_response)))
}

/// POST /api/admin/users - Create new user
pub async fn create_user(
    State(db): State<DatabaseConnection>,
    user_context: UserContext,
    Json(request): Json<CreateUserRequest>,
) -> AppResult<Json<ApiResponse<UserResponse>>> {
    let user_repo = UserRepository::new(Arc::new(db));
    
    // Check if username already exists
    if let Some(_) = user_repo.find_by_username(&request.username).await? {
        return Err(AppError::conflict("Username already exists"));
    }
    
    // Check if email already exists (if provided)
    if let Some(email) = &request.email {
        if let Some(_) = user_repo.find_by_email(email).await? {
            return Err(AppError::conflict("Email already exists"));
        }
    }
    
    // Hash password
    let password_hash = crate::auth::password_simple::hash_password(&request.password)
        .map_err(|e| AppError::internal(&format!("Failed to hash password: {}", e)))?;
    
    // Create new user
    let new_user = users::ActiveModel {
        id: Set(Uuid::new_v4()),
        username: Set(request.username),
        email: Set(request.email),
        password_hash: Set(password_hash),
        is_active: Set(request.is_active.unwrap_or(true)),
        is_admin: Set(request.is_admin.unwrap_or(false)),
        max_concurrent_devices: Set(request.max_concurrent_devices.unwrap_or(3)),
        is_banned: Set(false),
        ban_reason: Set(None),
        banned_until: Set(None),
        total_traffic_used: Set(0),
        last_login_at: Set(None),
        created_at: Set(Utc::now().into()),
        updated_at: Set(Utc::now().into()),
        last_traffic_reset: Set(Utc::now().into()),
    };
    
    let user = user_repo.create(new_user).await
        .map_err(|e| AppError::internal(&format!("Failed to create user: {}", e)))?;
    
    tracing::info!("Admin {} created user {}", user_context.username, user.username);
    
    let mut user_response = UserResponse::from(user);
    user_response.subscription_count = 0;
    user_response.active_subscription_count = 0;
    
    Ok(Json(success(user_response)))
}

/// PUT /api/admin/users/:id - Update user information
pub async fn update_user(
    State(db): State<DatabaseConnection>,
    Path(user_id): Path<Uuid>,
    user_context: UserContext,
    Json(request): Json<UpdateUserRequest>,
) -> AppResult<Json<ApiResponse<UserResponse>>> {
    let user_repo = UserRepository::new(Arc::new(db.clone()));
    let subscription_repo = SubscriptionRepository::new(Arc::new(db));
    
    // Check if user exists
    let existing_user = user_repo.find_by_id(user_id).await?
        .ok_or(AppError::not_found("User not found"))?;
    
    // Prevent self-modification of admin status
    if user_id == user_context.user_id && request.is_admin == Some(false) {
        return Err(AppError::authorization("Cannot remove your own admin status"));
    }
    
    // Check if username conflict exists (if username is being updated)
    if let Some(ref new_username) = request.username {
        if new_username != &existing_user.username {
            if let Some(_) = user_repo.find_by_username(new_username).await? {
                return Err(AppError::conflict("Username already exists"));
            }
        }
    }
    
    // Check if email conflict exists (if email is being updated)
    if let Some(ref new_email) = request.email {
        if Some(new_email) != existing_user.email.as_ref() {
            if let Some(_) = user_repo.find_by_email(new_email).await? {
                return Err(AppError::conflict("Email already exists"));
            }
        }
    }
    
    // Hash new password if provided
    let password_hash = if let Some(password) = &request.password {
        Some(crate::auth::password_simple::hash_password(password)
            .map_err(|e| AppError::internal(&format!("Failed to hash password: {}", e)))?)
    } else {
        None
    };
    
    // Create updated user
    let updated_user = users::ActiveModel {
        id: Set(user_id),
        username: Set(request.username.unwrap_or(existing_user.username)),
        email: Set(request.email.or(existing_user.email)),
        password_hash: Set(password_hash.unwrap_or(existing_user.password_hash)),
        is_active: Set(request.is_active.unwrap_or(existing_user.is_active)),
        is_admin: Set(request.is_admin.unwrap_or(existing_user.is_admin)),
        max_concurrent_devices: Set(request.max_concurrent_devices.unwrap_or(existing_user.max_concurrent_devices)),
        is_banned: Set(existing_user.is_banned),
        ban_reason: Set(existing_user.ban_reason),
        banned_until: Set(existing_user.banned_until),
        total_traffic_used: Set(existing_user.total_traffic_used),
        last_login_at: Set(existing_user.last_login_at),
        created_at: Set(existing_user.created_at),
        updated_at: Set(Utc::now().into()),
        last_traffic_reset: Set(existing_user.last_traffic_reset),
    };
    
    let user = user_repo.update(user_id, updated_user).await
        .map_err(|e| AppError::internal(&format!("Failed to update user: {}", e)))?;
    
    tracing::info!("Admin {} updated user {}", user_context.username, user.username);
    
    // Get subscription counts
    let subscriptions = subscription_repo.find_by_user_id(user_id).await?;
    let mut user_response = UserResponse::from(user);
    user_response.subscription_count = subscriptions.len() as u32;
    user_response.active_subscription_count = subscriptions
        .iter()
        .filter(|s| s.is_active())
        .count() as u32;
    
    Ok(Json(success(user_response)))
}

/// PUT /api/admin/users/:id/ban - Ban user
pub async fn ban_user(
    State(db): State<DatabaseConnection>,
    Path(user_id): Path<Uuid>,
    user_context: UserContext,
    Json(request): Json<BanUserRequest>,
) -> AppResult<Json<ApiResponse<UserResponse>>> {
    let user_repo = UserRepository::new(Arc::new(db.clone()));
    let subscription_repo = SubscriptionRepository::new(Arc::new(db));
    
    // Prevent self-banning
    if user_id == user_context.user_id {
        return Err(AppError::authorization("Cannot ban yourself"));
    }
    
    let user = user_repo.ban_user(user_id, request.reason, request.banned_until.map(|dt| dt.into())).await?;
    
    tracing::info!("Admin {} banned user {}", user_context.username, user.username);
    
    // Get subscription counts
    let subscriptions = subscription_repo.find_by_user_id(user_id).await?;
    let mut user_response = UserResponse::from(user);
    user_response.subscription_count = subscriptions.len() as u32;
    user_response.active_subscription_count = subscriptions
        .iter()
        .filter(|s| s.is_active())
        .count() as u32;
    
    Ok(Json(success(user_response)))
}

/// PUT /api/admin/users/:id/unban - Unban user
pub async fn unban_user(
    State(db): State<DatabaseConnection>,
    Path(user_id): Path<Uuid>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<UserResponse>>> {
    let user_repo = UserRepository::new(Arc::new(db.clone()));
    let subscription_repo = SubscriptionRepository::new(Arc::new(db));
    
    let user = user_repo.unban_user(user_id).await?;
    
    tracing::info!("Admin {} unbanned user {}", user_context.username, user.username);
    
    // Get subscription counts
    let subscriptions = subscription_repo.find_by_user_id(user_id).await?;
    let mut user_response = UserResponse::from(user);
    user_response.subscription_count = subscriptions.len() as u32;
    user_response.active_subscription_count = subscriptions
        .iter()
        .filter(|s| s.is_active())
        .count() as u32;
    
    Ok(Json(success(user_response)))
}

/// DELETE /api/admin/users/:id - Delete user
pub async fn delete_user(
    State(db): State<DatabaseConnection>,
    Path(user_id): Path<Uuid>,
    user_context: UserContext,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let user_repo = UserRepository::new(Arc::new(db));
    
    // Prevent self-deletion
    if user_id == user_context.user_id {
        return Err(AppError::authorization("Cannot delete yourself"));
    }
    
    // Check if user exists
    let user = user_repo.find_by_id(user_id).await?
        .ok_or(AppError::not_found("User not found"))?;
    
    // TODO: Check if user has active subscriptions before deleting
    // This would require implementing business logic for handling active subscriptions
    
    user_repo.delete(user_id).await
        .map_err(|e| AppError::internal(&format!("Failed to delete user: {}", e)))?;
    
    tracing::info!("Admin {} deleted user {}", user_context.username, user.username);
    
    Ok(Json(success(serde_json::json!({
        "message": "User deleted successfully",
        "user_id": user_id
    }))))
}

/// GET /api/admin/users/stats - Get user statistics
pub async fn get_user_stats(
    State(db): State<DatabaseConnection>,
    _user_context: UserContext,
) -> AppResult<Json<ApiResponse<UserStatsResponse>>> {
    let user_repo = UserRepository::new(Arc::new(db.clone()));
    let subscription_repo = SubscriptionRepository::new(Arc::new(db));
    
    let all_users = user_repo.find_all().await?;
    let total_users = all_users.len() as u64;
    
    let active_users = all_users.iter().filter(|u| u.is_active).count() as u64;
    let banned_users = all_users.iter().filter(|u| u.is_banned).count() as u64;
    let admin_users = all_users.iter().filter(|u| u.is_admin).count() as u64;
    
    // Count users with active subscriptions
    let mut users_with_active_subscriptions = 0;
    for user in &all_users {
        let subscriptions = subscription_repo.find_active_by_user_id(user.id).await?;
        if !subscriptions.is_empty() {
            users_with_active_subscriptions += 1;
        }
    }
    
    // Count new users this month
    let now = Utc::now();
    let month_start = now.date_naive().with_day(1).unwrap().and_hms_opt(0, 0, 0).unwrap();
    let new_users_this_month = all_users
        .iter()
        .filter(|u| {
            let created_date = u.created_at.date_naive();
            created_date >= month_start.date()
        })
        .count() as u64;
    
    let stats = UserStatsResponse {
        total_users,
        active_users,
        banned_users,
        admin_users,
        users_with_active_subscriptions,
        new_users_this_month,
    };
    
    Ok(Json(success(stats)))
}

/// Create admin user management routes
pub fn admin_user_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/", get(list_users).post(create_user))
        .route("/:id", get(get_user).put(update_user).delete(delete_user))
        .route("/:id/ban", put(ban_user))
        .route("/:id/unban", put(unban_user))
        .route("/stats", get(get_user_stats))
}