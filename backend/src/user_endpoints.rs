// User management API endpoints

use axum::{
    extract::{Request, State},
    response::Json,
    routing::get,
    Router,
};
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::auth::{extract_user_context, UserContext};
use crate::entities::{users, announcements, subscriptions, plans};
use std::sync::Arc;
use crate::errors::{AppError, AppResult, ApiResponse, success};
use crate::repositories::{
    UserRepository, Repository, AnnouncementRepository, SubscriptionRepository, PlanRepository, SystemSettingRepository
};
use crate::config_generator::{ConfigTemplateManager, GeneratedConfig};

/// User profile response structure
#[derive(Debug, Serialize)]
pub struct UserProfileResponse {
    pub id: Uuid,
    pub username: String,
    pub email: Option<String>,
    pub is_active: bool,
    pub is_admin: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_login_at: Option<chrono::DateTime<chrono::Utc>>,
    pub max_concurrent_devices: i32,
    pub is_banned: bool,
    pub ban_reason: Option<String>,
    pub banned_until: Option<chrono::DateTime<chrono::Utc>>,
    pub total_traffic_used: i64,
    pub last_traffic_reset: chrono::DateTime<chrono::Utc>,
    pub subscription_info: Option<UserSubscriptionInfo>,
}

/// User subscription information
#[derive(Debug, Serialize)]
pub struct UserSubscriptionInfo {
    pub id: Uuid,
    pub plan_name: String,
    pub status: String,
    pub start_date: chrono::DateTime<chrono::Utc>,
    pub end_date: chrono::DateTime<chrono::Utc>,
    pub total_traffic_gb: i32,
    pub used_traffic_gb: i32,
    pub remaining_traffic_gb: i32,
    pub device_limit: i32,
    pub is_active: bool,
    pub is_expired: bool,
    pub days_remaining: i64,
}

/// User configuration response
#[derive(Debug, Serialize)]
pub struct UserConfigResponse {
    pub user_id: Uuid,
    pub username: String,
    pub config: String,
    pub config_metadata: ConfigMetadata,
    pub server_endpoints: Vec<String>,
    pub subscription_valid: bool,
    pub subscription_expires_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// Configuration metadata for API response
#[derive(Debug, Serialize)]
pub struct ConfigMetadata {
    pub generated_at: chrono::DateTime<chrono::Utc>,
    pub template_version: String,
    pub placeholders_replaced: Vec<String>,
    pub config_hash: String,
}

/// User announcement response
#[derive(Debug, Serialize)]
pub struct UserAnnouncementResponse {
    pub id: Uuid,
    pub title: String,
    pub content: String,
    pub announcement_type: String,
    pub priority: i32,
    pub is_published: bool,
    pub published_at: Option<chrono::DateTime<chrono::Utc>>,
    pub expires_at: Option<chrono::DateTime<chrono::Utc>>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// GET /api/user/profile - Get user profile information
pub async fn get_user_profile(
    State(db): State<DatabaseConnection>,
    request: Request,
) -> AppResult<Json<ApiResponse<UserProfileResponse>>> {
    let user_context = extract_user_context(&request)
        .ok_or_else(|| AppError::auth("User not authenticated"))?;

    let user_repo = UserRepository::new(Arc::new(db.clone()));
    let subscription_repo = SubscriptionRepository::new(Arc::new(db.clone()));
    let plan_repo = PlanRepository::new(Arc::new(db));

    // Get user details
    let user = user_repo.find_by_id(user_context.user_id).await?
        .ok_or_else(|| AppError::not_found("User not found"))?;

    // Get user's current subscription
    let active_subscriptions = subscription_repo.find_active_by_user_id(user_context.user_id).await?;
    let subscription_info = if let Some(subscription) = active_subscriptions.first() {
        let plan = plan_repo.find_by_id(subscription.plan_id).await?
            .ok_or_else(|| AppError::not_found("Plan not found"))?;

        Some(UserSubscriptionInfo {
            id: subscription.id,
            plan_name: plan.name,
            status: format!("{:?}", subscription.status),
            start_date: subscription.start_date.into(),
            end_date: subscription.end_date.into(),
            total_traffic_gb: subscription.total_traffic_gb.to_string().parse::<i32>().unwrap_or(0),
            used_traffic_gb: subscription.used_traffic_gb.to_string().parse::<i32>().unwrap_or(0),
            remaining_traffic_gb: (subscription.total_traffic_gb - subscription.used_traffic_gb).to_string().parse::<i32>().unwrap_or(0),
            device_limit: subscription.max_concurrent_devices,
            is_active: subscription.is_active(),
            is_expired: subscription.is_expired(),
            days_remaining: subscription.remaining_days(),
        })
    } else {
        None
    };

    let profile = UserProfileResponse {
        id: user.id,
        username: user.username,
        email: user.email,
        is_active: user.is_active,
        is_admin: user.is_admin,
        created_at: user.created_at.into(),
        last_login_at: user.last_login_at.map(|dt| dt.into()),
        max_concurrent_devices: user.max_concurrent_devices,
        is_banned: user.is_banned,
        ban_reason: user.ban_reason,
        banned_until: user.banned_until.map(|dt| dt.into()),
        total_traffic_used: user.total_traffic_used,
        last_traffic_reset: user.last_traffic_reset.into(),
        subscription_info,
    };

    Ok(Json(success(profile)))
}

/// GET /api/user/config - Get user VPN configuration
pub async fn get_user_config(
    State(db): State<DatabaseConnection>,
    request: Request,
) -> AppResult<Json<ApiResponse<UserConfigResponse>>> {
    let user_context = extract_user_context(&request)
        .ok_or_else(|| AppError::auth("User not authenticated"))?;

    let user_repo = UserRepository::new(Arc::new(db.clone()));
    let subscription_repo = SubscriptionRepository::new(Arc::new(db.clone()));
    let plan_repo = PlanRepository::new(Arc::new(db.clone()));
    let system_settings_repo = SystemSettingRepository::new(Arc::new(db));

    // Get user details
    let user = user_repo.find_by_id(user_context.user_id).await?
        .ok_or_else(|| AppError::not_found("User not found"))?;

    // Check if user has an active subscription
    let active_subscriptions = subscription_repo.find_active_by_user_id(user_context.user_id).await?;
    let subscription = active_subscriptions.first();
    let plan = if let Some(sub) = subscription {
        let plan = plan_repo.find_by_id(sub.plan_id).await?
            .ok_or_else(|| AppError::not_found("Plan not found"))?;

        if !sub.is_active() {
            return Err(AppError::authorization("Subscription is not active"));
        }

        if sub.is_expired() {
            return Err(AppError::authorization("Subscription has expired"));
        }

        if sub.is_traffic_exceeded() {
            return Err(AppError::authorization("Traffic limit exceeded"));
        }

        Some(plan)
    } else {
        return Err(AppError::authorization("No active subscription found"));
    };

    // Generate configuration using the new system
    let config_manager = ConfigTemplateManager::new();
    let generated_config = config_manager.generate_user_config(
        &user,
        subscription,
        plan.as_ref(),
        &system_settings_repo,
    ).await?;

    // Get server endpoints from system settings or use defaults
    let server_endpoints = get_server_endpoints(&system_settings_repo).await?;

    let config = UserConfigResponse {
        user_id: user.id,
        username: user.username,
        config: generated_config.config,
        config_metadata: ConfigMetadata {
            generated_at: generated_config.metadata.generated_at,
            template_version: generated_config.metadata.template_version,
            placeholders_replaced: generated_config.metadata.placeholders_replaced,
            config_hash: generated_config.metadata.config_hash,
        },
        server_endpoints,
        subscription_valid: subscription.is_some(),
        subscription_expires_at: subscription.map(|s| s.end_date.into()),
    };

    Ok(Json(success(config)))
}

/// GET /api/user/announcements - Get user announcements
pub async fn get_user_announcements(
    State(db): State<DatabaseConnection>,
    request: Request,
) -> AppResult<Json<ApiResponse<Vec<UserAnnouncementResponse>>>> {
    let user_context = extract_user_context(&request)
        .ok_or_else(|| AppError::auth("User not authenticated"))?;

    let user_repo = UserRepository::new(Arc::new(db.clone()));
    let announcement_repo = AnnouncementRepository::new(Arc::new(db.clone()));
    let subscription_repo = SubscriptionRepository::new(Arc::new(db));

    // Get user details
    let _user = user_repo.find_by_id(user_context.user_id).await?
        .ok_or_else(|| AppError::not_found("User not found"))?;

    // Get user's subscription info for announcement targeting
    let active_subscriptions = subscription_repo.find_active_by_user_id(user_context.user_id).await?;
    let subscription = active_subscriptions.first();
    let user_plan_id = subscription.map(|s| s.plan_id);

    // Get announcements
    let announcements = announcement_repo.find_active_announcements().await?;

    let user_announcements: Vec<UserAnnouncementResponse> = announcements
        .into_iter()
        .filter(|announcement| {
            // Filter announcements based on user subscription status
            let subscription_status = if subscription.is_some() && subscription.as_ref().unwrap().is_active() {
                Some("active")
            } else {
                None
            };

            announcement.applies_to_user(subscription_status, user_plan_id)
        })
        .map(|announcement| UserAnnouncementResponse {
            id: announcement.id,
            title: announcement.title,
            content: announcement.content,
            announcement_type: format!("{:?}", announcement.announcement_type),
            priority: 1, // Default priority since field doesn't exist
            is_published: announcement.is_active,
            published_at: Some(announcement.publish_at.into()),
            expires_at: announcement.expires_at.map(|dt| dt.into()),
            created_at: announcement.created_at.into(),
        })
        .collect();

    Ok(Json(success(user_announcements)))
}

/// Get server endpoints from system settings or use defaults
async fn get_server_endpoints(
    system_settings_repo: &SystemSettingRepository,
) -> AppResult<Vec<String>> {
    if let Some(setting) = system_settings_repo.find_by_key("server_endpoints").await? {
        let endpoints = setting.value.unwrap_or_default();
        if !endpoints.is_empty() {
            return Ok(endpoints.split(',').map(|s| s.trim().to_string()).collect());
        }
    }
    
    // Default server endpoints
    Ok(vec![
        "server1.vpn.example.com:443".to_string(),
        "server2.vpn.example.com:443".to_string(),
    ])
}

/// Create user management routes
pub fn user_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/profile", get(get_user_profile))
        .route("/config", get(get_user_config))
        .route("/announcements", get(get_user_announcements))
}