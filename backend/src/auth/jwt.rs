// JWT token utilities - to be implemented in subtask 3.2

use jsonwebtoken::{decode, encode, Algorithm, <PERSON>od<PERSON><PERSON><PERSON>, <PERSON>co<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ida<PERSON>};
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};
use thiserror::Error;
use uuid::Uuid;

#[derive(Debug, Error)]
pub enum JwtError {
    #[error("Token encoding failed: {0}")]
    EncodingError(String),
    #[error("Token decoding failed: {0}")]
    DecodingError(String),
    #[error("Token validation failed: {0}")]
    ValidationError(String),
    #[error("Token expired")]
    TokenExpired,
    #[error("Invalid token")]
    InvalidToken,
}

pub type JwtResult<T> = Result<T, JwtError>;

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,      // Subject (user ID)
    pub exp: usize,       // Expiration time
    pub iat: usize,       // Issued at
    pub jti: String,      // JWT ID
    pub user_id: Uuid,    // User ID
    pub username: String, // Username
    pub role: String,     // User role (admin, user)
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RefreshTokenClaims {
    pub sub: String,      // Subject (user ID)
    pub exp: usize,       // Expiration time
    pub iat: usize,       // Issued at
    pub jti: String,      // JWT ID
    pub token_type: String, // "refresh"
}

pub struct JwtManager {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
    algorithm: Algorithm,
    access_token_duration: u64,  // seconds
    refresh_token_duration: u64, // seconds
}

impl JwtManager {
    pub fn new(secret: &str) -> Self {
        Self {
            encoding_key: EncodingKey::from_secret(secret.as_ref()),
            decoding_key: DecodingKey::from_secret(secret.as_ref()),
            algorithm: Algorithm::HS256,
            access_token_duration: 3600,        // 1 hour
            refresh_token_duration: 604800,     // 7 days
        }
    }

    pub fn with_durations(secret: &str, access_duration: u64, refresh_duration: u64) -> Self {
        Self {
            encoding_key: EncodingKey::from_secret(secret.as_ref()),
            decoding_key: DecodingKey::from_secret(secret.as_ref()),
            algorithm: Algorithm::HS256,
            access_token_duration: access_duration,
            refresh_token_duration: refresh_duration,
        }
    }

    /// Generate a new access token for a user
    pub fn generate_access_token(&self, user_id: Uuid, username: &str, role: &str) -> JwtResult<String> {
        let now = current_timestamp();
        let exp = now + self.access_token_duration as usize;
        
        let claims = Claims {
            sub: user_id.to_string(),
            exp,
            iat: now,
            jti: Uuid::new_v4().to_string(),
            user_id,
            username: username.to_string(),
            role: role.to_string(),
        };

        let header = Header::new(self.algorithm);
        
        encode(&header, &claims, &self.encoding_key)
            .map_err(|e| JwtError::EncodingError(e.to_string()))
    }

    /// Generate a new refresh token for a user
    pub fn generate_refresh_token(&self, user_id: Uuid) -> JwtResult<String> {
        let now = current_timestamp();
        let exp = now + self.refresh_token_duration as usize;
        
        let claims = RefreshTokenClaims {
            sub: user_id.to_string(),
            exp,
            iat: now,
            jti: Uuid::new_v4().to_string(),
            token_type: "refresh".to_string(),
        };

        let header = Header::new(self.algorithm);
        
        encode(&header, &claims, &self.encoding_key)
            .map_err(|e| JwtError::EncodingError(e.to_string()))
    }

    /// Validate an access token and return its claims
    pub fn validate_access_token(&self, token: &str) -> JwtResult<Claims> {
        let mut validation = Validation::new(self.algorithm);
        validation.leeway = 0; // No leeway for expiration
        
        let token_data = decode::<Claims>(token, &self.decoding_key, &validation)
            .map_err(|e| match e.kind() {
                jsonwebtoken::errors::ErrorKind::ExpiredSignature => JwtError::TokenExpired,
                _ => JwtError::DecodingError(e.to_string()),
            })?;

        Ok(token_data.claims)
    }

    /// Validate a refresh token and return its claims
    pub fn validate_refresh_token(&self, token: &str) -> JwtResult<RefreshTokenClaims> {
        let mut validation = Validation::new(self.algorithm);
        validation.leeway = 0; // No leeway for expiration
        
        let token_data = decode::<RefreshTokenClaims>(token, &self.decoding_key, &validation)
            .map_err(|e| match e.kind() {
                jsonwebtoken::errors::ErrorKind::ExpiredSignature => JwtError::TokenExpired,
                _ => JwtError::DecodingError(e.to_string()),
            })?;

        // Ensure it's actually a refresh token
        if token_data.claims.token_type != "refresh" {
            return Err(JwtError::InvalidToken);
        }

        Ok(token_data.claims)
    }

    /// Generate a new access token using a valid refresh token
    pub fn refresh_access_token(&self, refresh_token: &str, username: &str, role: &str) -> JwtResult<String> {
        let refresh_claims = self.validate_refresh_token(refresh_token)?;
        
        // Parse user ID from refresh token
        let user_id = Uuid::parse_str(&refresh_claims.sub)
            .map_err(|_| JwtError::InvalidToken)?;
        
        // Generate new access token
        self.generate_access_token(user_id, username, role)
    }

    /// Generate both access and refresh tokens for a user
    pub fn generate_token_pair(&self, user_id: Uuid, username: &str, role: &str) -> JwtResult<(String, String)> {
        let access_token = self.generate_access_token(user_id, username, role)?;
        let refresh_token = self.generate_refresh_token(user_id)?;
        
        Ok((access_token, refresh_token))
    }

    /// Get the access token duration in seconds
    pub fn access_token_duration(&self) -> u64 {
        self.access_token_duration
    }

    /// Get the refresh token duration in seconds
    pub fn refresh_token_duration(&self) -> u64 {
        self.refresh_token_duration
    }
}

fn current_timestamp() -> usize {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs() as usize
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_jwt_manager_creation() {
        let jwt_manager = JwtManager::new("test_secret");
        assert_eq!(jwt_manager.access_token_duration(), 3600);
        assert_eq!(jwt_manager.refresh_token_duration(), 604800);
    }

    #[test]
    fn test_jwt_manager_with_durations() {
        let jwt_manager = JwtManager::with_durations("test_secret", 1800, 86400);
        assert_eq!(jwt_manager.access_token_duration(), 1800);
        assert_eq!(jwt_manager.refresh_token_duration(), 86400);
    }

    #[test]
    fn test_access_token_generation_and_validation() {
        let jwt_manager = JwtManager::new("test_secret");
        let user_id = Uuid::new_v4();
        let username = "testuser";
        let role = "user";

        let token = jwt_manager.generate_access_token(user_id, username, role).unwrap();
        
        // Token should not be empty
        assert!(!token.is_empty());
        
        // Token should be validatable
        let claims = jwt_manager.validate_access_token(&token).unwrap();
        assert_eq!(claims.user_id, user_id);
        assert_eq!(claims.username, username);
        assert_eq!(claims.role, role);
        assert_eq!(claims.sub, user_id.to_string());
    }

    #[test]
    fn test_refresh_token_generation_and_validation() {
        let jwt_manager = JwtManager::new("test_secret");
        let user_id = Uuid::new_v4();

        let token = jwt_manager.generate_refresh_token(user_id).unwrap();
        
        // Token should not be empty
        assert!(!token.is_empty());
        
        // Token should be validatable
        let claims = jwt_manager.validate_refresh_token(&token).unwrap();
        assert_eq!(claims.sub, user_id.to_string());
        assert_eq!(claims.token_type, "refresh");
    }

    #[test]
    fn test_token_pair_generation() {
        let jwt_manager = JwtManager::new("test_secret");
        let user_id = Uuid::new_v4();
        let username = "testuser";
        let role = "user";

        let (access_token, refresh_token) = jwt_manager.generate_token_pair(user_id, username, role).unwrap();
        
        // Both tokens should be valid
        let access_claims = jwt_manager.validate_access_token(&access_token).unwrap();
        let refresh_claims = jwt_manager.validate_refresh_token(&refresh_token).unwrap();
        
        assert_eq!(access_claims.user_id, user_id);
        assert_eq!(refresh_claims.sub, user_id.to_string());
    }

    #[test]
    fn test_refresh_access_token() {
        let jwt_manager = JwtManager::new("test_secret");
        let user_id = Uuid::new_v4();
        let username = "testuser";
        let role = "user";

        let refresh_token = jwt_manager.generate_refresh_token(user_id).unwrap();
        let new_access_token = jwt_manager.refresh_access_token(&refresh_token, username, role).unwrap();
        
        // New access token should be valid
        let claims = jwt_manager.validate_access_token(&new_access_token).unwrap();
        assert_eq!(claims.user_id, user_id);
        assert_eq!(claims.username, username);
        assert_eq!(claims.role, role);
    }

    #[test]
    fn test_invalid_token_validation() {
        let jwt_manager = JwtManager::new("test_secret");
        
        // Invalid token should fail validation
        let result = jwt_manager.validate_access_token("invalid_token");
        assert!(result.is_err());
    }

    #[test]
    fn test_wrong_secret_validation() {
        let jwt_manager1 = JwtManager::new("secret1");
        let jwt_manager2 = JwtManager::new("secret2");
        
        let user_id = Uuid::new_v4();
        let token = jwt_manager1.generate_access_token(user_id, "testuser", "user").unwrap();
        
        // Token signed with different secret should fail validation
        let result = jwt_manager2.validate_access_token(&token);
        assert!(result.is_err());
    }

    #[test]
    fn test_expired_token_handling() {
        // Create JWT manager with very short expiration for testing
        let jwt_manager = JwtManager::with_durations("test_secret", 1, 1);
        let user_id = Uuid::new_v4();
        
        let token = jwt_manager.generate_access_token(user_id, "testuser", "user").unwrap();
        
        // Token should be expired after waiting
        std::thread::sleep(std::time::Duration::from_secs(2));
        let result = jwt_manager.validate_access_token(&token);
        
        // Verify token is properly expired
        assert!(matches!(result, Err(JwtError::TokenExpired)));
    }

    #[test]
    fn test_access_token_used_as_refresh_token() {
        let jwt_manager = JwtManager::new("test_secret");
        let user_id = Uuid::new_v4();
        
        let access_token = jwt_manager.generate_access_token(user_id, "testuser", "user").unwrap();
        
        // Using access token as refresh token should fail
        let result = jwt_manager.validate_refresh_token(&access_token);
        assert!(result.is_err());
    }
}