use bcrypt::{hash, verify, DEFAULT_COST};

/// Simple password hashing utility for subtask 3.1
pub fn hash_password(password: &str) -> Result<String, String> {
    hash(password, DEFAULT_COST).map_err(|e| e.to_string())
}

/// Simple password verification utility for subtask 3.1
pub fn verify_password(password: &str, hash: &str) -> Result<bool, String> {
    verify(password, hash).map_err(|e| e.to_string())
}

/// Basic password strength validation for subtask 3.1
pub fn is_password_strong(password: &str) -> bool {
    password.len() >= 8 
        && password.chars().any(|c| c.is_uppercase())
        && password.chars().any(|c| c.is_lowercase())
        && password.chars().any(|c| c.is_ascii_digit())
        && password.chars().any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hash_password() {
        let password = "TestPass123!";
        let hashed = hash_password(password).unwrap();
        
        assert_ne!(hashed, password);
        assert!(hashed.starts_with("$2b$"));
    }

    #[test]
    fn test_verify_password() {
        let password = "TestPass123!";
        let hashed = hash_password(password).unwrap();
        
        assert!(verify_password(password, &hashed).unwrap());
        assert!(!verify_password("WrongPass123!", &hashed).unwrap());
    }

    #[test]
    fn test_password_strength() {
        assert!(is_password_strong("StrongPass123!"));
        assert!(!is_password_strong("weak"));
        assert!(!is_password_strong("NoNumbers!"));
        assert!(!is_password_strong("nonumbers123!"));
        assert!(!is_password_strong("NOLOWERCASE123!"));
        assert!(!is_password_strong("NoSpecialChars123"));
    }
}