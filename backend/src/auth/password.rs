use bcrypt::{hash, verify, DEFAULT_COST};
use thiserror::Error;

#[derive(Debug, Error)]
pub enum PasswordError {
    #[error("Failed to hash password: {0}")]
    HashingError(String),
    #[error("Failed to verify password: {0}")]
    VerificationError(String),
}

pub type PasswordResult<T> = Result<T, PasswordError>;

/// Password hashing utility with bcrypt
pub struct PasswordHasher {
    cost: u32,
}

impl PasswordHasher {
    /// Create a new password hasher with default cost (12)
    pub fn new() -> Self {
        Self {
            cost: DEFAULT_COST,
        }
    }

    /// Create a new password hasher with custom cost
    /// Cost should be between 4-31, with higher values being more secure but slower
    /// - 10-12: Good for most applications
    /// - 13-15: High security requirements
    /// - 16+: Very high security but slow
    pub fn with_cost(cost: u32) -> Self {
        Self { cost }
    }

    /// Hash a password using bcrypt
    /// 
    /// # Arguments
    /// * `password` - The plain text password to hash
    /// 
    /// # Returns
    /// * `Ok(String)` - The hashed password
    /// * `Err(PasswordError)` - If hashing fails
    pub fn hash_password(&self, password: &str) -> PasswordResult<String> {
        hash(password, self.cost)
            .map_err(|e| PasswordError::HashingError(e.to_string()))
    }

    /// Verify a password against a hash
    /// 
    /// # Arguments
    /// * `password` - The plain text password to verify
    /// * `hash` - The hashed password to verify against
    /// 
    /// # Returns
    /// * `Ok(true)` - If password matches the hash
    /// * `Ok(false)` - If password doesn't match the hash
    /// * `Err(PasswordError)` - If verification fails
    pub fn verify_password(&self, password: &str, hash: &str) -> PasswordResult<bool> {
        verify(password, hash)
            .map_err(|e| PasswordError::VerificationError(e.to_string()))
    }
}

impl Default for PasswordHasher {
    fn default() -> Self {
        Self::new()
    }
}

/// Convenience function to hash a password with default settings
pub fn hash_password(password: &str) -> PasswordResult<String> {
    PasswordHasher::new().hash_password(password)
}

/// Convenience function to verify a password with default settings
pub fn verify_password(password: &str, hash: &str) -> PasswordResult<bool> {
    PasswordHasher::new().verify_password(password, hash)
}

/// Validate password strength
pub fn validate_password_strength(password: &str) -> Result<(), Vec<String>> {
    let mut errors = Vec::new();

    // Minimum length check
    if password.len() < 8 {
        errors.push("Password must be at least 8 characters long".to_string());
    }

    // Maximum length check (for security and performance)
    if password.len() > 128 {
        errors.push("Password must not exceed 128 characters".to_string());
    }

    // Character type requirements
    let has_lowercase = password.chars().any(|c| c.is_lowercase());
    let has_uppercase = password.chars().any(|c| c.is_uppercase());
    let has_digit = password.chars().any(|c| c.is_ascii_digit());
    let has_special = password.chars().any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c));

    if !has_lowercase {
        errors.push("Password must contain at least one lowercase letter".to_string());
    }

    if !has_uppercase {
        errors.push("Password must contain at least one uppercase letter".to_string());
    }

    if !has_digit {
        errors.push("Password must contain at least one digit".to_string());
    }

    if !has_special {
        errors.push("Password must contain at least one special character".to_string());
    }

    // Check for common weak passwords
    let weak_passwords = [
        "password", "123456", "password123", "admin", "qwerty", 
        "letmein", "welcome", "monkey", "1234567890", "password1"
    ];

    if weak_passwords.iter().any(|&weak| password.to_lowercase().contains(weak)) {
        errors.push("Password contains common weak patterns".to_string());
    }

    if errors.is_empty() {
        Ok(())
    } else {
        Err(errors)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_password_hashing() {
        let hasher = PasswordHasher::new();
        let password = "secure_password_123!";
        
        let hash = hasher.hash_password(password).unwrap();
        assert_ne!(hash, password);
        assert!(hash.starts_with("$2b$"));
    }

    #[test]
    fn test_password_verification() {
        let hasher = PasswordHasher::new();
        let password = "secure_password_123!";
        
        let hash = hasher.hash_password(password).unwrap();
        
        // Correct password should verify
        assert!(hasher.verify_password(password, &hash).unwrap());
        
        // Wrong password should not verify
        assert!(!hasher.verify_password("wrong_password", &hash).unwrap());
    }

    #[test]
    fn test_different_costs() {
        let hasher_low = PasswordHasher::with_cost(4);
        let hasher_high = PasswordHasher::with_cost(10);
        
        let password = "test_password";
        
        let hash_low = hasher_low.hash_password(password).unwrap();
        let hash_high = hasher_high.hash_password(password).unwrap();
        
        // Both should verify correctly
        assert!(hasher_low.verify_password(password, &hash_low).unwrap());
        assert!(hasher_high.verify_password(password, &hash_high).unwrap());
        
        // Hashes should be different
        assert_ne!(hash_low, hash_high);
    }

    #[test]
    fn test_convenience_functions() {
        let password = "test_password_123!";
        
        let hash = hash_password(password).unwrap();
        assert!(verify_password(password, &hash).unwrap());
        assert!(!verify_password("wrong", &hash).unwrap());
    }

    #[test]
    fn test_password_strength_validation() {
        // Valid strong password
        assert!(validate_password_strength("StrongPass123!").is_ok());
        
        // Too short
        assert!(validate_password_strength("Sh0rt!").is_err());
        
        // Missing uppercase
        assert!(validate_password_strength("lowercase123!").is_err());
        
        // Missing lowercase
        assert!(validate_password_strength("UPPERCASE123!").is_err());
        
        // Missing digit
        assert!(validate_password_strength("NoDigits!").is_err());
        
        // Missing special character
        assert!(validate_password_strength("NoSpecial123").is_err());
        
        // Common weak password
        assert!(validate_password_strength("password123").is_err());
    }
}