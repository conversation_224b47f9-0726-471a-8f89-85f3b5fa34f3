// Authentication middleware implementation

use axum::{
    extract::{Request, State, FromRequestParts},
    http::{header, StatusCode, request::Parts},
    middleware::Next,
    response::Response,
};
use std::sync::Arc;

use super::jwt::{Jwt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};

// Authentication middleware that validates JWT tokens and extracts user context
pub async fn auth_middleware(
    State(jwt_manager): State<Arc<JwtManager>>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Extract Authorization header
    let auth_header = request
        .headers()
        .get(header::AUTHORIZATION)
        .and_then(|header| header.to_str().ok());

    let token = match auth_header {
        Some(header) => extract_jwt_from_header(header),
        None => return Err(StatusCode::UNAUTHORIZED),
    };

    let token = match token {
        Some(token) => token,
        None => return Err(StatusCode::UNAUTHORIZED),
    };

    // Validate the token
    let claims = match jwt_manager.validate_access_token(token) {
        Ok(claims) => claims,
        Err(_) => return Err(StatusCode::UNAUTHORIZED),
    };

    // Add user context to request extensions
    let user_context = UserContext::from(claims);
    request.extensions_mut().insert(user_context);

    // Continue with the request
    Ok(next.run(request).await)
}

// Role-based authorization middleware that checks for admin role
pub async fn admin_middleware(
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Extract user context from extensions (should be set by auth_middleware)
    let user_context = request.extensions().get::<UserContext>().cloned();

    match user_context {
        Some(context) => {
            // Check if user has admin role
            if context.role == "admin" {
                Ok(next.run(request).await)
            } else {
                tracing::warn!("User {} is not an admin", context.username);
                Err(StatusCode::FORBIDDEN)
            }
        }
        None => {
            // No user context found, user is not authenticated
            Err(StatusCode::UNAUTHORIZED)
        }
    }
}

// Helper function to extract JWT from Authorization header
fn extract_jwt_from_header(auth_header: &str) -> Option<&str> {
    // Authorization header format: "Bearer <token>"
    if auth_header.starts_with("Bearer ") {
        Some(&auth_header[7..]) // Skip "Bearer " prefix
    } else {
        None
    }
}

// Helper struct to store user context in request extensions
#[derive(Clone, Debug)]
pub struct UserContext {
    pub user_id: uuid::Uuid,
    pub username: String,
    pub role: String,
}

impl From<Claims> for UserContext {
    fn from(claims: Claims) -> Self {
        Self {
            user_id: claims.user_id,
            username: claims.username,
            role: claims.role,
        }
    }
}

// Implement FromRequestParts for UserContext to enable extraction in handlers
#[axum::async_trait]
impl<S> FromRequestParts<S> for UserContext
where
    S: Send + Sync,
{
    type Rejection = StatusCode;

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        parts
            .extensions
            .get::<UserContext>()
            .cloned()
            .ok_or(StatusCode::UNAUTHORIZED)
    }
}

// Helper function to extract user context from request extensions
pub fn extract_user_context(request: &Request) -> Option<UserContext> {
    request.extensions().get::<UserContext>().cloned()
}

// Helper function to require user context from request extensions
pub fn require_user_context(request: &Request) -> Result<UserContext, StatusCode> {
    extract_user_context(request).ok_or(StatusCode::UNAUTHORIZED)
}

// Helper function to require admin context from request extensions
pub fn require_admin_context(request: &Request) -> Result<UserContext, StatusCode> {
    let context = require_user_context(request)?;
    if context.role == "admin" {
        Ok(context)
    } else {
        Err(StatusCode::FORBIDDEN)
    }
}

// Usage examples:
// 
// For authenticated routes:
// router.route("/protected", get(handler))
//     .layer(axum::middleware::from_fn_with_state(jwt_manager.clone(), auth_middleware))
//
// For admin-only routes:
// router.route("/admin", get(handler))
//     .layer(axum::middleware::from_fn_with_state(jwt_manager.clone(), auth_middleware))
//     .layer(axum::middleware::from_fn(admin_middleware))
//
// Or apply to a group of routes:
// router.nest("/api/protected", protected_routes)
//     .layer(axum::middleware::from_fn_with_state(jwt_manager.clone(), auth_middleware))

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::{Request, Method};
    use axum::body::Body;
    use std::sync::Arc;
    use uuid::Uuid;

    fn create_test_jwt_manager() -> Arc<JwtManager> {
        Arc::new(JwtManager::new("test_secret"))
    }

    fn create_test_request_with_auth(token: &str) -> Request<Body> {
        Request::builder()
            .method(Method::GET)
            .uri("/")
            .header("Authorization", format!("Bearer {}", token))
            .body(Body::empty())
            .unwrap()
    }

    fn create_test_request_without_auth() -> Request<Body> {
        Request::builder()
            .method(Method::GET)
            .uri("/")
            .body(Body::empty())
            .unwrap()
    }


    #[test]
    fn test_extract_jwt_from_header_valid_bearer() {
        let header = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9";
        let token = extract_jwt_from_header(header);
        assert_eq!(token, Some("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"));
    }

    #[test]
    fn test_extract_jwt_from_header_invalid_format() {
        let header = "InvalidFormat token";
        let token = extract_jwt_from_header(header);
        assert_eq!(token, None);
    }

    #[test]
    fn test_extract_jwt_from_header_missing_token() {
        let header = "Bearer ";
        let token = extract_jwt_from_header(header);
        assert_eq!(token, Some(""));
    }

    #[test]
    fn test_extract_jwt_from_header_only_bearer() {
        let header = "Bearer";
        let token = extract_jwt_from_header(header);
        assert_eq!(token, None);
    }


    #[test]
    fn test_user_context_from_claims() {
        let user_id = Uuid::new_v4();
        let username = "testuser";
        let role = "user";

        let claims = Claims {
            sub: user_id.to_string(),
            exp: 1234567890,
            iat: 1234567890,
            jti: "test_jti".to_string(),
            user_id,
            username: username.to_string(),
            role: role.to_string(),
        };

        let user_context = UserContext::from(claims);
        assert_eq!(user_context.user_id, user_id);
        assert_eq!(user_context.username, username);
        assert_eq!(user_context.role, role);
    }

    #[test]
    fn test_extract_user_context() {
        let user_id = Uuid::new_v4();
        let username = "testuser";
        let role = "user";

        let user_context = UserContext {
            user_id,
            username: username.to_string(),
            role: role.to_string(),
        };

        let mut request = create_test_request_without_auth();
        request.extensions_mut().insert(user_context.clone());

        let extracted = extract_user_context(&request);
        assert!(extracted.is_some());
        let extracted = extracted.unwrap();
        assert_eq!(extracted.user_id, user_id);
        assert_eq!(extracted.username, username);
        assert_eq!(extracted.role, role);
    }

    #[test]
    fn test_require_user_context_success() {
        let user_id = Uuid::new_v4();
        let username = "testuser";
        let role = "user";

        let user_context = UserContext {
            user_id,
            username: username.to_string(),
            role: role.to_string(),
        };

        let mut request = create_test_request_without_auth();
        request.extensions_mut().insert(user_context.clone());

        let result = require_user_context(&request);
        assert!(result.is_ok());
        let context = result.unwrap();
        assert_eq!(context.user_id, user_id);
        assert_eq!(context.username, username);
        assert_eq!(context.role, role);
    }

    #[test]
    fn test_require_user_context_failure() {
        let request = create_test_request_without_auth();

        let result = require_user_context(&request);
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), StatusCode::UNAUTHORIZED);
    }

    #[test]
    fn test_require_admin_context_success() {
        let user_id = Uuid::new_v4();
        let username = "admin";
        let role = "admin";

        let user_context = UserContext {
            user_id,
            username: username.to_string(),
            role: role.to_string(),
        };

        let mut request = create_test_request_without_auth();
        request.extensions_mut().insert(user_context.clone());

        let result = require_admin_context(&request);
        assert!(result.is_ok());
        let context = result.unwrap();
        assert_eq!(context.user_id, user_id);
        assert_eq!(context.username, username);
        assert_eq!(context.role, role);
    }

    #[test]
    fn test_require_admin_context_forbidden() {
        let user_id = Uuid::new_v4();
        let username = "user";
        let role = "user";

        let user_context = UserContext {
            user_id,
            username: username.to_string(),
            role: role.to_string(),
        };

        let mut request = create_test_request_without_auth();
        request.extensions_mut().insert(user_context.clone());

        let result = require_admin_context(&request);
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), StatusCode::FORBIDDEN);
    }

    #[test]
    fn test_require_admin_context_unauthorized() {
        let request = create_test_request_without_auth();

        let result = require_admin_context(&request);
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), StatusCode::UNAUTHORIZED);
    }
}