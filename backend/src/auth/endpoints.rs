use axum::{
    extract::State,
    http::StatusCode,
    response::<PERSON><PERSON>,
    routing::post,
    Router,
};
use sea_orm::{DatabaseConnection, EntityTrait, QueryFilter, ColumnTrait, Set, ActiveModelTrait};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::Utc;
use validator::{Validate, ValidationError};

use crate::entities::{users, prelude::*};
use super::password_simple::{hash_password, is_password_strong, verify_password};
use super::jwt::JwtManager;

#[derive(Debug, Deserialize, Validate)]
pub struct RegisterRequest {
    #[validate(length(min = 3, max = 50, message = "Username must be between 3 and 50 characters"))]
    pub username: String,
    
    #[validate(email(message = "Invalid email format"))]
    pub email: Option<String>,
    
    #[validate(length(min = 8, message = "Password must be at least 8 characters"))]
    #[validate(custom = "validate_password_strength")]
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct RegisterResponse {
    pub success: bool,
    pub message: String,
    pub user_id: Option<Uuid>,
}

#[derive(Debug, Serialize)]
pub struct ErrorResponse {
    pub error: String,
    pub details: Option<String>,
}

// Custom password strength validator
fn validate_password_strength(password: &str) -> Result<(), ValidationError> {
    if is_password_strong(password) {
        Ok(())
    } else {
        Err(ValidationError::new("Password must contain at least 8 characters with uppercase, lowercase, digit, and special character"))
    }
}

#[derive(Debug, Deserialize, Validate)]
pub struct LoginRequest {
    #[validate(length(min = 3, max = 50, message = "Username must be between 3 and 50 characters"))]
    pub username: String,
    
    #[validate(length(min = 1, message = "Password is required"))]
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub success: bool,
    pub message: String,
    pub access_token: Option<String>,
    pub refresh_token: Option<String>,
    pub user_id: Option<Uuid>,
    pub username: Option<String>,
}

pub async fn register(
    State(db): State<DatabaseConnection>,
    Json(payload): Json<RegisterRequest>,
) -> Result<Json<RegisterResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate input
    if let Err(validation_errors) = payload.validate() {
        let error_messages: Vec<String> = validation_errors
            .field_errors()
            .iter()
            .flat_map(|(field, errors)| {
                let field_name = field.to_string();
                errors.iter().map(move |error| {
                    format!("{}: {}", field_name, error.message.as_ref().unwrap_or(&"Invalid value".into()))
                })
            })
            .collect();
        
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Validation failed".to_string(),
                details: Some(error_messages.join(", ")),
            }),
        ));
    }

    // Check if username already exists
    let existing_user = Users::find()
        .filter(users::Column::Username.eq(&payload.username))
        .one(&db)
        .await;

    match existing_user {
        Ok(Some(_)) => {
            return Err((
                StatusCode::CONFLICT,
                Json(ErrorResponse {
                    error: "Username already exists".to_string(),
                    details: None,
                }),
            ));
        }
        Err(e) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Database error".to_string(),
                    details: Some(e.to_string()),
                }),
            ));
        }
        _ => {}
    }

    // Check if email already exists (if provided)
    if let Some(ref email) = payload.email {
        let existing_email = Users::find()
            .filter(users::Column::Email.eq(email))
            .one(&db)
            .await;

        match existing_email {
            Ok(Some(_)) => {
                return Err((
                    StatusCode::CONFLICT,
                    Json(ErrorResponse {
                        error: "Email already exists".to_string(),
                        details: None,
                    }),
                ));
            }
            Err(e) => {
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse {
                        error: "Database error".to_string(),
                        details: Some(e.to_string()),
                    }),
                ));
            }
            _ => {}
        }
    }

    // Hash password
    let password_hash = match hash_password(&payload.password) {
        Ok(hash) => hash,
        Err(e) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Password hashing failed".to_string(),
                    details: Some(e),
                }),
            ));
        }
    };

    // Create new user
    let user_id = Uuid::new_v4();
    let now = Utc::now();
    
    let new_user = users::ActiveModel {
        id: Set(user_id),
        username: Set(payload.username),
        email: Set(payload.email),
        password_hash: Set(password_hash),
        is_active: Set(true),
        is_admin: Set(false),
        created_at: Set(now.into()),
        updated_at: Set(now.into()),
        last_login_at: Set(None),
        max_concurrent_devices: Set(3),
        is_banned: Set(false),
        ban_reason: Set(None),
        banned_until: Set(None),
        total_traffic_used: Set(0),
        last_traffic_reset: Set(now.into()),
    };

    // Insert user into database
    match new_user.insert(&db).await {
        Ok(_) => {
            Ok(Json(RegisterResponse {
                success: true,
                message: "User registered successfully".to_string(),
                user_id: Some(user_id),
            }))
        }
        Err(e) => {
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to create user".to_string(),
                    details: Some(e.to_string()),
                }),
            ))
        }
    }
}

pub async fn login(
    State(db): State<DatabaseConnection>,
    Json(payload): Json<LoginRequest>,
) -> Result<Json<LoginResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate input
    if let Err(validation_errors) = payload.validate() {
        let error_messages: Vec<String> = validation_errors
            .field_errors()
            .iter()
            .flat_map(|(field, errors)| {
                let field_name = field.to_string();
                errors.iter().map(move |error| {
                    format!("{}: {}", field_name, error.message.as_ref().unwrap_or(&"Invalid value".into()))
                })
            })
            .collect();
        
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Validation failed".to_string(),
                details: Some(error_messages.join(", ")),
            }),
        ));
    }

    // Find user by username
    let user = Users::find()
        .filter(users::Column::Username.eq(&payload.username))
        .one(&db)
        .await;

    let user = match user {
        Ok(Some(user)) => user,
        Ok(None) => {
            return Err((
                StatusCode::UNAUTHORIZED,
                Json(ErrorResponse {
                    error: "Invalid credentials".to_string(),
                    details: None,
                }),
            ));
        }
        Err(e) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Database error".to_string(),
                    details: Some(e.to_string()),
                }),
            ));
        }
    };

    // Verify password
    let password_valid = match verify_password(&payload.password, &user.password_hash) {
        Ok(valid) => valid,
        Err(e) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Password verification failed".to_string(),
                    details: Some(e),
                }),
            ));
        }
    };

    if !password_valid {
        return Err((
            StatusCode::UNAUTHORIZED,
            Json(ErrorResponse {
                error: "Invalid credentials".to_string(),
                details: None,
            }),
        ));
    }

    // Check if user is banned
    if user.is_banned {
        let ban_message = if let Some(banned_until) = user.banned_until {
            format!("Account banned until {}. Reason: {}", 
                   banned_until.format("%Y-%m-%d %H:%M:%S"), 
                   user.ban_reason.as_deref().unwrap_or("No reason provided"))
        } else {
            format!("Account permanently banned. Reason: {}", 
                   user.ban_reason.as_deref().unwrap_or("No reason provided"))
        };
        
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "Account banned".to_string(),
                details: Some(ban_message),
            }),
        ));
    }

    // Check if user is active
    if !user.is_active {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "Account inactive".to_string(),
                details: Some("Your account has been deactivated. Please contact support.".to_string()),
            }),
        ));
    }

    // Generate JWT tokens
    let jwt_secret = std::env::var("JWT_SECRET").unwrap_or_else(|_| "default_secret_key".to_string());
    let jwt_manager = JwtManager::new(&jwt_secret);
    
    let role = if user.is_admin { "admin" } else { "user" };
    
    // Store user info before conversion
    let user_id = user.id;
    let username = user.username.clone();
    
    match jwt_manager.generate_token_pair(user_id, &username, role) {
        Ok((access_token, refresh_token)) => {
            // Update last login time
            let mut user_update: users::ActiveModel = user.into();
            user_update.last_login_at = Set(Some(Utc::now().into()));
            
            if let Err(e) = user_update.update(&db).await {
                // Log the error but don't fail the login
                tracing::warn!("Failed to update last login time for user {}: {}", payload.username, e);
            }
            
            Ok(Json(LoginResponse {
                success: true,
                message: "Login successful".to_string(),
                access_token: Some(access_token),
                refresh_token: Some(refresh_token),
                user_id: Some(user_id),
                username: Some(username),
            }))
        }
        Err(e) => {
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Token generation failed".to_string(),
                    details: Some(e.to_string()),
                }),
            ))
        }
    }
}

pub fn auth_routes() -> Router<DatabaseConnection> {
    Router::new()
        .route("/register", post(register))
        .route("/login", post(login))
}