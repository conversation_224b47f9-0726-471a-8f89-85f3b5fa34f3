// Common error types and response structures for the VPN backend API

use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    J<PERSON>,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Standard API error response structure
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiError {
    pub error: String,
    pub message: String,
    pub status_code: u16,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub validation_errors: Option<HashMap<String, Vec<String>>>,
}

/// Standard API success response structure
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: T,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
}

/// Application-specific error types
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sea_orm::DbErr),
    
    #[error("Repository error: {0}")]
    Repository(#[from] crate::repositories::RepositoryError),
    
    #[error("Authentication error: {0}")]
    Auth(String),
    
    #[error("Authorization error: {0}")]
    Authorization(String),
    
    #[error("Validation error: {0}")]
    Validation(String),
    
    #[error("Not found: {0}")]
    NotFound(String),
    
    #[error("Conflict: {0}")]
    Conflict(String),
    
    #[error("Internal server error: {0}")]
    Internal(String),
    
    #[error("Bad request: {0}")]
    BadRequest(String),
    
    #[error("Configuration error: {0}")]
    Configuration(String),
    
    #[error("External service error: {0}")]
    External(String),
}

impl AppError {
    /// Create a new authentication error
    pub fn auth(msg: impl Into<String>) -> Self {
        Self::Auth(msg.into())
    }
    
    /// Create a new authorization error
    pub fn authorization(msg: impl Into<String>) -> Self {
        Self::Authorization(msg.into())
    }
    
    /// Create a new validation error
    pub fn validation(msg: impl Into<String>) -> Self {
        Self::Validation(msg.into())
    }
    
    /// Create a new not found error
    pub fn not_found(msg: impl Into<String>) -> Self {
        Self::NotFound(msg.into())
    }
    
    /// Create a new conflict error
    pub fn conflict(msg: impl Into<String>) -> Self {
        Self::Conflict(msg.into())
    }
    
    /// Create a new internal server error
    pub fn internal(msg: impl Into<String>) -> Self {
        Self::Internal(msg.into())
    }
    
    /// Create a new bad request error
    pub fn bad_request(msg: impl Into<String>) -> Self {
        Self::BadRequest(msg.into())
    }
    
    /// Create a new configuration error
    pub fn configuration(msg: impl Into<String>) -> Self {
        Self::Configuration(msg.into())
    }
    
    /// Create a new external service error
    pub fn external(msg: impl Into<String>) -> Self {
        Self::External(msg.into())
    }
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status_code, error_type, message) = match self {
            AppError::Database(ref err) => {
                tracing::error!("Database error: {}", err);
                (StatusCode::INTERNAL_SERVER_ERROR, "DATABASE_ERROR", "Internal server error")
            }
            AppError::Repository(ref err) => {
                tracing::error!("Repository error: {}", err);
                (StatusCode::INTERNAL_SERVER_ERROR, "REPOSITORY_ERROR", "Internal server error")
            }
            AppError::Auth(ref msg) => {
                tracing::warn!("Authentication error: {}", msg);
                (StatusCode::UNAUTHORIZED, "AUTH_ERROR", msg.as_str())
            }
            AppError::Authorization(ref msg) => {
                tracing::warn!("Authorization error: {}", msg);
                (StatusCode::FORBIDDEN, "AUTHORIZATION_ERROR", msg.as_str())
            }
            AppError::Validation(ref msg) => {
                tracing::info!("Validation error: {}", msg);
                (StatusCode::BAD_REQUEST, "VALIDATION_ERROR", msg.as_str())
            }
            AppError::NotFound(ref msg) => {
                tracing::info!("Not found error: {}", msg);
                (StatusCode::NOT_FOUND, "NOT_FOUND", msg.as_str())
            }
            AppError::Conflict(ref msg) => {
                tracing::info!("Conflict error: {}", msg);
                (StatusCode::CONFLICT, "CONFLICT", msg.as_str())
            }
            AppError::Internal(ref msg) => {
                tracing::error!("Internal server error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, "INTERNAL_ERROR", "Internal server error")
            }
            AppError::BadRequest(ref msg) => {
                tracing::info!("Bad request error: {}", msg);
                (StatusCode::BAD_REQUEST, "BAD_REQUEST", msg.as_str())
            }
            AppError::Configuration(ref msg) => {
                tracing::error!("Configuration error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, "CONFIGURATION_ERROR", "Configuration error")
            }
            AppError::External(ref msg) => {
                tracing::error!("External service error: {}", msg);
                (StatusCode::BAD_GATEWAY, "EXTERNAL_ERROR", "External service error")
            }
        };

        let api_error = ApiError {
            error: error_type.to_string(),
            message: message.to_string(),
            status_code: status_code.as_u16(),
            details: None,
            validation_errors: None,
        };

        (status_code, Json(api_error)).into_response()
    }
}

/// Helper function to create a successful API response
pub fn success<T>(data: T) -> ApiResponse<T> {
    ApiResponse {
        success: true,
        data,
        message: None,
    }
}

/// Helper function to create a successful API response with message
pub fn success_with_message<T>(data: T, message: impl Into<String>) -> ApiResponse<T> {
    ApiResponse {
        success: true,
        data,
        message: Some(message.into()),
    }
}

/// Helper function to create validation error with detailed field errors
pub fn validation_error_with_details(
    message: impl Into<String>,
    validation_errors: HashMap<String, Vec<String>>,
) -> Response {
    let api_error = ApiError {
        error: "VALIDATION_ERROR".to_string(),
        message: message.into(),
        status_code: 400,
        details: None,
        validation_errors: Some(validation_errors),
    };

    (StatusCode::BAD_REQUEST, Json(api_error)).into_response()
}

/// Type alias for Result with AppError
pub type AppResult<T> = Result<T, AppError>;