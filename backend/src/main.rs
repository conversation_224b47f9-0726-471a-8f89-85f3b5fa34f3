use axum::{extract::Request, http::StatusCode, response::Json, routing::get, Router};
use serde_json::{json, Value};
use std::sync::Arc;
use std::time::Duration;
use tokio::signal;
use tower::ServiceBuilder;
use tower_http::{
    cors::{Any, CorsLayer},
    trace::TraceLayer,
};
use tracing_subscriber;

mod admin_expiration_endpoints;
mod admin_order_endpoints;
mod admin_user_management;
mod auth;
mod config_endpoints;
mod config_generator;
mod config_template_endpoints;
mod database;
mod entities;
mod errors;
mod order_endpoints;
mod plan_endpoints;
mod repositories;
mod services;
mod subscription_endpoints;
mod traffic_endpoints;
mod user_endpoints;

#[tokio::main]
async fn main() {
    // Load environment variables from .env file
    dotenv::dotenv().ok();

    tracing_subscriber::fmt::init();

    // Initialize database connection
    let db = match database::initialize_database().await {
        Ok(db) => db,
        Err(e) => {
            tracing::error!("Failed to initialize database: {}", e);
            std::process::exit(1);
        }
    };

    tracing::info!("Database initialized successfully");

    // Create JWT manager for middleware
    let jwt_secret =
        std::env::var("JWT_SECRET").unwrap_or_else(|_| "default_secret_key".to_string());
    let jwt_manager = Arc::new(auth::JwtManager::new(&jwt_secret));

    // Start traffic management service
    let traffic_service = services::traffic_management::TrafficManagementService::new(Arc::new(db.clone()));
    let traffic_service_clone = Arc::new(traffic_service);
    
    // Start traffic sync task in background
    let traffic_service_task = traffic_service_clone.clone();
    tokio::spawn(async move {
        traffic_service_task.start_sync_task().await;
    });

    // Start monthly reset task in background
    let traffic_service_monthly = traffic_service_clone.clone();
    tokio::spawn(async move {
        traffic_service_monthly.start_monthly_reset_task().await;
    });

    // Start order completion service for expired orders processing
    let order_completion_service = services::order_completion::OrderCompletionService::new(Arc::new(db.clone()));
    order_completion_service.start_expired_orders_task().await;

    // Start subscription expiration service
    let expiration_service = services::subscription_expiration::SubscriptionExpirationService::new(Arc::new(db.clone()));
    let expiration_service_clone = Arc::new(expiration_service);
    
    // Start expiration task in background
    let expiration_service_task = expiration_service_clone.clone();
    tokio::spawn(async move {
        expiration_service_task.start_expiration_task().await;
    });

    // Create protected routes with authentication middleware
    let user_routes = Router::new()
        .nest("/", user_endpoints::user_routes())
        .nest("/orders", order_endpoints::order_routes())
        .nest("/subscriptions", subscription_endpoints::user_subscription_routes())
        .nest("/traffic", traffic_endpoints::user_traffic_routes())
        .layer(axum::middleware::from_fn_with_state(
            jwt_manager.clone(),
            auth::auth_middleware,
        ));

    // Create public routes (no authentication required)
    let public_routes = Router::new().nest("/plans", plan_endpoints::public_plan_routes());

    // Create admin routes with both auth and admin middleware
    let admin_routes = Router::new()
        .nest("/plans", plan_endpoints::admin_plan_routes())
        .nest("/config", config_endpoints::config_routes())
        .nest(
            "/config-templates",
            config_template_endpoints::admin_config_template_routes(),
        )
        .nest("/users", admin_user_management::admin_user_routes())
        .nest("/orders", admin_order_endpoints::admin_order_routes())
        .nest("/subscriptions", subscription_endpoints::admin_subscription_routes())
        .nest("/traffic", traffic_endpoints::admin_traffic_routes())
        .nest("/expiration", admin_expiration_endpoints::admin_expiration_routes())
        .layer(axum::middleware::from_fn(auth::admin_middleware))
        .layer(axum::middleware::from_fn_with_state(
            jwt_manager.clone(),
            auth::auth_middleware,
        ));

    // Configure CORS middleware
    let cors = CorsLayer::new()
        .allow_origin(Any)
        .allow_methods([
            axum::http::Method::GET,
            axum::http::Method::POST,
            axum::http::Method::PUT,
            axum::http::Method::DELETE,
            axum::http::Method::OPTIONS,
        ])
        .allow_headers(Any)
        .max_age(Duration::from_secs(3600));

    let app = Router::new()
        .route("/", get(health_check))
        .route("/health", get(health_check))
        .nest("/api/auth", auth::auth_routes())
        .nest("/api/user", user_routes)
        .nest("/api/admin", admin_routes)
        .nest("/api/pub", public_routes)
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(cors),
        )
        .with_state(db);

    // Get server configuration from environment variables
    let host = std::env::var("SERVER_HOST").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port = std::env::var("SERVER_PORT").unwrap_or_else(|_| "3000".to_string());
    let bind_address = format!("{}:{}", host, port);
    println!("Binding to address: {}: {}", host, port);
    let listener = tokio::net::TcpListener::bind(&bind_address)
        .await
        .expect("Failed to bind to address");
    tracing::info!(
        "Backend server listening on {}",
        listener.local_addr().unwrap()
    );

    // Run server with graceful shutdown
    let result = axum::serve(listener, app)
        .with_graceful_shutdown(shutdown_signal())
        .await;

    tracing::info!("Backend server shutdown complete result: {:?}", result);
}

async fn health_check() -> Json<errors::ApiResponse<serde_json::Value>> {
    Json(errors::success(json!({
        "status": "ok",
        "service": "vpn-backend",
        "version": "0.1.0",
        "timestamp": chrono::Utc::now()
    })))
}

// Protected endpoint that requires authentication
async fn protected_handler(request: Request) -> Result<Json<Value>, StatusCode> {
    // Get user context from middleware
    let user_context = auth::extract_user_context(&request).ok_or(StatusCode::UNAUTHORIZED)?;

    Ok(Json(json!({
        "message": "Welcome to protected area",
        "user_id": user_context.user_id,
        "username": user_context.username,
        "role": user_context.role
    })))
}

// Admin-only endpoint
async fn admin_handler(request: Request) -> Result<Json<Value>, StatusCode> {
    // Require admin role
    let user_context = auth::require_admin_context(&request)?;

    Ok(Json(json!({
        "message": "Welcome to admin area",
        "user_id": user_context.user_id,
        "username": user_context.username,
        "role": user_context.role
    })))
}

/// Graceful shutdown signal handling
async fn shutdown_signal() {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {
            tracing::info!("Received Ctrl+C, shutting down gracefully...");
        },
        _ = terminate => {
            tracing::info!("Received SIGTERM, shutting down gracefully...");
        },
    }
}
