[package]
name = "backend"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true

[dependencies]
# Workspace dependencies
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true
thiserror.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true
uuid.workspace = true
chrono.workspace = true

# Backend specific
axum.workspace = true
tower.workspace = true
tower-http.workspace = true
sea-orm.workspace = true
sea-orm-migration.workspace = true
jsonwebtoken.workspace = true
bcrypt.workspace = true
reqwest.workspace = true
dotenv.workspace = true
async-trait.workspace = true
rust_decimal.workspace = true
sha2.workspace = true
rand.workspace = true
hex.workspace = true
validator.workspace = true
regex = "1.10"
md5 = "0.7"

# Local dependencies
shared = { path = "../shared" }