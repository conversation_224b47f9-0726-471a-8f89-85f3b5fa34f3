# API 集成测试

## 测试目标

验证新的 `/api/v1/app/outbound/all_outbounds` API 端点集成是否正常工作

## 测试步骤

### 1. 后端测试

确保后端返回正确的数据结构：
- API端点：`GET /api/v1/app/outbound/all_outbounds`
- 返回格式：
```json
{
  "outbounds": [
    {
      "tag": "Proxy",
      "protocol": "select",
      "sub_outbounds_tag": ["node1", "node2", "node3"]
    },
    {
      "tag": "Direct",
      "protocol": "direct",
      "sub_outbounds_tag": null
    }
  ]
}
```

### 2. Rust 客户端测试

- 启动 Tauri 应用
- 连接到代理
- 调用 `get_proxy_nodes` 命令
- 验证数据结构包含：
  - `selectable` 字段正确设置
  - `protocol` 字段正确显示
  - `has_sub_outbounds` 字段正确设置
  - 只有 `select` 协议的 outbounds 可以选择

### 3. 前端 UI 测试

- 打开 Dashboard 页面
- 验证 outbounds 显示：
  - Select 协议的 outbounds 显示为绿色标签
  - 非 Select 协议的 outbounds 显示为灰色标签
  - 有 sub_outbounds 的显示可展开箭头
  - 没有 sub_outbounds 的显示相应消息
  - 非 Select 协议的节点不可点击选择

## 期望结果

1. **API 数据正确传输**：新的 API 端点数据能够正确传输到前端
2. **选择逻辑正确**：只有 select 协议的 outbounds 可以选择
3. **UI 显示正确**：
   - 协议标签正确显示
   - 分组逻辑正确（有 sub_outbounds 时显示为组）
   - 选择状态正确反映
4. **向后兼容**：现有功能不受影响

## 测试状态

- [ ] 后端 API 测试
- [ ] Rust 客户端测试
- [ ] 前端 UI 测试
- [ ] 集成测试
- [ ] 回归测试

## 备注

- 确保在有真实 leaf 代理运行时进行测试
- 测试不同协议类型的 outbounds
- 验证错误处理（如 API 不可用时的 fallback）