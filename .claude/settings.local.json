{"permissions": {"allow": ["Bash(cargo:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(task-master set-status:*)", "Bash(task-master update-subtask:*)", "Bash(rm:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python3:*)", "Bash(npx @tauri-apps/cli@latest dev:*)", "<PERSON><PERSON>(task-master list:*)", "<PERSON><PERSON>(task-master:*)", "Bash(grep:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(jobs)", "<PERSON><PERSON>(timeout:*)", "Bash(find:*)", "Bash(RUST_BACKTRACE=1 cargo check)", "Bash(npm install:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(true)", "Bash(npx tsc:*)"], "deny": []}, "enabledMcpjsonServers": ["Context7", "playwright"]}