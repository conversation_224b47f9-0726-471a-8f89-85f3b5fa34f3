私有VPN客户端及管理后台产品需求文档 (PRD)
版本: 1.0
日期: 2025年7月8日

1. 项目概述
1.1. 项目目标
本项目旨在为用户提供一个安全、稳定、易用的私有VPN服务。通过开发一套完整的跨平台客户端、Web管理后台和用户中心，实现对用户、订阅套餐、代理服务的全面管理，提升用户体验和运营效率。

1.2. 项目范围
项目包含四个核心部分：

用户客户端 (桌面端): 基于 Rust + Tauri 技术栈，支持 Windows 和 macOS，集成 leaf 代理引擎，提供核心代理功能和用户交互界面。

Web 服务后端: 基于 Rust + Axum 技术栈，负责处理业务逻辑、数据存储、API交互，并为客户端和Web前端提供支持。

管理端 Web UI: 面向管理员，用于管理用户、套餐、订单和系统配置。

用户端 Web UI: 面向终端用户，用于注册、登录、查看和购买套餐。

1.3. 用户画像
普通用户: 需要简单、可靠的代理服务来访问网络资源，关心连接速度、稳定性和隐私安全。

管理员: 负责整个VPN服务的运营和维护，需要高效的工具来管理用户、配置服务和监控系统状态。

2. 系统架构
客户端: 通过API与Web后端通信，获取登录凭证、订阅信息和节点配置。在本地调用和管理leaf代理引擎实例。

Web后端: 核心业务处理中心。连接数据库，对外提供RESTful API，并与一个独立的用户管理API服务器进行通信，以实现核心的用户连接控制。

Web前端 (管理端/用户端): 单页应用 (SPA)，通过API与Web后端交互，渲染页面和处理用户操作。

3. 功能需求详述
第一部分：用户客户端 (Tauri App)
3.1. 核心代理功能
一键连接/断开:

需求: 界面最醒目位置提供一个总开关按钮，清晰地显示当前是“已连接”还是“已断开”状态。

逻辑: 点击按钮调用leaf引擎的启动或关闭接口。

代理模式选择:

需求: 提供一个下拉菜单或选项组，用于切换代理模式。

选项:

直连模式: 等同于关闭代理，所有网络流量不经过代理引擎。

全局模式: 所有网络流量都通过代理引擎处理。

规则模式 (可选/未来扩展): 根据预设规则分流流量。

节点选择:

需求: 提供一个清晰的节点选择界面，允许用户查看和切换代理节点。

实现:

通过API从leaf引擎获取所有可用的节点组 (如 "香港专线", "美国CN2")。

点击节点组，展开显示该组下的所有具体节点 (如 "香港节点1", "香港节点2")。

显示每个节点的延迟信息 (通过客户端测速实现)。

允许用户选择某个具体节点，或选择“自动选择”(由引擎决定最优节点)。

客户端需能调用leaf引擎API来更新当前使用的节点。

3.2. 用户与订阅
用户登录/登出:

需求: 提供登录界面，输入账号密码进行登录。登录成功后，本地持久化缓存用户凭证 (如Token)，实现自动登录。

逻辑: 启动时，检查本地缓存。若凭证有效，则自动登录；若无效或不存在，则显示登录界面。

登出: 提供登出按钮，清除本地缓存并返回登录界面。

订阅信息显示:

需求: 在主界面显著位置显示用户的订阅状态。

内容:

套餐名称 (如 "高级会员")。

到期时间 (格式: "YYYY-MM-DD HH:mm" 或 "剩余 X 天")。

剩余流量 (格式: "剩余 15.6 / 100 GB")。

订阅限制与续费:

需求: 当订阅到期或流量用尽时，自动停止代理服务。

逻辑:

客户端定期从后端同步订阅状态。

当状态变为无效时，立即调用leaf引擎的关闭接口。

此时若用户尝试点击“连接”按钮，弹窗提示“您的套餐已过期/流量已用尽，请续费后使用”。

提供“立即续费”按钮，点击后打开一个预设的URL。

URL格式: https://your-domain.com/user/subscribe?user_id=[用户ID]&token=[临时令牌]，方便网页端直接识别用户信息。

3.3. UI 与信息展示
实时速率图:

需求: 提供一个可折叠/隐藏的折线图，实时显示当前的上行/下行速率。

数据源: 定期 (如每秒) 从leaf引擎的API获取流量统计数据来绘制图表。

实时连接信息:

需求: 提供一个可折叠/隐藏的列表，展示当前的活动网络连接。

数据源: 调用leaf引擎API获取Stat结构列表。

显示列: 网络协议 (如 TCP/UDP), 源地址, 目标地址, 使用的出口节点, 已发送/接收流量。

本次流量统计:

需求: 在界面某处显示“本次已用流量：XXX MB”，统计从软件启动到当前的总流量消耗。

逻辑: 客户端自行累加从leaf引擎获取的流量数据。

日志面板:

需求: 提供一个默认隐藏的日志查看面板。

逻辑: 实时捕获并显示leaf引擎输出的日志流，方便排查问题。

公告通知:

需求: 客户端定期从后端检查是否有新的套餐公告。

逻辑: 如果有未读公告，通过小红点或弹窗等方式提醒用户。用户可以点击查看公告详情。

3.4. 配置与更新
节点配置管理:

需求: 登录成功后，客户端从后端获取完整的代理配置文件内容。

安全: 配置文件必须仅保存在内存中，严禁写入本地磁盘文件，以防泄露。

更新: 客户端将配置内容直接传递给leaf引擎进行加载或重载。

第二部分：Web 服务后端 (Axum)
3.1. 数据库设计
ORM选型: 使用 SeaORM 或 Diesel (除sqlx外)。

核心表结构:

users: id, username, password_hash, created_at

subscriptions: id, user_id, plan_id, start_date, end_date, total_traffic_gb, used_traffic_gb, status (active, expired, suspended)

plans: id, name, price, duration_days (0 for permanent), description, config_template, is_active

announcements: id, plan_id (nullable, null for global), content, created_at

orders: id, user_id, plan_id, order_number, amount, status (pending_payment, completed, cancelled), created_at

3.2. API Endpoints
/api/auth/

POST /login: 用户登录，成功返回JWT Token。

POST /register: 用户注册。

/api/user/

GET /profile: 获取当前用户信息和订阅详情。

GET /config: 获取当前用户的leaf配置文件。后端根据用户的订阅动态生成（替换端口、密码等占位符）。

GET /announcements: 获取该用户可见的公告。

/api/plans/

GET /: 获取所有在售套餐列表。

/api/orders/

POST /create: 创建一个新订单。

GET /:order_number: 查看订单状态。

/api/admin/ (需管理员权限)

用户管理: GET /users, POST /users, PUT /users/:id, DELETE /users/:id

套餐管理: GET /plans, POST /plans, PUT /plans/:id

订单管理: GET /orders, PUT /orders/:id/complete

系统设置: GET /settings, PUT /settings

3.3. 核心业务逻辑
流量自动重置:

需求: 对于周期套餐，需要在每个计费周期开始时自动重置used_traffic_gb字段。

实现: 创建一个定时任务 (e.g., daily cron job)，检查所有订阅，如果当前日期超过了end_date，则根据续费情况更新或禁用；如果订阅是按月且当天是新的计费周期的第一天，则重置流量。

过期用户处理:

需求: 当用户的订阅过期或流量用尽时，后端需要有机制确保用户无法连接。

实现:

客户端API (/api/user/config) 在返回配置前，先校验用户订阅状态。如果无效，则返回错误，客户端无法获取配置。

（关键） 调用用户管理API服务器的接口，通知其暂停或删除该用户的代理服务权限。这提供了一层服务器端的硬性保障，防止用户绕过客户端限制。

与用户管理API服务器的集成:

需求: 后端需要能调用一个外部API来管理底层的代理用户。

接口封装:

addUser(userId): 在代理服务器上创建一个用户，返回密码和端口。

removeUser(userId): 从代理服务器上删除一个用户。

suspendUser(userId): 暂停一个用户。

resumeUser(userId): 恢复一个用户。

第三部分：管理端 Web UI
3.1. 用户管理
功能:

显示所有用户列表（分页、搜索）。

创建新用户，并为其关联一个订阅。

修改用户信息。

手动延长/修改用户的订阅到期时间、重置流量。

查看用户的详细信息，包括订阅历史、流量使用情况和由用户管理API生成的连接密码。

删除用户。

3.2. 套餐管理
功能:

显示所有套餐列表。

创建新套餐，定义价格、服务周期（如30天、90天、永久）、流量限制、描述。

配置文件模板: 为每个套餐提供一个文本域，用于编辑其leaf配置文件的基础模板。模板中的用户特定信息（如密码、端口）使用占位符，如{{password}}。

为指定套餐发布/编辑公告。

3.3. 订单管理
功能:

显示所有订单列表（分页、按状态筛选）。

查看订单详情。

手动将“待支付”的订单标记为“已完成”（在收到线下付款后）。标记完成后，系统将为用户激活对应的订阅。

3.4. 全局站点配置
功能:

自动清理: 设置一个天数（如30天），系统将自动清理注册超过该天数但从未购买过任何套餐的用户数据。

续费链接模板: 配置客户端中“立即续费”按钮跳转的URL模板。

其他预留参数字段，方便未来扩展。

第四部分：用户端 Web UI
4.1. 基础功能
用户注册: 提供简单的注册页面，仅需用户名和密码，无需邮箱验证。

用户登录: 用户可使用注册的账号登录。即使没有购买套餐，也应能成功登录并访问用户中心。

4.2. 用户中心
仪表盘:

显示当前订阅状态（同客户端）。

如果没有订阅，则提示“您当前没有有效套餐”。

套餐列表:

展示所有在售套餐的卡片，包含名称、价格、周期、流量、描述。

提供“购买”按钮。

购买流程:

点击“购买”后，系统生成一个“待支付”的订单。

跳转到一个支付引导页面，清晰地显示：

订单号

需支付金额

支付说明: "请联系客服并提供您的订单号完成支付" 以及客服的联系方式。

我的订单:

显示用户的历史订单列表及其状态。

4. 非功能性需求
性能: 客户端启动迅速，UI响应流畅。后端API平均响应时间应低于200ms。

安全:

前后端通信全程使用HTTPS。

用户密码必须哈希存储。

客户端绝不将配置文件明文存储于本地。

API需进行严格的权限校验。

可用性: UI/UX设计简洁明了，核心功能易于发现和使用。

可维护性: 代码结构清晰，注释充分，便于后续迭代和维护。
