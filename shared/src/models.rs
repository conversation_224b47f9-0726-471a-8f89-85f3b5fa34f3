use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Subscription {
    pub id: Uuid,
    pub user_id: Uuid,
    pub plan_id: Uuid,
    pub start_date: DateTime<Utc>,
    pub end_date: DateTime<Utc>,
    pub total_traffic_gb: f64,
    pub used_traffic_gb: f64,
    pub status: SubscriptionStatus,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum SubscriptionStatus {
    Active,
    Expired,
    Suspended,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Plan {
    pub id: Uuid,
    pub name: String,
    pub price: f64,
    pub duration_days: u32,
    pub traffic_gb: f64,
    pub description: String,
    pub config_template: String,
    pub is_active: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Order {
    pub id: Uuid,
    pub user_id: Uuid,
    pub plan_id: Uuid,
    pub order_number: String,
    pub amount: f64,
    pub status: OrderStatus,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OrderStatus {
    PendingPayment,
    Completed,
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Announcement {
    pub id: Uuid,
    pub plan_id: Option<Uuid>,
    pub content: String,
    pub created_at: DateTime<Utc>,
}