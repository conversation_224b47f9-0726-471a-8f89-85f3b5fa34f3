# Database Configuration
DATABASE_URL=postgresql://vpn_user:vpn_password@localhost:5432/vpn_database
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5
DB_CONNECT_TIMEOUT=10
DB_IDLE_TIMEOUT=300

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-please-change-in-production
JWT_EXPIRY_HOURS=24

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=3000
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# User Management API Configuration
USER_MANAGEMENT_API_URL=http://localhost:4000
USER_MANAGEMENT_API_KEY=your-api-key-here

# Logging Configuration
RUST_LOG=info
LOG_LEVEL=info

# Environment
ENVIRONMENT=development

# Optional: Email Configuration (for future use)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# Optional: Redis Configuration (for future caching)
REDIS_URL=redis://localhost:6379

# Optional: File Storage Configuration
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
EOF < /dev/null